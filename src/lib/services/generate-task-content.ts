import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';
import {
  ContentGenerationParams,
  GeneratedContent
} from '../types/content-generation.js';
import {
  createPromptVariables,
  normalizeCompiledPrompt,
  validateCompiledPrompt,
  validateContentGenerationParams,
  ContentGenerationError
} from '../utils/content-generation.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Legacy interface for backward compatibility
interface TaskContentParams extends ContentGenerationParams {}
interface GeneratedTaskContent extends GeneratedContent {}

/**
 * Compiles a Langfuse prompt with the given variables
 */
async function compilePrompt(params: ContentGenerationParams): Promise<string> {
  const prompt = await langfuse.getPrompt("generate_content_body_v2", undefined, { label: "production" });
  const promptVariables = createPromptVariables(params);
  const compiled = prompt.compile(promptVariables);
  const compiledText = normalizeCompiledPrompt(compiled);
  validateCompiledPrompt(compiledText);
  return compiledText;
}

/**
 * Generates content for a single task using Langfuse prompt and LLM
 */
export async function generateTaskContent(params: TaskContentParams): Promise<GeneratedTaskContent> {
  try {
    // Validate input parameters
    validateContentGenerationParams(params);

    // Compile the prompt with parameters
    const promptObject = await compilePrompt(params);

    // Call LLM with compiled prompt
    const response = await callLLM(
      promptObject,
      {
        temperature: 0.7,
      }, // Provide temperature config
      "google/gemini-2.5-pro-preview",
      { parse: false }
    );

    // Return standardized response format
    return {
      content: response,
      visual_description: null,
      seo_keywords: [],
      trend_keywords: []
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (error instanceof ContentGenerationError) {
      console.warn(`   ❌ Content generation validation failed: ${errorMessage}`);
      throw error;
    }

    if (errorMessage.includes('generate_content_body_v2')) {
      console.warn(`   ❌ Langfuse prompt failed: ${errorMessage}`);
      throw new ContentGenerationError(`Prompt compilation failed: ${errorMessage}`);
    }

    console.error('Error generating task content:', error);
    throw new ContentGenerationError(
      'Failed to generate task content. Please check your API key and try again.',
      undefined,
      undefined,
      error instanceof Error ? error : new Error(errorMessage)
    );
  }
}
