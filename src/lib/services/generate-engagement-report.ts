import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});


export async function generateEngagementReport(engaged_user_profiles: string[], icp_data: string, product_documents: string): Promise<any> {
  try {

    // Get production prompt 
    const prompt = await langfuse.getPrompt("generate_engagement_report", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
        linkedIn_Profiles: JSON.stringify(engaged_user_profiles),
      icp_info: icp_data,
      product_info: product_documents
    });

    console.log(compiledPrompt);
    const response = await callLLM(compiledPrompt, prompt, "google/gemini-2.5-pro-preview");
    return response;
    
  } catch (error) {
    console.error('Error generating content schedule:', error);
    throw new Error('Failed to generate content schedule.');
  }
}
