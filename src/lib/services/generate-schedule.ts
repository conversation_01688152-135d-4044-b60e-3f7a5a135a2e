import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

interface ScheduleParams {
  productDocumentation: string;
  campaignGoal: string;
  startDate: any; // YYYY-MM-DD format
  endDate: any; // YYYY-MM-DD format
  externalResearch?: string;
  channels?: string[];
  targetICPs?: string;
  targetPersonas?: string;
  companyBrand?: object;
}

interface ContentItem {
  id: string;
  title: string;
  content_type: string;
  channel: string;
  scheduled_publishing_time: string | {
    date: string;
    time: string;
  };
  marketing_content_formula?: string;
  content_language: string;
  hasVisualContent: boolean;
  seo_keywords?: string[];
  trend_keywords?: string[];
  personas?: string[];
  content_draft: {
    subject?: string;
    description: string;
    visual_description?: string;
  };
}

export async function generateContentSchedule(params: ScheduleParams): Promise<ContentItem[]> {
  try {
    const { productDocumentation, campaignGoal, externalResearch, startDate, endDate, targetICPs, targetPersonas, companyBrand } = params;

    console.log("params", params);
   
    // Get production prompt 
    const prompt = await langfuse.getPrompt("generate_content_schedule", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
      campaign_goal: campaignGoal,
      product_information: productDocumentation,
      campaign_start_date: startDate,
      campaign_end_date: endDate,
      external_research: externalResearch || "",
      target_icps: targetICPs || "",
      target_personas: targetPersonas || "",
      brand_book: JSON.stringify(companyBrand) || "",
    });
    console.log(compiledPrompt);
    const response = await callLLM(compiledPrompt, prompt, "google/gemini-2.5-pro-preview");
    return response;
    
  } catch (error) {
    console.error('Error generating content schedule:', error);
    throw new Error('Failed to generate content schedule. Please check your API key and try again.');
  }
}
