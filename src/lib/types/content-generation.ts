/**
 * Shared types and interfaces for content generation (backend)
 */

// Core content generation parameters
export interface ContentGenerationParams {
  taskTitle: string;
  taskDescription: string;
  contentType: string;
  channel: string;
  campaignGoal: string;
  productInformation?: string;
  targetICPs?: string;
  targetPersonas?: string;
  companyBrand?: object;
  externalResearch?: string;
}

// Content generation response
export interface GeneratedContent {
  content: string;
  visual_description?: string | null;
  seo_keywords?: string[];
  trend_keywords?: string[];
}

// Prompt compilation variables
export interface PromptVariables extends Record<string, string> {
  channel: string;
  content_type: string;
  task_title: string;
  task_description: string;
  personas_block: string;
  icps_block: string;
  research_block: string;
  documents_block: string;
  seo_keywords: string;
  trend_keywords: string;
  brand_guidelines: string;
}

// Content generation status
export interface ContentGenerationStatus {
  total: number;
  completed: number;
  generating: number;
  failed: number;
  pending: number;
}

// Task processing result
export interface TaskProcessingResult {
  success: boolean;
  taskId: string;
  title: string;
  error?: string;
}

// Database task model
export interface ContentTask {
  id: string;
  task_title: string;
  task_description: string;
  content_type: string;
  channel: string;
  content?: string;
  is_generating: boolean;
  error_generating: boolean;
  campaign_id: string;
  company_id: string;
}

// Campaign data for content generation
export interface CampaignData {
  objective: string;
  products?: string;
  external_research?: string;
  target_icps?: string[];
  target_personas?: string[];
}

// Company brand data
export interface CompanyBrandData {
  name?: string;
  voice?: string;
  tone?: string;
  guidelines?: string;
  [key: string]: any;
}
