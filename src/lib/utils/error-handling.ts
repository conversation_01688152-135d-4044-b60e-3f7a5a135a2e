/**
 * Standardized error handling utilities
 */

// Error types enum
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  DATABASE = 'DATABASE',
  API = 'API',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT = 'RATE_LIMIT',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  CONTENT_GENERATION = 'CONTENT_GENERATION',
  UNKNOWN = 'UNKNOWN'
}

// Base error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: Record<string, any>;
  public readonly timestamp: Date;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    statusCode: number = 500,
    isOperational: boolean = true,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = this.constructor.name;
    this.type = type;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;
    this.timestamp = new Date();

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

// Specific error classes
export class ValidationError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorType.VALIDATION, 400, true, context);
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorType.DATABASE, 500, true, context);
  }
}

export class APIError extends AppError {
  constructor(message: string, statusCode: number = 500, context?: Record<string, any>) {
    super(message, ErrorType.API, statusCode, true, context);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string, identifier?: string) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    super(message, ErrorType.NOT_FOUND, 404, true, { resource, identifier });
  }
}

export class ContentGenerationError extends AppError {
  constructor(
    message: string, 
    context?: Record<string, any>
  ) {
    super(message, ErrorType.CONTENT_GENERATION, 500, true, context);
  }
}

export class ExternalServiceError extends AppError {
  constructor(
    service: string,
    message: string,
    statusCode: number = 500,
    context?: Record<string, any>
  ) {
    super(
      `External service error (${service}): ${message}`,
      ErrorType.EXTERNAL_SERVICE,
      statusCode,
      true,
      { service, ...context }
    );
  }
}

// Error handling utilities
export function isAppError(error: any): error is AppError {
  return error instanceof AppError;
}

export function isOperationalError(error: any): boolean {
  if (isAppError(error)) {
    return error.isOperational;
  }
  return false;
}

// Error logging utility
export function logError(error: Error, context?: Record<string, any>): void {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    context
  };

  if (isAppError(error)) {
    errorInfo.context = {
      ...errorInfo.context,
      type: error.type,
      statusCode: error.statusCode,
      isOperational: error.isOperational,
      errorContext: error.context
    };
  }

  console.error('Error occurred:', errorInfo);
}

// Safe async wrapper
export async function safeAsync<T>(
  operation: () => Promise<T>,
  errorMessage?: string,
  context?: Record<string, any>
): Promise<{ data: T | null; error: AppError | null }> {
  try {
    const data = await operation();
    return { data, error: null };
  } catch (error) {
    const appError = isAppError(error) 
      ? error 
      : new AppError(
          errorMessage || 'Operation failed',
          ErrorType.UNKNOWN,
          500,
          true,
          { originalError: error instanceof Error ? error.message : String(error), ...context }
        );
    
    logError(appError, context);
    return { data: null, error: appError };
  }
}

// Retry utility with exponential backoff
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  maxDelay: number = 10000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw new AppError(
    `Operation failed after ${maxRetries + 1} attempts: ${lastError.message}`,
    ErrorType.EXTERNAL_SERVICE,
    500,
    true,
    { maxRetries, lastError: lastError.message }
  );
}

// Error response formatter for APIs
export function formatErrorResponse(error: Error): {
  success: false;
  error: {
    message: string;
    type: string;
    statusCode: number;
    timestamp: string;
    context?: Record<string, any>;
  };
} {
  if (isAppError(error)) {
    return {
      success: false,
      error: {
        message: error.message,
        type: error.type,
        statusCode: error.statusCode,
        timestamp: error.timestamp.toISOString(),
        context: error.context
      }
    };
  }

  return {
    success: false,
    error: {
      message: error.message || 'An unexpected error occurred',
      type: ErrorType.UNKNOWN,
      statusCode: 500,
      timestamp: new Date().toISOString()
    }
  };
}

// Success response formatter
export function formatSuccessResponse<T>(data: T): {
  success: true;
  data: T;
  timestamp: string;
} {
  return {
    success: true,
    data,
    timestamp: new Date().toISOString()
  };
}
