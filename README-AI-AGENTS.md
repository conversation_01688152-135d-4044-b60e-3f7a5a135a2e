# AI Agent Development Guide

> **Comprehensive guide for AI agents working with this content marketing SaaS monorepo**

## 🏗️ Architecture Overview

This is a **real-time, multi-tenant SaaS platform** for AI-powered content marketing. The system uses **Zero sync** for real-time collaboration and **Supabase** for persistent storage, auth, and RLS.

### Core Applications
- **`apps/web/`** - Next.js 15 frontend (main SaaS platform)
- **`apps/sb-server/`** - Express.js backend (AI services & data processing) 
- **`apps/zero-server/`** - Zero sync real-time server
- **`apps/e2e/`** - Playwright testing suite

### Key Packages
- **`packages/zero-schema/`** - Shared database schema & mutations
- **`packages/ui/`** - Reusable React components (shadcn/ui)
- **`packages/supabase/`** - Database client & auth utilities

---

## 🗄️ Database Changes Workflow

### 1. **Schema Updates**

When modifying database structure, you must update **3 places**:

#### A. Supabase Migration
```bash
# Create new migration
cd apps/web
pnpm run supabase:migration:new "add_new_field_to_campaigns"
```

Edit the generated SQL file in `apps/web/supabase/migrations/`:
```sql
-- Add new column
ALTER TABLE company_campaigns 
ADD COLUMN new_field TEXT;

-- Update RLS policy if needed
CREATE POLICY "Users can view campaigns" ON company_campaigns
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM accounts_memberships 
    WHERE account_id = company_campaigns.company_id 
    AND user_id = auth.uid()
  )
);
```

#### B. Zero Schema Definition
Update `packages/zero-schema/src/schema.ts`:
```typescript
export const company_campaigns = table('company_campaigns').columns({
  // existing fields...
  new_field: string().optional(), // Add new field
}).primaryKey('id');

// Update permissions if needed
company_campaigns: {
  row: {
    select: [allowIfCompanyCampaignIsCompany],
    // add insert/update permissions as needed
  },
},
```

#### C. TypeScript Types (Auto-generated)
```bash
# Regenerate types after migration
cd apps/web
pnpm run supabase:typegen
```

### 2. **Deploy Database Changes**
```bash
# Push to preview environment
git add . && git commit -m "feat: add new_field to campaigns"
git push origin feature-branch

# GitHub Actions will automatically:
# 1. Run Supabase migrations via deploy-supabase.yml
# 2. Deploy updated zero-schema via deploy-zero-server.yml
```

---

## 🚀 Backend Development (AI Services)

### Where to Add New AI Services

All LLM/AI services go in **`apps/sb-server/src/lib/services/`**:

```
apps/sb-server/src/lib/services/
├── generate-content.ts      # Content generation
├── generate-persona.ts      # ICP/persona creation
├── generate-schedule.ts     # Campaign planning  
├── scrape-website.ts        # Web scraping
├── your-new-service.ts      # 👈 Add new services here
```

### Service Pattern
```typescript
// apps/sb-server/src/lib/services/your-new-service.ts
import { openai } from '../clients/openai.js';

interface ServiceInput {
  companyId: string;
  prompt: string;
  // other inputs
}

interface ServiceOutput {
  result: string;
  metadata?: any;
}

export async function yourNewService(input: ServiceInput): Promise<ServiceOutput> {
  try {
    // AI processing logic
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: input.prompt }],
    });

    return {
      result: response.choices[0]?.message?.content || '',
      metadata: { tokens: response.usage?.total_tokens },
    };
  } catch (error) {
    console.error('Service error:', error);
    throw new Error('Failed to process request');
  }
}
```

### Adding API Endpoints
Add to **`apps/sb-server/src/index.ts`**:
```typescript
import { yourNewService } from './lib/services/your-new-service.js';

app.post('/your-endpoint', async (req: Request, res: Response) => {
  try {
    const { companyId, prompt } = req.body;
    
    if (!companyId || !prompt) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }

    const result = await yourNewService({ companyId, prompt });
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Service temporarily unavailable',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
```

### Server Mutators (Database Operations)

For operations that trigger background work, use server mutators in **`apps/sb-server/src/lib/server-mutators.ts`**:

```typescript
export function createServerMutators(authData: AuthData, asyncTasks: Array<() => Promise<void>>) {
  return {
    // existing mutators...
    your_table: {
      insert: async (tx: any, { id, values }: { id: string; values: any }) => {
        // Insert record immediately
        await tx.mutate.your_table.insert({
          id,
          ...values,
          is_generating: true, // Show loading state
        });

        // Queue background work
        asyncTasks.push(async () => {
          try {
            const result = await yourNewService(values);
            
            // Update with results
            await sql`
              UPDATE your_table 
              SET 
                result = ${sql.json(result)},
                is_generating = false,
                updated_at = ${Date.now()}
              WHERE id = ${id}
            `;
          } catch (error) {
            // Mark as failed
            await sql`
              UPDATE your_table 
              SET 
                is_generating = false,
                error_generating = true
              WHERE id = ${id}
            `;
          }
        });
      },
    },
  };
}
```

---

## 🎨 Frontend Development (UI Components)

### Component Structure
```
apps/web/app/home/<USER>/
├── campaigns/
│   ├── _components/          # Feature-specific components
│   │   ├── campaign-form.tsx
│   │   ├── campaign-list.tsx
│   │   └── index.ts         # Export barrel
│   ├── _lib/                # Feature utilities
│   │   ├── actions.ts       # Server actions
│   │   ├── queries.ts       # Data queries
│   │   └── validations.ts   # Zod schemas
│   └── page.tsx             # Route page
```

### Shared UI Components
Add reusable components to **`packages/ui/src/`**:
```typescript
// packages/ui/src/your-component.tsx
import * as React from 'react';
import { cn } from './lib/utils';

interface YourComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export const YourComponent = React.forwardRef<
  HTMLDivElement,
  YourComponentProps
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("base-styles", className)}
      {...props}
    >
      {children}
    </div>
  );
});
YourComponent.displayName = "YourComponent";
```

Export in **`packages/ui/src/index.ts`**:
```typescript
export { YourComponent } from './your-component';
```

### Real-Time Data Patterns
Use Zero sync for real-time updates:
```typescript
'use client';

import { useQuery, useMutation } from '@kit/zero/react';
import { useZero } from '@kit/zero/zero-provider';

export function CampaignList({ companyId }: { companyId: string }) {
  const z = useZero();
  
  // Real-time query
  const [campaigns] = useQuery(
    z.query.company_campaigns
      .where('company_id', companyId)
      .orderBy('created_at', 'desc')
  );

  // Optimistic mutation
  const createCampaign = useMutation(z.mutate.company_campaigns.insert);

  const handleCreate = async (data: CampaignData) => {
    // Optimistic update (immediate UI response)
    await createCampaign({
      id: generateId(),
      company_id: companyId,
      ...data,
      is_generating: true,
    });
  };

  return (
    <div>
      {campaigns.map(campaign => (
        <CampaignCard 
          key={campaign.id} 
          campaign={campaign}
          isGenerating={campaign.is_generating}
        />
      ))}
    </div>
  );
}
```

### Server Actions (Data Mutations)
```typescript
// apps/web/app/home/<USER>/campaigns/_lib/actions.ts
'use server';

import { requireUserInServerComponent } from '@kit/supabase/require-user-in-server-component';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function createCampaign(data: CreateCampaignData) {
  const user = await requireUserInServerComponent();
  const client = getSupabaseServerClient();

  // Validate permissions
  const { data: membership } = await client
    .from('accounts_memberships')
    .select('account_role')
    .eq('user_id', user.id)
    .eq('account_id', data.companyId)
    .single();

  if (!membership) {
    throw new Error('Unauthorized');
  }

  // Perform server-side operation
  const { data: campaign, error } = await client
    .from('company_campaigns')
    .insert(data)
    .select()
    .single();

  if (error) throw error;
  return campaign;
}
```

---

## 📋 Development Checklist

### ✅ Database Changes
- [ ] Create Supabase migration
- [ ] Update Zero schema definition
- [ ] Update permissions in Zero schema
- [ ] Regenerate TypeScript types
- [ ] Test migration locally
- [ ] Deploy via GitHub Actions

### ✅ Backend Development
- [ ] Add service to `apps/sb-server/src/lib/services/`
- [ ] Add API endpoint to `apps/sb-server/src/index.ts`
- [ ] Add server mutator if needed
- [ ] Add async task handler if needed
- [ ] Test API endpoints locally
- [ ] Deploy via GitHub Actions

### ✅ Frontend Development
- [ ] Create feature components in `_components/`
- [ ] Add server actions in `_lib/actions.ts`
- [ ] Add data queries in `_lib/queries.ts`
- [ ] Add form validations in `_lib/validations.ts`
- [ ] Use Zero sync for real-time updates
- [ ] Add loading and error states
- [ ] Test user flows

---

## 🚨 Important Patterns & Conventions

### 1. **Always Use Optimistic Updates**
```typescript
// ✅ Good - Immediate UI feedback
const createItem = useMutation(z.mutate.items.insert);
await createItem({ id: generateId(), ...data });

// ❌ Bad - Slow server round-trip
await fetch('/api/items', { method: 'POST', body: JSON.stringify(data) });
```

### 2. **Multi-Tenant Data Isolation**
```typescript
// ✅ Always scope by company_id
const campaigns = z.query.company_campaigns
  .where('company_id', companyId);

// ❌ Never query without tenant scope
const campaigns = z.query.company_campaigns.related.all;
```

### 3. **Async Work Pattern**
```typescript
// ✅ Good - Immediate insert + background processing
await tx.mutate.items.insert({ ...data, is_generating: true });
asyncTasks.push(() => processItem(data));

// ❌ Bad - Slow synchronous processing
const result = await longRunningAIProcess(data);
await tx.mutate.items.insert({ ...data, result });
```

### 4. **Error Handling**
```typescript
// ✅ Always handle loading and error states
{isGenerating && <LoadingSpinner />}
{error && <ErrorMessage message={error.message} />}
{data && <Content data={data} />}
```

### 5. **Permission Checks**
```typescript
// ✅ Always verify user permissions
const user = await requireUserInServerComponent();
const membership = await verifyMembership(user.id, companyId);
if (!membership) throw new Error('Unauthorized');
```

---

## 🔧 Development Commands

### Database
```bash
# Local development
pnpm run supabase:web:start        # Start local Supabase
pnpm run supabase:web:reset        # Reset local database
pnpm run supabase:typegen          # Generate TypeScript types

# Migrations
pnpm run supabase:migration:new "description"  # Create migration
pnpm run supabase:db:push          # Push to remote
```

### Development
```bash
# Start all services
pnpm run dev                       # Web app + Zero server
pnpm run dev:sb-server            # Backend API server
pnpm run dev:zero-server          # Real-time sync server

# Testing  
pnpm run test                      # Run all tests
pnpm run test:e2e                  # E2E tests only
pnpm run typecheck                 # Type checking
pnpm run lint                      # Linting
```

### Deployment
```bash
# Automatic via GitHub Actions
git push origin main               # Production deploy
git push origin staging            # Staging deploy  
git push origin feature-branch     # Preview deploy

# Manual deployment (emergency)
pnpm run deploy:preview            # Manual preview deploy
```

---

## 🧠 AI/LLM Best Practices

### 1. **Provider Flexibility**
```typescript
// ✅ Support multiple AI providers
const providers = {
  openai: new OpenAI({ apiKey: process.env.OPENAI_API_KEY }),
  anthropic: new Anthropic({ apiKey: process.env.CLAUDE_API_KEY }),
  google: new GoogleAI({ apiKey: process.env.GOOGLE_AI_KEY }),
};

const provider = providers[preferredProvider] || providers.openai;
```

### 2. **Prompt Engineering**
```typescript
// ✅ Use structured prompts with context
const systemPrompt = `
You are a content marketing expert. Generate social media content based on:
- Brand Voice: ${brandVoice}
- Target Audience: ${targetAudience}  
- Campaign Goal: ${campaignGoal}

Format: JSON with title, content, hashtags, call_to_action
`;
```

### 3. **Error Recovery**
```typescript
// ✅ Graceful fallbacks
try {
  return await primaryAIProvider.generate(prompt);
} catch (error) {
  console.warn('Primary AI failed, trying fallback:', error);
  return await fallbackAIProvider.generate(prompt);
}
```

### 4. **Content Validation**
```typescript
// ✅ Validate AI output
const contentSchema = z.object({
  title: z.string().min(1).max(100),
  content: z.string().min(1).max(2000),
  hashtags: z.array(z.string()).max(10),
});

const validatedContent = contentSchema.parse(aiResponse);
```

---

## 📚 Additional Resources

- **Zero Sync Docs**: Understanding real-time synchronization
- **Supabase Docs**: Database, auth, and RLS patterns
- **Next.js 15 Docs**: App Router and Server Components
- **shadcn/ui**: Component library patterns
- **Cursor Rules**: `.cursorrules` file for AI-assisted development

---

**Remember**: This is a real-time, collaborative platform. Every change should consider multi-user scenarios, data consistency, and user experience. Always test with multiple browser tabs to verify real-time behavior works correctly.