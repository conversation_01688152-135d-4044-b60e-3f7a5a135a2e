## Users and Accounts System


### Overview
The application implements a dual-account system:
1. Personal Accounts - Individual user workspaces
2. Team Accounts - Collaborative workspaces shared between multiple users

### Account Types

#### Personal Accounts
- Created automatically when a user signs up
- Maps 1:1 with Supabase Auth users (shares same UUID)
- Lives under the route `app/home/<USER>
- Accessed using `useUserWorkspace` hook in client components
- Personal data is only accessible by the owner

#### Team Accounts
- Collaborative workspaces that can have multiple members
- Lives under the route `app/home/<USER>
- Identified by a unique slug in the URL
- Accessed using `useTeamAccountWorkspace` hook in client components
- Team data is accessible by all team members based on their roles

### Database Structure

```sql
-- Core tables structure
create table public.accounts (
  id uuid primary key references auth.users(id),
  type text check (type in ('personal', 'team')),
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

create table public.team_members (
  id uuid primary key default gen_random_uuid(),
  team_id uuid references public.accounts(id),
  user_id uuid references public.accounts(id),
  role text check (role in ('owner', 'admin', 'member')),
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);
```

### RLS Policies Guide

#### Core Principles
1. Personal accounts can only access their own data
2. Team members can access team data based on their role
3. Always use `auth.uid()` for user identification
4. Implement policies for both read and write operations

#### Example RLS Implementation

```sql
-- Example for a notes table
create table public.notes (
  id uuid primary key default gen_random_uuid(),
  account_id uuid references public.accounts(id),
  title text,
  content text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Enable RLS
alter table public.notes enable row level security;

-- Personal account access
create policy "Users can access their own notes"
  on public.notes
  for all
  using (
    account_id in (
      select id 
      from public.accounts 
      where id = auth.uid() and type = 'personal'
    )
  );

-- Team account access
create policy "Team members can access team notes"
  on public.notes
  for all
  using (
    account_id in (
      select team_id 
      from public.team_members 
      where user_id = auth.uid()
    )
  );
```

### Creating New Database Functions with RLS

When creating new database functions that need to respect RLS:

1. **Security Context**
```sql
-- Always set security definer and RLS to false
create or replace function my_function(...)
returns setof my_table
security definer
set search_path = public
language plpgsql
as $$
begin
  -- Function logic
end;
$$;
```

2. **Access Verification**
```sql
-- Example function with access check
create or replace function get_account_notes(p_account_id uuid)
returns setof notes
security definer
set search_path = public
language plpgsql
as $$
begin
  -- Verify access
  if not exists (
    select 1 
    from accounts a
    left join team_members tm on tm.team_id = a.id
    where (
      -- Personal account check
      (a.id = auth.uid() and a.type = 'personal')
      or 
      -- Team account check
      (tm.user_id = auth.uid() and tm.team_id = p_account_id)
    )
  ) then
    raise exception 'Access denied';
  end if;

  -- Return data
  return query
    select * from notes where account_id = p_account_id;
end;
$$;
```

### Best Practices

1. **Access Control**
   - Always verify user authentication before data access
   - Use `auth.uid()` for user identification
   - Implement role-based access for team accounts
   - Never bypass RLS without explicit security considerations

2. **Data Isolation**
   - Personal account data should never leak to other users
   - Team account data should only be accessible to team members
   - Always include `account_id` in your tables for proper data segregation

3. **Function Security**
   - Use `security definer` for functions that need to bypass RLS
   - Always verify access before returning data
   - Set explicit search paths to prevent search path injection
   - Document security implications in function comments

4. **Error Handling**
   - Use explicit error messages for access violations
   - Log security-related errors appropriately
   - Don't expose sensitive information in error messages

### Testing RLS Policies

Always test your RLS policies with different user contexts:
1. Unauthenticated users
2. Personal account owners
3. Team members with different roles
4. Users not belonging to the team

```sql
-- Example test cases
begin;
  set local role authenticated;
  set local request.jwt.claim.sub to 'user_id';
  -- Run your queries here
rollback;
```

## Code Organization and Architecture

### Project Structure Overview
```
apps/
  web/
    app/
      home/           # Protected routes
        (user)/       # Personal workspace routes
        [account]/    # Team workspace routes
      (marketing)/    # Public marketing pages
      auth/          # Authentication pages
    components/      # Global components
    lib/            # Global utilities
    supabase/       # Supabase configuration and clients
```

### Data Access Patterns

#### Server Components (Preferred)
Always prefer Server Components for data fetching when possible. Use the Supabase server client directly:

```tsx
import { getSupabaseServerClient } from '@kit/supabase/server-client';

async function ServerComponent() {
  const client = getSupabaseServerClient();
  const { data, error } = await client.from('notes').select('*');
  
  if (error) {
    return <ErrorComponent error={error} />;
  }

  return <DataDisplay data={data} />;
}
```

#### Client Components (When Required)
Use Client Components only when necessary (e.g., for interactivity or hooks). Prefer passing data down from Server Components:

```tsx
// Server Component
async function NotesPage() {
  const client = getSupabaseServerClient();
  const { data } = await client.from('notes').select('*');
  
  return <NotesDisplay initialData={data} />;
}

// Client Component
'use client';
function NotesDisplay({ initialData }) {
  // Client-side state management, interactivity, etc.
  return <div>{/* render notes */}</div>;
}
```

### API Organization

#### Server Actions
Place server actions in a `server-actions.ts` file within the feature directory:

```
features/
  notes/
    _lib/
      server-actions.ts    # Server actions
      schemas.ts          # Zod schemas
    components/          # Feature components
    page.tsx            # Page component
```

Example server action:
```tsx
// _lib/server-actions.ts
'use server';

import { enhanceAction } from '@kit/next/actions';
import { CreateNoteSchema } from './schemas';

export const createNoteAction = enhanceAction(
  async function (data, user) {
    // Implementation
  },
  {
    auth: true,
    schema: CreateNoteSchema,
  }
);
```

#### API Routes
Place API routes in a `route.ts` file within the feature directory:

```
app/
  api/
    notes/
      route.ts    # API route handlers
```

Example route handler:
```tsx
// route.ts
import { enhanceRouteHandler } from '@kit/next/routes';

export const GET = enhanceRouteHandler(
  async function({ user }) {
    // Implementation
  },
  { auth: true }
);
```

### Feature Organization

Each feature should follow this structure:
```
features/
  feature-name/
    _components/     # Private components
      index.ts      # Export public components
    _lib/           # Private utilities
      schemas.ts    # Zod schemas
      queries.ts    # React Query hooks
      server-actions.ts  # Server actions
    api/            # API related code
    components/     # Public components
    page.tsx        # Page component
```

### Data Flow Best Practices

1. **Server-First Approach**
   - Start with Server Components
   - Use server-side data fetching
   - Pass data down to Client Components

2. **State Management**
   - Use React Query for client-side state
   - Define query hooks in feature's `_lib/queries.ts`
   - Implement optimistic updates when appropriate

```tsx
// _lib/queries.ts
export function useNotes() {
  return useQuery({
    queryKey: ['notes'],
    queryFn: async () => {
      const { data } = await supabase.from('notes').select('*');
      return data;
    }
  });
}
```

3. **Form Handling**
   - Define Zod schemas in `_lib/schemas.ts`
   - Use React Hook Form with Zod validation
   - Implement server actions for form submission

```tsx
// _lib/schemas.ts
export const NoteSchema = z.object({
  title: z.string().min(1),
  content: z.string().min(1)
});

// components/note-form.tsx
'use client';
export function NoteForm() {
  const form = useForm({
    resolver: zodResolver(NoteSchema)
  });
  
  return <Form {...form}>{/* form fields */}</Form>;
}
```

### Context Usage

1. **Personal Account Context**
```tsx
'use client';
import { useUserWorkspace } from '@kit/accounts/hooks/use-user-workspace';

export function PersonalComponent() {
  const { user } = useUserWorkspace();
  return <div>{/* personal workspace UI */}</div>;
}
```

2. **Team Account Context**
```tsx
'use client';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

export function TeamComponent() {
  const { account, user } = useTeamAccountWorkspace();
  return <div>{/* team workspace UI */}</div>;
}
```

### Error Handling and Logging

1. **Server-Side Errors**
```tsx
import { logger } from '@kit/shared/logger';

export async function serverAction(data: unknown) {
  const ctx = {
    action: 'createNote',
    userId: user.id
  };
  
  try {
    logger.info(ctx, 'Creating note');
    // Implementation
  } catch (error) {
    logger.error(ctx, 'Failed to create note', { error });
    throw error;
  }
}
```

2. **Client-Side Errors**
```tsx
'use client';
export function ErrorBoundary({ children }) {
  return (
    <ErrorBoundary
      fallback={<ErrorDisplay />}
      onError={(error) => {
        // Log to monitoring service
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
```

### Testing Strategy

1. **Unit Tests**
   - Place next to implementation: `component.test.tsx`
   - Test business logic and UI components separately
   - Mock Supabase calls and server actions

2. **Integration Tests**
   - Place in `__tests__` directory
   - Test feature workflows end-to-end
   - Use test database with RLS policies

3. **E2E Tests**
   - Place in `e2e` directory
   - Test critical user flows
   - Include authentication scenarios
