# Vercel Blob Setup Instructions

## Overview
This application now uses Vercel Blob for handling file uploads larger than 4.5MB for:
- **Brand document uploads** (PDF files only)
- **Product document uploads** (PDF and DOCX files)

## Setup Steps

### 1. Create a Blob Store
1. Navigate to your Vercel Project dashboard
2. Select the **Storage** tab
3. Click **Connect Database**
4. Under **Create New**, select **Blob**
5. Click **Continue**
6. Use the name "Documents" (or any name you prefer)
7. Select **Create a new Blob store**
8. Choose the environments where you want the read-write token

### 2. Environment Variable
Vercel automatically creates the following environment variable:
```
BLOB_READ_WRITE_TOKEN
```

### 3. Local Development
Pull the environment variables locally:
```bash
vercel env pull
```

### 4. API Routes Created
The following API routes have been created for blob uploads:

#### Brand Documents
- `/api/documents/upload` - Handles Vercel Blob upload tokens for brand documents (PDF only)
- `/api/documents/process` - Processes brand documents from blob URLs using DocuPanda

#### Product Documents
- `/api/product-documents/upload` - Handles Vercel Blob upload tokens for product documents (PDF and DOCX)
- Processing is handled directly in the server action to avoid authentication issues with server-to-server calls

### 5. Components Updated
The following components have been updated to use Vercel Blob:

#### Brand Documents
- `BrandDetailsForm.tsx` - Brand setup form with document upload
- `BrandDetails.tsx` - Brand management with document upload

#### Product Documents
- `UploadForm.tsx` - Product document upload form
- `product-document.ts` - Product document service

## How It Works

### Brand Documents
1. **Authentication Check**: User must be authenticated to upload
2. **Client Upload**: Files are uploaded directly from the browser to Vercel Blob
3. **Security**: Upload tokens are generated server-side with user context
4. **Processing**: Once uploaded, the blob URL is sent to the processing API
5. **DocuPanda**: The document is downloaded from blob and processed with DocuPanda
6. **AI Extraction**: Text is extracted and processed by AI for brand information

### Product Documents
1. **Authentication Check**: User must be authenticated to upload
2. **Client Upload**: Files are uploaded directly from the browser to Vercel Blob
3. **Security**: Upload tokens are generated server-side with user context
4. **Direct Processing**: DocuPanda processing happens directly in the server action
5. **Text Extraction**: Extracted text is stored in the database with the blob URL

## File Size Limits

- **Before**: 4.5MB limit due to Vercel's request size limit
- **After**: Much larger files supported via direct blob upload

## File Type Support

### Brand Documents
- **PDF files only** - For brand guides and documentation

### Product Documents
- **PDF files** - Product documentation, specifications
- **DOCX files** - Microsoft Word documents

## Testing

For local development with full upload flow (including `onUploadCompleted`):
- Use a tunneling service like ngrok
- Vercel Blob cannot contact `localhost` for completion webhooks

## Security Features

- **Authentication Required**: All upload and processing routes check for authenticated users
- **File Type Validation**: Strict validation on both client and server
- **User Context**: Upload tokens include user ID for audit purposes
- **Random Suffixes**: Files are uploaded with random suffixes to prevent conflicts
- **Secure Downloads**: Blob URLs are verified before processing

## Error Handling

- **File Type Validation**: Only allowed file types accepted
- **Authentication Errors**: 401 responses for unauthenticated requests
- **Upload Failures**: Proper error messages and logging
- **Processing Failures**: Graceful handling of DocuPanda errors
- **Toast Notifications**: User-friendly feedback for all operations

## Migration from Direct Upload

### Brand Documents
The old `/api/documents` route has been replaced with:
- `/api/documents/upload` - For blob upload handling
- `/api/documents/process` - For document processing from blob URLs

### Product Documents
The product document upload has been updated to:
- Use Vercel Blob for initial upload instead of Supabase storage
- Store blob URLs in the database instead of Supabase storage paths
- Support both legacy file uploads and new blob uploads

## Database Considerations

### Product Documents
- The `file_path` field now stores either:
  - Blob URLs (for new uploads)
  - Supabase storage paths (for legacy uploads)
- Legacy file handling is maintained for backward compatibility

This change maintains the same functionality while supporting larger files and better performance.

## Architecture Notes

### Why Different Processing Approaches?

**Brand Documents**: Use API routes for processing to maintain separation of concerns and enable potential future features like webhooks or external processing triggers.

**Product Documents**: Use direct processing in server actions to avoid authentication complexity with server-to-server calls. Since both the upload and processing happen in the same user context, this approach is simpler and more reliable. 