#!/bin/bash
set -e

echo "🚀 Deploying Zero Server to Fly.io (dev)..."

# # Create app if it doesn't exist
# fly app create axc-dev-zero-cache --org personal || echo "App already exists"


# # Set secrets (you'll need to provide these values)
# echo "🔐 Setting Fly secrets for dev environment..."
# echo "Please run these commands to set your dev secrets:"
# echo "fly secrets set ZERO_UPSTREAM_DB='postgres://postgres:<EMAIL>:6543/postgres' --app axc-dev-zero-cache"
# echo "fly secrets set ZERO_CVR_DB='postgres://postgres:<EMAIL>:6543/postgres' --app axc-dev-zero-cache"
# echo "fly secrets set ZERO_CHANGE_DB='postgres://postgres:<EMAIL>:6543/postgres' --app axc-dev-zero-cache"
# echo "fly secrets set ZERO_AUTH_SECRET='ZSq20niwM8/gI2EFd/Qksl142LAtdESDPD/zlu0cjyFXqPdwmgMjXKU0jM8X+bOWhjBJAEVfVEc3Q0nQq7UjEQ==' --app axc-dev-zero-cache"
# echo ""
# echo "Press Enter to continue after setting secrets..."
# read

# Deploy using dev configuration
fly deploy --config deploy/fly-dev.toml --app axc-dev-zero-cache

echo "✅ Dev deployment complete!"
echo "🔗 Service URL: https://axc-dev-zero-cache.fly.dev/"
echo "📝 Next: Run ./deploy-permissions-dev.sh to deploy permissions"