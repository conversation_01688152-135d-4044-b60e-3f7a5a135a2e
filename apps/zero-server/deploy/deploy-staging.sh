#!/bin/bash
set -e

echo "🚀 Deploying Zero Server to Fly.io (staging)..."

# Create app if it doesn't exist
# fly app create axc-staging-zero-cache --org personal || echo "App already exists"

# Set secrets (you'll need to provide these values)
# echo "🔐 Setting Fly secrets for staging environment..."
# echo "Please run these commands to set your staging secrets:"
# echo "fly secrets set ZERO_UPSTREAM_DB='postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require' --app axc-staging-zero-cache"
# echo "fly secrets set ZERO_CVR_DB='postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require' --app axc-staging-zero-cache"
# echo "fly secrets set ZERO_CHANGE_DB='postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require' --app axc-staging-zero-cache"
# echo "fly secrets set ZERO_AUTH_SECRET='2YVglaGxdj+dXDNScuXa6vUutCuG3XAEB4B8ZDZLtKMnO7OiR/2kTMdjQsol0I7XWuxBHDpNPWSw4dk7297lUg==' --app axc-staging-zero-cache"
# echo ""
# echo "Press Enter to continue after setting secrets..."
# read

# Deploy using staging configuration
fly deploy --config deploy/fly-staging.toml --app axc-staging-zero-cache

echo "✅ Staging deployment complete!"
echo "🔗 Service URL: https://axc-staging-zero-cache.fly.dev/"
echo "📝 Next: Run ./deploy-permissions-staging.sh to deploy permissions"