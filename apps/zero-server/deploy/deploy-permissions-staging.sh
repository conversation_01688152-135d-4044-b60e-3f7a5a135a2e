#!/bin/bash
set -e

echo "📋 Deploying Zero permissions to staging environment..."

# Find schema file
SCHEMA_PATH=""
if [ -f "../../packages/zero-schema/src/schema.ts" ]; then
    SCHEMA_PATH="../../packages/zero-schema/src/schema.ts"
elif [ -f "../web/lib/schema.ts" ]; then
    SCHEMA_PATH="../web/lib/schema.ts"
elif [ -f "./src/schema.ts" ]; then
    SCHEMA_PATH="./src/schema.ts"
else
    echo "❌ Schema file not found. Please specify the path to your Zero schema file."
    echo "Common locations checked:"
    echo "- ../../../packages/zero-schema/src/schema.ts"
    echo "- ../web/lib/schema.ts"
    echo "- ./src/schema.ts"
    exit 1
fi

echo "📂 Using schema file: $SCHEMA_PATH"

# Note: Fly secrets can't be read directly for security
# You need to provide the staging Supabase connection string here
echo "🔐 Please provide your staging Supabase connection string:"
read -s ZERO_UPSTREAM_DB
export ZERO_UPSTREAM_DB

# Deploy permissions
echo "🔑 Deploying permissions..."
npx zero-deploy-permissions --schema-path="$SCHEMA_PATH"

echo "✅ Staging permissions deployed successfully!"