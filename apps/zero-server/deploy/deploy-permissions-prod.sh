#!/bin/bash
set -e

echo "📋 Deploying Zero permissions to production environment..."

# Find schema file
SCHEMA_PATH=""
if [ -f "../../packages/zero-schema/src/schema.ts" ]; then
    SCHEMA_PATH="../../packages/zero-schema/src/schema.ts"
elif [ -f "../web/lib/schema.ts" ]; then
    SCHEMA_PATH="../web/lib/schema.ts"
elif [ -f "./src/schema.ts" ]; then
    SCHEMA_PATH="./src/schema.ts"
else
    echo "❌ Schema file not found. Please specify the path to your Zero schema file."
    echo "Common locations checked:"
    echo "- ../../packages/zero-schema/src/schema.ts"
    echo "- ../web/lib/schema.ts"
    echo "- ./src/schema.ts"
    exit 1
fi

echo "📂 Using schema file: $SCHEMA_PATH"

# Use production Supabase connection from fly.toml
export ZERO_UPSTREAM_DB="postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require"

# Deploy permissions
echo "🔑 Deploying permissions..."
npx zero-deploy-permissions --schema-path="$SCHEMA_PATH"

echo "✅ Production permissions deployed successfully!"