# Zero Server - Fly.io Multi-Environment Deployment

Deploy Zero Server to dev, staging, and production environments on Fly.io with automated GitHub Actions.

## Environment Setup

- **Production**: `axc-prod-zero-cache` (existing)
- **Staging**: `axc-staging-zero-cache` 
- **Development**: `axc-dev-zero-cache`

## GitHub Actions Setup (Recommended)

### 1. Add GitHub Secrets

In your GitHub repository settings, add these secrets:

- `FLY_API_TOKEN` - Get with: `fly auth token`
- `SUPABASE_CONNECTION_DEV` - Your dev Supabase connection string
- `SUPABASE_CONNECTION_STAGING` - Your staging Supabase connection string  
- `SUPABASE_CONNECTION_PROD` - Your prod Supabase connection string
- `ZERO_AUTH_SECRET_DEV` - Generate with: `openssl rand -base64 64`
- `ZERO_AUTH_SECRET_STAGING` - Generate with: `openssl rand -base64 64`

### 2. Deploy via Git Push

- Push to `main` → Deploy to production
- Push to `staging` → Deploy to staging
- Push to `develop` → Deploy to dev

## Manual Deployment

### 1. Deploy Services

```bash
# Deploy development
./deploy/deploy-dev.sh

# Deploy staging
./deploy/deploy-staging.sh

# Deploy production (existing)
fly deploy --app axc-prod-zero-cache
```

### 2. Deploy Permissions

```bash
./deploy/deploy-permissions-dev.sh
./deploy/deploy-permissions-staging.sh  
./deploy/deploy-permissions-prod.sh
```

## Service URLs

- **Dev**: https://axc-dev-zero-cache.fly.dev/
- **Staging**: https://axc-staging-zero-cache.fly.dev/
- **Production**: https://axc-prod-zero-cache.fly.dev/