app = "axc-dev-zero-cache"
primary_region = "ams"

[build]
image = "registry.hub.docker.com/rocicorp/zero:0.21.2025062401"

[http_service]
internal_port = 4848
force_https = true
auto_stop_machines = 'off'
min_machines_running = 1

[[http_service.checks]]
grace_period = "10s"
interval = "30s"
method = "GET"
timeout = "5s"
path = "/"

[[vm]]
memory = "2gb"
cpu_kind = "shared"
cpus = 2

[mounts]
source = "sqlite_db_dev"
destination = "/data"

[env]
ZERO_REPLICA_FILE = "/data/sync-replica.db"
ZERO_PUSH_URL = "https://preview-dot-psychic-valve-439013-d2.lm.r.appspot.com/push"
LOG_LEVEL = "debug"