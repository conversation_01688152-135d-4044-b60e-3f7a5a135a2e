{"name": "zero-server", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "dev": "npx zero-cache-dev", "start": "npx zero-cache"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "description": "", "dependencies": {"@kit/zero-schema": "workspace:*", "@rocicorp/zero": "0.21.**********"}, "devDependencies": {"@types/node": "^22.15.9", "typescript": "^5.8.3"}}