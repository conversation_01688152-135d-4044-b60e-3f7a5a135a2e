# PowerSync Sync Rules for Content Marketing Platform
# Multi-tenant architecture where users access data through account memberships

bucket_definitions:
  # User's personal account data (always synced)
  user_account:
    parameters: SELECT request.user_id() AS account_id
    data:
      - SELECT * FROM accounts WHERE id = bucket.account_id AND is_personal_account = true
      - SELECT * FROM onboarding WHERE account_id = bucket.account_id

  # Team accounts the user has access to
  team_accounts:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT * FROM accounts WHERE id = bucket.account_id AND is_personal_account = false
      - SELECT user_id || '_' || account_id AS id, user_id, account_id, account_role, created_at, updated_at, created_by, updated_by FROM accounts_memberships WHERE account_id = bucket.account_id

  # Roles and permissions (global data)
  roles_and_permissions:
    data:
      - SELECT name AS id, name, hierarchy_level FROM roles
      - SELECT id, role, permission FROM role_permissions

  # Company brand and identity data
  company_brand_data:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT company_id AS id, company_id, mission, vision, value_proposition, audience, personality, messaging_pillars, voice, identity, guidelines, brand_colors, brand_fonts, product_list, has_brand_setup, created_at, updated_at FROM company_brand WHERE company_id = bucket.account_id
      - SELECT * FROM feature_usage WHERE account_id = bucket.account_id

  # ICPs and Personas for accounts user has access to
  personas_and_icps:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT * FROM icps WHERE company_id = bucket.account_id
      - SELECT * FROM personas WHERE company_id = bucket.account_id
      - SELECT * FROM product_documents WHERE company_id = bucket.account_id

  # Campaign data for accounts user has access to
  campaigns:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT * FROM company_campaigns WHERE company_id = bucket.account_id
      - SELECT * FROM campaign_ideas WHERE company_id = bucket.account_id
      - SELECT * FROM company_task_statuses WHERE company_id = bucket.account_id

  # Content data for accounts user has access to  
  content:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT * FROM company_content WHERE company_id = bucket.account_id ORDER BY created_at DESC LIMIT 50


  # Research data for accounts user has access to
  research:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT * FROM saved_research WHERE account_id = bucket.account_id
      - SELECT * FROM generated_research WHERE account_id = bucket.account_id

  # Social media profiles - user's own profiles
  user_social_profiles:
    parameters: SELECT request.user_id() AS user_id
    data:
      - SELECT id || '_' || user_id AS id, id AS profile_id, user_id, company_id, title, "refId", "profileKey", "messagingActive", permissions, is_shared, is_active, profile_name, description, created_at, updated_at FROM ayrshare_user_profile WHERE user_id = bucket.user_id
      - SELECT * FROM social_profiles WHERE user_id = bucket.user_id

  # Twitter OAuth state for user
  twitter_oauth:
    parameters: SELECT request.user_id() AS user_id
    data:
      - SELECT user_id AS id, user_id, state, access_token, refresh_token, scope, expires_in, refresh_token_expires_in, screen_name, name, profile_image_url, description, code_verifier FROM twitterState WHERE user_id = bucket.user_id

  # Shared social profiles for accounts user has access to
  shared_social_profiles:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT id || '_' || company_id AS id, id AS profile_id, user_id, company_id, title, "refId", "profileKey", "messagingActive", permissions, is_shared, is_active, profile_name, description, created_at, updated_at FROM ayrshare_user_profile WHERE company_id = bucket.account_id AND is_shared = true
      - SELECT * FROM social_profiles WHERE account_id = bucket.account_id AND is_shared = true

  # Notifications for user's accounts
  notifications:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT * FROM notifications WHERE account_id = bucket.account_id AND dismissed = false

  # Personal notifications
  personal_notifications:
    parameters: SELECT request.user_id() AS account_id
    data:
      - SELECT * FROM notifications WHERE account_id = bucket.account_id AND dismissed = false

  # Billing data - main billing entities only (items need denormalization)
  billing:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT id, account_id, email, provider, customer_id FROM billing_customers WHERE account_id = bucket.account_id
      - SELECT * FROM subscriptions WHERE account_id = bucket.account_id
      - SELECT * FROM orders WHERE account_id = bucket.account_id

  # Invitations - relies on client-side permission filtering based on synced roles
  invitations:
    parameters: SELECT account_id FROM accounts_memberships WHERE user_id = request.user_id()
    data:
      - SELECT * FROM invitations WHERE account_id = bucket.account_id

  # Global configuration (synced to all authenticated users)
  global_config:
    data:
      - SELECT 'app_config' AS id, enable_team_accounts, enable_account_billing, enable_team_account_billing, billing_provider FROM config