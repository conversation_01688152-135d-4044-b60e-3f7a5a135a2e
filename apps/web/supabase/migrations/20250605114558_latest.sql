drop function if exists "kit"."cleanup_expired_nonces"(p_older_than_days integer, p_include_used boolean, p_include_revoked boolean);


create type "public"."campaign_status" as enum ('Draft', 'Ready', 'In Progress', 'Completed', 'Archived');

drop trigger if exists "social_profiles_set_timestamps" on "public"."social_profiles";

drop policy "restrict_mfa_accounts" on "public"."accounts";

drop policy "super_admins_access_accounts" on "public"."accounts";

drop policy "restrict_mfa_accounts_memberships" on "public"."accounts_memberships";

drop policy "super_admins_access_accounts_memberships" on "public"."accounts_memberships";

drop policy "restrict_mfa_invitations" on "public"."invitations";

drop policy "super_admins_access_invitations" on "public"."invitations";

drop policy "Users can read their own nonces" on "public"."nonces";

drop policy "restrict_mfa_notifications" on "public"."notifications";

drop policy "restrict_mfa_order_items" on "public"."order_items";

drop policy "super_admins_access_order_items" on "public"."order_items";

drop policy "restrict_mfa_orders" on "public"."orders";

drop policy "super_admins_access_orders" on "public"."orders";

drop policy "restrict_mfa_role_permissions" on "public"."role_permissions";

drop policy "super_admins_access_role_permissions" on "public"."role_permissions";

drop policy "social_profiles_delete" on "public"."social_profiles";

drop policy "social_profiles_insert" on "public"."social_profiles";

drop policy "social_profiles_select" on "public"."social_profiles";

drop policy "social_profiles_update" on "public"."social_profiles";

drop policy "restrict_mfa_subscription_items" on "public"."subscription_items";

drop policy "super_admins_access_subscription_items" on "public"."subscription_items";

drop policy "restrict_mfa_subscriptions" on "public"."subscriptions";

drop policy "super_admins_access_subscriptions" on "public"."subscriptions";

drop policy "invitations_read_self" on "public"."invitations";

revoke delete on table "public"."nonces" from "anon";

revoke insert on table "public"."nonces" from "anon";

revoke references on table "public"."nonces" from "anon";

revoke select on table "public"."nonces" from "anon";

revoke trigger on table "public"."nonces" from "anon";

revoke truncate on table "public"."nonces" from "anon";

revoke update on table "public"."nonces" from "anon";

revoke delete on table "public"."nonces" from "authenticated";

revoke insert on table "public"."nonces" from "authenticated";

revoke references on table "public"."nonces" from "authenticated";

revoke select on table "public"."nonces" from "authenticated";

revoke trigger on table "public"."nonces" from "authenticated";

revoke truncate on table "public"."nonces" from "authenticated";

revoke update on table "public"."nonces" from "authenticated";

revoke delete on table "public"."nonces" from "service_role";

revoke insert on table "public"."nonces" from "service_role";

revoke references on table "public"."nonces" from "service_role";

revoke select on table "public"."nonces" from "service_role";

revoke trigger on table "public"."nonces" from "service_role";

revoke truncate on table "public"."nonces" from "service_role";

revoke update on table "public"."nonces" from "service_role";

revoke delete on table "public"."social_profiles" from "anon";

revoke insert on table "public"."social_profiles" from "anon";

revoke references on table "public"."social_profiles" from "anon";

revoke select on table "public"."social_profiles" from "anon";

revoke trigger on table "public"."social_profiles" from "anon";

revoke truncate on table "public"."social_profiles" from "anon";

revoke update on table "public"."social_profiles" from "anon";

revoke delete on table "public"."social_profiles" from "authenticated";

revoke insert on table "public"."social_profiles" from "authenticated";

revoke references on table "public"."social_profiles" from "authenticated";

revoke select on table "public"."social_profiles" from "authenticated";

revoke trigger on table "public"."social_profiles" from "authenticated";

revoke truncate on table "public"."social_profiles" from "authenticated";

revoke update on table "public"."social_profiles" from "authenticated";

revoke delete on table "public"."social_profiles" from "service_role";

revoke insert on table "public"."social_profiles" from "service_role";

revoke references on table "public"."social_profiles" from "service_role";

revoke select on table "public"."social_profiles" from "service_role";

revoke trigger on table "public"."social_profiles" from "service_role";

revoke truncate on table "public"."social_profiles" from "service_role";

revoke update on table "public"."social_profiles" from "service_role";

alter table "public"."nonces" drop constraint "nonces_user_id_fkey";

alter table "public"."social_profiles" drop constraint "social_profiles_account_id_fkey";

alter table "public"."social_profiles" drop constraint "social_profiles_user_id_fkey";

alter table "public"."social_profiles" drop constraint "unique_user_account_title";

drop function if exists "public"."create_nonce"(p_user_id uuid, p_purpose text, p_expires_in_seconds integer, p_metadata jsonb, p_scopes text[], p_revoke_previous boolean);

drop function if exists "public"."get_nonce_status"(p_id uuid);

drop function if exists "public"."is_aal2"();

drop function if exists "public"."is_mfa_compliant"();

drop function if exists "public"."is_super_admin"();

drop function if exists "public"."revoke_nonce"(p_id uuid, p_reason text);

drop function if exists "public"."verify_nonce"(p_token text, p_purpose text, p_user_id uuid, p_required_scopes text[], p_max_verification_attempts integer, p_ip inet, p_user_agent text);

drop function if exists "public"."get_user_social_profiles"(target_account_id uuid, target_user_id uuid);

alter table "public"."nonces" drop constraint "nonces_pkey";

alter table "public"."social_profiles" drop constraint "social_profiles_pkey";

drop index if exists "public"."idx_nonces_status";

drop index if exists "public"."nonces_pkey";

drop index if exists "public"."social_profiles_account_id_idx";

drop index if exists "public"."social_profiles_pkey";

drop index if exists "public"."social_profiles_profile_key_idx";

drop index if exists "public"."social_profiles_shared_idx";

drop index if exists "public"."social_profiles_user_id_idx";

drop index if exists "public"."unique_user_account_title";

drop table "public"."nonces";

drop table "public"."social_profiles";

create table "public"."ayrshare_user_profile" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "created_at" timestamp with time zone not null default now(),
    "title" text,
    "refId" text not null,
    "profileKey" text not null,
    "messagingActive" boolean,
    "company_id" uuid,
    "description" text,
    "is_active" boolean not null default true,
    "profile_name" character varying(255) not null default 'Default Profile'::character varying,
    "is_shared" boolean default false,
    "permissions" jsonb default '{"can_post": false, "can_view": true, "can_analytics": false}'::jsonb,
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."ayrshare_user_profile" enable row level security;

create table "public"."campaign_ideas" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "company_id" uuid not null,
    "campaign_id" uuid not null,
    "content" text not null,
    "is_selected" boolean default false,
    "metadata" jsonb,
    "brief" jsonb,
    "content_types" jsonb,
    "languages" jsonb,
    "channels" jsonb,
    "title" text,
    "brief_blocks" jsonb,
    "content_blocks" jsonb
);


create table "public"."campaign_personas" (
    "id" uuid not null default gen_random_uuid(),
    "campaign_id" uuid not null,
    "persona_id" uuid not null,
    "is_primary" boolean default false,
    "created_at" timestamp with time zone default now()
);


create table "public"."campaign_templates" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "title" character varying,
    "goal" character varying,
    "description" character varying,
    "style" jsonb,
    "image_url" character varying,
    "duration_weeks" smallint
);


alter table "public"."campaign_templates" enable row level security;

create table "public"."company_brand" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "company_id" uuid not null,
    "mission" text,
    "vision" text,
    "value_proposition" text,
    "audience" text,
    "personality" text,
    "messaging_pillars" text,
    "identity" text,
    "guidelines" text,
    "voice" text,
    "brand_colors" jsonb,
    "has_brand_setup" boolean,
    "product_list" text,
    "updated_at" timestamp without time zone,
    "brand_fonts" jsonb,
    "is_draft" boolean,
    "brand_name" text
);


create table "public"."company_campaigns" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "company_id" uuid not null,
    "user_id" uuid not null,
    "name" text not null,
    "slug" text not null,
    "messaging" text,
    "value_prop" text,
    "objective" text,
    "identity" text,
    "kpis" text,
    "objectives" text,
    "guidelines" text,
    "personas" text,
    "personality" text,
    "targetAudience" text,
    "tone" text,
    "visualStyle" text,
    "voice" text,
    "documents" jsonb,
    "metadata" jsonb,
    "status" campaign_status default 'Draft'::campaign_status,
    "end_date" date,
    "start_date" date,
    "has_reached_summary" boolean default false,
    "external_research" jsonb,
    "is_generating" boolean
);


alter table "public"."company_campaigns" enable row level security;

create table "public"."company_content" (
    "id" uuid not null default gen_random_uuid(),
    "campaign_id" uuid,
    "company_id" uuid,
    "idea_id" uuid,
    "content_type" text,
    "language" text,
    "content" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "image_path" text,
    "content_template" text,
    "image_url" text,
    "channel" text,
    "has_image" boolean default false,
    "visual_description" text,
    "avatar_script" text,
    "avatar_voice_id" text,
    "avatar_presenter_id" text,
    "avatar_video_id" text,
    "avatar_video_url" text,
    "is_avatar_ready" boolean,
    "has_avatar" boolean,
    "is_generating" boolean,
    "status" text default ''::text,
    "task_description" text,
    "task_id" text,
    "task_title" text,
    "has_video_presentation" boolean,
    "video_presentation_url" text,
    "video_presentation_render_params" jsonb,
    "video_presentation_script" text,
    "seo_keywords" jsonb,
    "trend_keywords" jsonb,
    "is_posted" boolean default false,
    "is_scheduled" boolean default false,
    "is_draft" boolean default true,
    "visual_description_group" jsonb,
    "content_editor_template" jsonb,
    "scheduled_publishing_time" date,
    "video_editor_overlays" jsonb,
    "video_editor_aspect_ratio" jsonb,
    "video_editor_player_dimensions" jsonb
);


alter table "public"."company_content" enable row level security;

create table "public"."company_task_statuses" (
    "id" uuid not null default gen_random_uuid(),
    "company_id" uuid not null,
    "name" text not null,
    "display_name" text not null,
    "status_order" integer not null,
    "color" text,
    "icon" text,
    "created_at" timestamp with time zone default now()
);


alter table "public"."company_task_statuses" enable row level security;

create table "public"."content_personas" (
    "id" uuid not null default gen_random_uuid(),
    "content_id" uuid not null,
    "persona_id" uuid not null,
    "is_primary" boolean default false,
    "created_at" timestamp with time zone default now()
);


create table "public"."feature_usage" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "feature" character varying(255) not null,
    "usage" jsonb not null default '{}'::jsonb,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."feature_usage" enable row level security;

create table "public"."generated_research" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "icp_id" uuid not null,
    "persona_id" uuid not null,
    "research_type" text,
    "time_filter" text,
    "title" text,
    "results" jsonb default '[]'::jsonb,
    "content_suggestions" jsonb default '[]'::jsonb,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "created_by" uuid,
    "updated_by" uuid,
    "topic" text,
    "is_generating" boolean
);


alter table "public"."generated_research" enable row level security;

create table "public"."linkedInState" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "state" character varying,
    "access_token" character varying,
    "refresh_token" character varying,
    "scope" character varying,
    "user_id" uuid default auth.uid(),
    "first_name" character varying,
    "last_name" character varying,
    "profile_picture_url" character varying,
    "headline" character varying,
    "updated_at" timestamp without time zone,
    "expires_in" timestamp without time zone,
    "refresh_token_expires_in" timestamp without time zone,
    "person_urn" character varying
);


create table "public"."onboarding" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "data" jsonb default '{}'::jsonb,
    "completed" boolean default false,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."onboarding" enable row level security;

create table "public"."personas" (
    "id" uuid not null default gen_random_uuid(),
    "name" character varying(255) not null,
    "role" character varying(255) not null,
    "avatar_url" text,
    "department" character varying(255),
    "management_level" character varying(50),
    "status" character varying(20) default 'Active'::character varying,
    "industries" jsonb,
    "company_size" character varying(50),
    "location" character varying(255),
    "tech_stack" jsonb,
    "budget_range" character varying(50),
    "challenges" jsonb,
    "goals" jsonb,
    "decision_authority" character varying(50),
    "buying_stage" character varying(50),
    "info_preferences" jsonb,
    "content_formats" jsonb,
    "communication_style" character varying(50),
    "content_length" character varying(50),
    "channels" jsonb,
    "topics" jsonb,
    "content_count" integer default 0,
    "last_used" timestamp with time zone,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "company_id" uuid not null,
    "icp_id" uuid
);


create table "public"."product_documents" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "title" text not null,
    "file_path" text not null,
    "company_id" uuid not null,
    "content" text,
    "file_type" text not null
);


create table "public"."saved_research" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "icp_id" uuid not null,
    "persona_id" uuid not null,
    "title" text not null,
    "description" text not null,
    "source" text,
    "relevance_score" integer,
    "research_type" text,
    "time_filter" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "source_url" text,
    "topic" text
);


alter table "public"."saved_research" enable row level security;

create table "public"."twitterState" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp without time zone,
    "state" character varying,
    "access_token" character varying,
    "refresh_token" character varying,
    "scope" character varying,
    "expires_in" timestamp without time zone,
    "refresh_token_expires_in" timestamp without time zone,
    "user_id" uuid not null default auth.uid(),
    "screen_name" character varying,
    "name" character varying,
    "profile_image_url" character varying,
    "description" character varying,
    "code_verifier" character varying
);


create table "public"."user_cache" (
    "created_at" timestamp with time zone not null default now(),
    "selected_campaign" uuid,
    "user_id" uuid not null
);


alter table "public"."user_cache" enable row level security;

alter table "public"."accounts" add column "website" character varying;

alter table "public"."icps" disable row level security;

CREATE INDEX ayrshare_user_profile_company_id_idx ON public.ayrshare_user_profile USING btree (company_id);

CREATE INDEX ayrshare_user_profile_is_active_idx ON public.ayrshare_user_profile USING btree (is_active);

CREATE UNIQUE INDEX ayrshare_user_profile_pkey ON public.ayrshare_user_profile USING btree (id);

CREATE UNIQUE INDEX campaign_ideas_pkey ON public.campaign_ideas USING btree (id);

CREATE UNIQUE INDEX campaign_personas_campaign_id_persona_id_key ON public.campaign_personas USING btree (campaign_id, persona_id);

CREATE UNIQUE INDEX campaign_personas_pkey ON public.campaign_personas USING btree (id);

CREATE UNIQUE INDEX campaign_templates_pkey ON public.campaign_templates USING btree (id);

CREATE UNIQUE INDEX company_brand_pkey ON public.company_brand USING btree (id);

CREATE UNIQUE INDEX company_campaigns_pkey ON public.company_campaigns USING btree (id);

CREATE UNIQUE INDEX company_campaigns_slug_key ON public.company_campaigns USING btree (slug);

CREATE UNIQUE INDEX company_content_pkey ON public.company_content USING btree (id);

CREATE UNIQUE INDEX company_task_statuses_company_id_name_key ON public.company_task_statuses USING btree (company_id, name);

CREATE UNIQUE INDEX company_task_statuses_pkey ON public.company_task_statuses USING btree (id);

CREATE UNIQUE INDEX content_personas_content_id_persona_id_key ON public.content_personas USING btree (content_id, persona_id);

CREATE UNIQUE INDEX content_personas_pkey ON public.content_personas USING btree (id);

CREATE UNIQUE INDEX feature_usage_account_id_feature_key ON public.feature_usage USING btree (account_id, feature);

CREATE UNIQUE INDEX feature_usage_pkey ON public.feature_usage USING btree (id);

CREATE UNIQUE INDEX generated_research_pkey ON public.generated_research USING btree (id);

CREATE INDEX idx_company_content_campaign_idea ON public.company_content USING btree (campaign_id, idea_id);

CREATE INDEX idx_company_content_images ON public.company_content USING btree (image_url) WHERE (image_url IS NOT NULL);

CREATE INDEX idx_company_content_lookup ON public.company_content USING btree (campaign_id, idea_id, content_type, language);

CREATE INDEX idx_company_content_video_editor ON public.company_content USING btree (id) WHERE (video_editor_overlays IS NOT NULL);

CREATE INDEX idx_company_task_statuses_company_id ON public.company_task_statuses USING btree (company_id);

CREATE INDEX idx_feature_usage_account_id ON public.feature_usage USING btree (account_id, feature);

CREATE INDEX idx_generated_research_account_id ON public.generated_research USING btree (account_id);

CREATE INDEX idx_generated_research_created_at ON public.generated_research USING btree (created_at DESC);

CREATE INDEX idx_generated_research_icp_persona ON public.generated_research USING btree (icp_id, persona_id);

CREATE INDEX idx_generated_research_topic ON public.generated_research USING btree (topic);

CREATE INDEX idx_saved_research_account_id ON public.saved_research USING btree (account_id);

CREATE INDEX idx_saved_research_icp_persona ON public.saved_research USING btree (icp_id, persona_id);

CREATE INDEX idx_saved_research_topic ON public.saved_research USING btree (topic);

CREATE UNIQUE INDEX "linkedInState_pkey" ON public."linkedInState" USING btree (id);

CREATE UNIQUE INDEX "linkedInState_user_id_key" ON public."linkedInState" USING btree (user_id);

CREATE UNIQUE INDEX onboarding_account_id_key ON public.onboarding USING btree (account_id);

CREATE UNIQUE INDEX onboarding_pkey ON public.onboarding USING btree (id);

CREATE INDEX personas_company_id_idx ON public.personas USING btree (company_id);

CREATE INDEX personas_icp_id_idx ON public.personas USING btree (icp_id);

CREATE INDEX personas_name_idx ON public.personas USING btree (name);

CREATE UNIQUE INDEX personas_pkey ON public.personas USING btree (id);

CREATE INDEX personas_status_idx ON public.personas USING btree (status);

CREATE UNIQUE INDEX product_documents_pkey ON public.product_documents USING btree (id);

CREATE UNIQUE INDEX saved_research_account_id_icp_id_persona_id_title_research__key ON public.saved_research USING btree (account_id, icp_id, persona_id, title, research_type);

CREATE UNIQUE INDEX saved_research_pkey ON public.saved_research USING btree (id);

CREATE UNIQUE INDEX "twitterState_pkey" ON public."twitterState" USING btree (id);

CREATE UNIQUE INDEX "twitterState_user_id_key" ON public."twitterState" USING btree (user_id);

CREATE UNIQUE INDEX user_cache_pkey ON public.user_cache USING btree (user_id);

CREATE UNIQUE INDEX user_cache_user_id_key ON public.user_cache USING btree (user_id);

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_pkey" PRIMARY KEY using index "ayrshare_user_profile_pkey";

alter table "public"."campaign_ideas" add constraint "campaign_ideas_pkey" PRIMARY KEY using index "campaign_ideas_pkey";

alter table "public"."campaign_personas" add constraint "campaign_personas_pkey" PRIMARY KEY using index "campaign_personas_pkey";

alter table "public"."campaign_templates" add constraint "campaign_templates_pkey" PRIMARY KEY using index "campaign_templates_pkey";

alter table "public"."company_brand" add constraint "company_brand_pkey" PRIMARY KEY using index "company_brand_pkey";

alter table "public"."company_campaigns" add constraint "company_campaigns_pkey" PRIMARY KEY using index "company_campaigns_pkey";

alter table "public"."company_content" add constraint "company_content_pkey" PRIMARY KEY using index "company_content_pkey";

alter table "public"."company_task_statuses" add constraint "company_task_statuses_pkey" PRIMARY KEY using index "company_task_statuses_pkey";

alter table "public"."content_personas" add constraint "content_personas_pkey" PRIMARY KEY using index "content_personas_pkey";

alter table "public"."feature_usage" add constraint "feature_usage_pkey" PRIMARY KEY using index "feature_usage_pkey";

alter table "public"."generated_research" add constraint "generated_research_pkey" PRIMARY KEY using index "generated_research_pkey";

alter table "public"."linkedInState" add constraint "linkedInState_pkey" PRIMARY KEY using index "linkedInState_pkey";

alter table "public"."onboarding" add constraint "onboarding_pkey" PRIMARY KEY using index "onboarding_pkey";

alter table "public"."personas" add constraint "personas_pkey" PRIMARY KEY using index "personas_pkey";

alter table "public"."product_documents" add constraint "product_documents_pkey" PRIMARY KEY using index "product_documents_pkey";

alter table "public"."saved_research" add constraint "saved_research_pkey" PRIMARY KEY using index "saved_research_pkey";

alter table "public"."twitterState" add constraint "twitterState_pkey" PRIMARY KEY using index "twitterState_pkey";

alter table "public"."user_cache" add constraint "user_cache_pkey" PRIMARY KEY using index "user_cache_pkey";

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."ayrshare_user_profile" validate constraint "ayrshare_user_profile_company_id_fkey";

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_user_id_fkey" FOREIGN KEY (user_id) REFERENCES accounts(id) not valid;

alter table "public"."ayrshare_user_profile" validate constraint "ayrshare_user_profile_user_id_fkey";

alter table "public"."campaign_ideas" add constraint "campaign_ideas_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES company_campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."campaign_ideas" validate constraint "campaign_ideas_campaign_id_fkey";

alter table "public"."campaign_ideas" add constraint "campaign_ideas_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."campaign_ideas" validate constraint "campaign_ideas_company_id_fkey";

alter table "public"."campaign_personas" add constraint "campaign_personas_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES company_campaigns(id) ON DELETE CASCADE not valid;

alter table "public"."campaign_personas" validate constraint "campaign_personas_campaign_id_fkey";

alter table "public"."campaign_personas" add constraint "campaign_personas_campaign_id_persona_id_key" UNIQUE using index "campaign_personas_campaign_id_persona_id_key";

alter table "public"."campaign_personas" add constraint "campaign_personas_persona_id_fkey" FOREIGN KEY (persona_id) REFERENCES personas(id) ON DELETE CASCADE not valid;

alter table "public"."campaign_personas" validate constraint "campaign_personas_persona_id_fkey";

alter table "public"."company_brand" add constraint "company_brand_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."company_brand" validate constraint "company_brand_company_id_fkey";

alter table "public"."company_campaigns" add constraint "company_campaigns_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."company_campaigns" validate constraint "company_campaigns_company_id_fkey";

alter table "public"."company_campaigns" add constraint "company_campaigns_slug_key" UNIQUE using index "company_campaigns_slug_key";


alter table "public"."company_content" add constraint "company_content_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES company_campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."company_content" validate constraint "company_content_campaign_id_fkey";

alter table "public"."company_content" add constraint "company_content_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."company_content" validate constraint "company_content_company_id_fkey";

alter table "public"."company_content" add constraint "company_content_idea_id_fkey" FOREIGN KEY (idea_id) REFERENCES campaign_ideas(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."company_content" validate constraint "company_content_idea_id_fkey";

alter table "public"."company_task_statuses" add constraint "company_task_statuses_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."company_task_statuses" validate constraint "company_task_statuses_company_id_fkey";

alter table "public"."company_task_statuses" add constraint "company_task_statuses_company_id_name_key" UNIQUE using index "company_task_statuses_company_id_name_key";

alter table "public"."content_personas" add constraint "content_personas_content_id_fkey" FOREIGN KEY (content_id) REFERENCES company_content(id) ON DELETE CASCADE not valid;

alter table "public"."content_personas" validate constraint "content_personas_content_id_fkey";

alter table "public"."content_personas" add constraint "content_personas_content_id_persona_id_key" UNIQUE using index "content_personas_content_id_persona_id_key";

alter table "public"."content_personas" add constraint "content_personas_persona_id_fkey" FOREIGN KEY (persona_id) REFERENCES personas(id) ON DELETE CASCADE not valid;

alter table "public"."content_personas" validate constraint "content_personas_persona_id_fkey";

alter table "public"."feature_usage" add constraint "feature_usage_account_id_feature_key" UNIQUE using index "feature_usage_account_id_feature_key";

alter table "public"."feature_usage" add constraint "feature_usage_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."feature_usage" validate constraint "feature_usage_account_id_fkey";

alter table "public"."generated_research" add constraint "generated_research_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."generated_research" validate constraint "generated_research_account_id_fkey";

alter table "public"."generated_research" add constraint "generated_research_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."generated_research" validate constraint "generated_research_created_by_fkey";

alter table "public"."generated_research" add constraint "generated_research_research_type_check" CHECK ((research_type = ANY (ARRAY['pain-points'::text, 'trending-topics'::text, 'recent-news'::text]))) not valid;

alter table "public"."generated_research" validate constraint "generated_research_research_type_check";

alter table "public"."generated_research" add constraint "generated_research_updated_by_fkey" FOREIGN KEY (updated_by) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."generated_research" validate constraint "generated_research_updated_by_fkey";

alter table "public"."linkedInState" add constraint "linkedInState_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."linkedInState" validate constraint "linkedInState_user_id_fkey";

alter table "public"."linkedInState" add constraint "linkedInState_user_id_key" UNIQUE using index "linkedInState_user_id_key";

alter table "public"."onboarding" add constraint "onboarding_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) not valid;

alter table "public"."onboarding" validate constraint "onboarding_account_id_fkey";

alter table "public"."onboarding" add constraint "onboarding_account_id_key" UNIQUE using index "onboarding_account_id_key";

alter table "public"."personas" add constraint "personas_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."personas" validate constraint "personas_company_id_fkey";

alter table "public"."personas" add constraint "personas_icp_id_fkey" FOREIGN KEY (icp_id) REFERENCES icps(id) ON DELETE CASCADE not valid;

alter table "public"."personas" validate constraint "personas_icp_id_fkey";

alter table "public"."saved_research" add constraint "saved_research_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."saved_research" validate constraint "saved_research_account_id_fkey";

alter table "public"."saved_research" add constraint "saved_research_account_id_icp_id_persona_id_title_research__key" UNIQUE using index "saved_research_account_id_icp_id_persona_id_title_research__key";

alter table "public"."saved_research" add constraint "saved_research_relevance_score_check" CHECK (((relevance_score >= 0) AND (relevance_score <= 10))) not valid;

alter table "public"."saved_research" validate constraint "saved_research_relevance_score_check";

alter table "public"."saved_research" add constraint "saved_research_research_type_check" CHECK ((research_type = ANY (ARRAY['pain-points'::text, 'trending-topics'::text, 'recent-news'::text]))) not valid;

alter table "public"."saved_research" validate constraint "saved_research_research_type_check";

alter table "public"."twitterState" add constraint "twitterState_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."twitterState" validate constraint "twitterState_user_id_fkey";

alter table "public"."twitterState" add constraint "twitterState_user_id_key" UNIQUE using index "twitterState_user_id_key";

alter table "public"."user_cache" add constraint "cache_selected_campaign_fkey" FOREIGN KEY (selected_campaign) REFERENCES company_campaigns(id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."user_cache" validate constraint "cache_selected_campaign_fkey";

alter table "public"."user_cache" add constraint "cache_user_id_fkey" FOREIGN KEY (user_id) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."user_cache" validate constraint "cache_user_id_fkey";

alter table "public"."user_cache" add constraint "user_cache_user_id_key" UNIQUE using index "user_cache_user_id_key";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.add_company_task_status(p_company_id uuid, p_name text, p_display_name text, p_color text DEFAULT '#E2E8F0'::text, p_icon text DEFAULT 'Circle'::text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  new_position INT;
  new_status_id UUID;
BEGIN
  -- Get the next status_order
  SELECT COALESCE(MAX(status_order), 0) + 1 INTO new_position
  FROM public.company_task_statuses
  WHERE company_id = p_company_id;
  
  -- Insert new status
  INSERT INTO public.company_task_statuses (
    company_id, name, display_name, status_order, color, icon
  ) VALUES (
    p_company_id, LOWER(p_name), p_display_name, new_position, p_color, p_icon
  )
  RETURNING id INTO new_status_id;
  
  RETURN new_status_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_feature_usage_row()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.feature_usage (account_id, feature)
  VALUES (NEW.id, '');
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.delete_company_task_status(p_company_id uuid, p_status_id uuid, p_replacement_status text DEFAULT 'draft'::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  deleted_status_order INT;
  deleted_status_name TEXT;
BEGIN
  -- Check if replacement status exists for this company
  IF NOT public.is_valid_task_status(p_company_id, p_replacement_status) THEN
    RAISE EXCEPTION 'Replacement status is not valid for this company';
    RETURN FALSE;
  END IF;
  
  -- Get the status_order and name of the status to delete
  SELECT status_order, name INTO deleted_status_order, deleted_status_name
  FROM public.company_task_statuses
  WHERE id = p_status_id AND company_id = p_company_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Update any content using this status to the replacement status
  UPDATE public.company_content
  SET status = p_replacement_status
  WHERE company_id = p_company_id AND status = deleted_status_name;
  
  -- Delete the status
  DELETE FROM public.company_task_statuses
  WHERE id = p_status_id AND company_id = p_company_id;
  
  -- Reorder remaining statuses
  UPDATE public.company_task_statuses
  SET status_order = status_order - 1
  WHERE company_id = p_company_id AND status_order > deleted_status_order;
  
  RETURN TRUE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_company_task_statuses(p_company_id uuid)
 RETURNS TABLE(id uuid, name text, display_name text, status_order integer, color text, icon text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN QUERY
  SELECT 
    cts.id,
    cts.name,
    cts.display_name,
    cts.status_order,
    cts.color,
    cts.icon
  FROM 
    public.company_task_statuses cts
  WHERE 
    cts.company_id = p_company_id
  ORDER BY 
    cts.status_order;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.init_task_statuses_for_new_company()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  PERFORM public.initialize_company_task_statuses(NEW.id);
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.initialize_company_task_statuses(p_company_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- Only insert if no statuses exist for this company
  IF NOT EXISTS (SELECT 1 FROM public.company_task_statuses WHERE company_id = p_company_id) THEN
    INSERT INTO public.company_task_statuses (company_id, name, display_name, status_order, color, icon)
    VALUES
      (p_company_id, 'draft', 'Draft', 1, '#E2E8F0', 'Circle'),
      (p_company_id, 'to do', 'To Do', 2, '#3B82F6', 'Circle'),
      (p_company_id, 'in progress', 'In Progress', 3, '#F59E0B', 'Clock'),
      (p_company_id, 'done', 'Done', 4, '#10B981', 'Check');
  END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_valid_task_status(p_company_id uuid, p_status text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- Handle NULL or empty status
  IF p_status IS NULL OR p_status = '' THEN
    RETURN TRUE;
  END IF;
  
  -- Check if status exists in company_task_statuses
  RETURN EXISTS (
    SELECT 1 FROM public.company_task_statuses
    WHERE company_id = p_company_id 
    AND (name = p_status OR name = lower(p_status))
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.reorder_company_task_statuses(p_company_id uuid, p_status_ids uuid[])
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  i INT;
  status_id UUID;
BEGIN
  -- Validate all status IDs belong to the company
  IF EXISTS (
    SELECT 1 FROM UNNEST(p_status_ids) AS s(id)
    LEFT JOIN public.company_task_statuses cts ON cts.id = s.id AND cts.company_id = p_company_id
    WHERE cts.id IS NULL
  ) THEN
    RAISE EXCEPTION 'One or more status IDs do not belong to this company';
    RETURN FALSE;
  END IF;
  
  -- Update status_order
  FOR i IN 1..array_length(p_status_ids, 1) LOOP
    status_id := p_status_ids[i];
    UPDATE public.company_task_statuses
    SET status_order = i
    WHERE id = status_id AND company_id = p_company_id;
  END LOOP;
  
  RETURN TRUE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_company_task_status(p_status_id uuid, p_display_name text DEFAULT NULL::text, p_status_order integer DEFAULT NULL::integer, p_color text DEFAULT NULL::text, p_icon text DEFAULT NULL::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  update_query TEXT;
  params_count INT := 0;
BEGIN
  update_query := 'UPDATE public.company_task_statuses SET ';
  
  -- Build dynamic update query based on non-null parameters
  IF p_display_name IS NOT NULL THEN
    update_query := update_query || 'display_name = $1';
    params_count := params_count + 1;
  END IF;
  
  IF p_status_order IS NOT NULL THEN
    IF params_count > 0 THEN update_query := update_query || ', '; END IF;
    update_query := update_query || 'status_order = $' || (params_count + 1)::TEXT;
    params_count := params_count + 1;
  END IF;
  
  IF p_color IS NOT NULL THEN
    IF params_count > 0 THEN update_query := update_query || ', '; END IF;
    update_query := update_query || 'color = $' || (params_count + 1)::TEXT;
    params_count := params_count + 1;
  END IF;
  
  IF p_icon IS NOT NULL THEN
    IF params_count > 0 THEN update_query := update_query || ', '; END IF;
    update_query := update_query || 'icon = $' || (params_count + 1)::TEXT;
    params_count := params_count + 1;
  END IF;
  
  -- If no parameters were provided, return false
  IF params_count = 0 THEN
    RETURN FALSE;
  END IF;
  
  -- Complete the query with the WHERE clause
  update_query := update_query || ' WHERE id = $' || (params_count + 1)::TEXT;
  
  -- Execute the dynamic query
  EXECUTE update_query USING 
    p_display_name, 
    p_status_order, 
    p_color, 
    p_icon, 
    p_status_id;
  
  RETURN FOUND;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_personas_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_social_profiles(target_account_id uuid, target_user_id uuid DEFAULT auth.uid())
 RETURNS TABLE(id uuid, title text, "refId" text, "profileKey" text, "messagingActive" boolean, permissions jsonb, is_shared boolean, created_at timestamp with time zone, updated_at timestamp with time zone)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  -- Check if user has access to the account
  IF NOT public.has_role_on_account(target_account_id) THEN
    RAISE EXCEPTION 'Access denied to account';
  END IF;

  RETURN QUERY
  SELECT 
    aup.id,
    aup.title,
    aup."refId",
    aup."profileKey",
    aup."messagingActive",
    aup.permissions,
    aup.is_shared,
    aup.created_at,
    aup.updated_at
  FROM public.ayrshare_user_profile aup
  WHERE aup.company_id = target_account_id 
    AND (
      aup.user_id = target_user_id OR 
      (aup.is_shared = true AND public.has_role_on_account(target_account_id))
    )
  ORDER BY aup.created_at DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.toggle_social_profile_sharing(profile_id uuid, share_with_team boolean DEFAULT true)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  profile_owner UUID;
  profile_account UUID;
BEGIN
  -- Get profile owner and account
  SELECT user_id, company_id INTO profile_owner, profile_account
  FROM public.ayrshare_user_profile
  WHERE id = profile_id;

  -- Check if current user is the profile owner
  IF profile_owner != auth.uid() THEN
    RAISE EXCEPTION 'Only profile owner can change sharing settings';
  END IF;

  -- Check if user has role on the account
  IF NOT public.has_role_on_account(profile_account) THEN
    RAISE EXCEPTION 'Access denied to account';
  END IF;

  -- Update sharing status
  UPDATE public.ayrshare_user_profile
  SET 
    is_shared = share_with_team,
    updated_at = now()
  WHERE id = profile_id;

  RETURN share_with_team;
END;
$function$
;

grant delete on table "public"."ayrshare_user_profile" to "anon";

grant insert on table "public"."ayrshare_user_profile" to "anon";

grant references on table "public"."ayrshare_user_profile" to "anon";

grant select on table "public"."ayrshare_user_profile" to "anon";

grant trigger on table "public"."ayrshare_user_profile" to "anon";

grant truncate on table "public"."ayrshare_user_profile" to "anon";

grant update on table "public"."ayrshare_user_profile" to "anon";

grant delete on table "public"."ayrshare_user_profile" to "authenticated";

grant insert on table "public"."ayrshare_user_profile" to "authenticated";

grant references on table "public"."ayrshare_user_profile" to "authenticated";

grant select on table "public"."ayrshare_user_profile" to "authenticated";

grant trigger on table "public"."ayrshare_user_profile" to "authenticated";

grant truncate on table "public"."ayrshare_user_profile" to "authenticated";

grant update on table "public"."ayrshare_user_profile" to "authenticated";

grant delete on table "public"."ayrshare_user_profile" to "service_role";

grant insert on table "public"."ayrshare_user_profile" to "service_role";

grant references on table "public"."ayrshare_user_profile" to "service_role";

grant select on table "public"."ayrshare_user_profile" to "service_role";

grant trigger on table "public"."ayrshare_user_profile" to "service_role";

grant truncate on table "public"."ayrshare_user_profile" to "service_role";

grant update on table "public"."ayrshare_user_profile" to "service_role";

grant delete on table "public"."campaign_ideas" to "anon";

grant insert on table "public"."campaign_ideas" to "anon";

grant references on table "public"."campaign_ideas" to "anon";

grant select on table "public"."campaign_ideas" to "anon";

grant trigger on table "public"."campaign_ideas" to "anon";

grant truncate on table "public"."campaign_ideas" to "anon";

grant update on table "public"."campaign_ideas" to "anon";

grant delete on table "public"."campaign_ideas" to "authenticated";

grant insert on table "public"."campaign_ideas" to "authenticated";

grant references on table "public"."campaign_ideas" to "authenticated";

grant select on table "public"."campaign_ideas" to "authenticated";

grant trigger on table "public"."campaign_ideas" to "authenticated";

grant truncate on table "public"."campaign_ideas" to "authenticated";

grant update on table "public"."campaign_ideas" to "authenticated";

grant delete on table "public"."campaign_ideas" to "service_role";

grant insert on table "public"."campaign_ideas" to "service_role";

grant references on table "public"."campaign_ideas" to "service_role";

grant select on table "public"."campaign_ideas" to "service_role";

grant trigger on table "public"."campaign_ideas" to "service_role";

grant truncate on table "public"."campaign_ideas" to "service_role";

grant update on table "public"."campaign_ideas" to "service_role";

grant delete on table "public"."campaign_personas" to "anon";

grant insert on table "public"."campaign_personas" to "anon";

grant references on table "public"."campaign_personas" to "anon";

grant select on table "public"."campaign_personas" to "anon";

grant trigger on table "public"."campaign_personas" to "anon";

grant truncate on table "public"."campaign_personas" to "anon";

grant update on table "public"."campaign_personas" to "anon";

grant delete on table "public"."campaign_personas" to "authenticated";

grant insert on table "public"."campaign_personas" to "authenticated";

grant references on table "public"."campaign_personas" to "authenticated";

grant select on table "public"."campaign_personas" to "authenticated";

grant trigger on table "public"."campaign_personas" to "authenticated";

grant truncate on table "public"."campaign_personas" to "authenticated";

grant update on table "public"."campaign_personas" to "authenticated";

grant delete on table "public"."campaign_personas" to "service_role";

grant insert on table "public"."campaign_personas" to "service_role";

grant references on table "public"."campaign_personas" to "service_role";

grant select on table "public"."campaign_personas" to "service_role";

grant trigger on table "public"."campaign_personas" to "service_role";

grant truncate on table "public"."campaign_personas" to "service_role";

grant update on table "public"."campaign_personas" to "service_role";

grant delete on table "public"."campaign_templates" to "anon";

grant insert on table "public"."campaign_templates" to "anon";

grant references on table "public"."campaign_templates" to "anon";

grant select on table "public"."campaign_templates" to "anon";

grant trigger on table "public"."campaign_templates" to "anon";

grant truncate on table "public"."campaign_templates" to "anon";

grant update on table "public"."campaign_templates" to "anon";

grant delete on table "public"."campaign_templates" to "authenticated";

grant insert on table "public"."campaign_templates" to "authenticated";

grant references on table "public"."campaign_templates" to "authenticated";

grant select on table "public"."campaign_templates" to "authenticated";

grant trigger on table "public"."campaign_templates" to "authenticated";

grant truncate on table "public"."campaign_templates" to "authenticated";

grant update on table "public"."campaign_templates" to "authenticated";

grant delete on table "public"."campaign_templates" to "service_role";

grant insert on table "public"."campaign_templates" to "service_role";

grant references on table "public"."campaign_templates" to "service_role";

grant select on table "public"."campaign_templates" to "service_role";

grant trigger on table "public"."campaign_templates" to "service_role";

grant truncate on table "public"."campaign_templates" to "service_role";

grant update on table "public"."campaign_templates" to "service_role";

grant delete on table "public"."company_brand" to "anon";

grant insert on table "public"."company_brand" to "anon";

grant references on table "public"."company_brand" to "anon";

grant select on table "public"."company_brand" to "anon";

grant trigger on table "public"."company_brand" to "anon";

grant truncate on table "public"."company_brand" to "anon";

grant update on table "public"."company_brand" to "anon";

grant delete on table "public"."company_brand" to "authenticated";

grant insert on table "public"."company_brand" to "authenticated";

grant references on table "public"."company_brand" to "authenticated";

grant select on table "public"."company_brand" to "authenticated";

grant trigger on table "public"."company_brand" to "authenticated";

grant truncate on table "public"."company_brand" to "authenticated";

grant update on table "public"."company_brand" to "authenticated";

grant delete on table "public"."company_brand" to "service_role";

grant insert on table "public"."company_brand" to "service_role";

grant references on table "public"."company_brand" to "service_role";

grant select on table "public"."company_brand" to "service_role";

grant trigger on table "public"."company_brand" to "service_role";

grant truncate on table "public"."company_brand" to "service_role";

grant update on table "public"."company_brand" to "service_role";

grant delete on table "public"."company_campaigns" to "anon";

grant insert on table "public"."company_campaigns" to "anon";

grant references on table "public"."company_campaigns" to "anon";

grant select on table "public"."company_campaigns" to "anon";

grant trigger on table "public"."company_campaigns" to "anon";

grant truncate on table "public"."company_campaigns" to "anon";

grant update on table "public"."company_campaigns" to "anon";

grant delete on table "public"."company_campaigns" to "authenticated";

grant insert on table "public"."company_campaigns" to "authenticated";

grant references on table "public"."company_campaigns" to "authenticated";

grant select on table "public"."company_campaigns" to "authenticated";

grant trigger on table "public"."company_campaigns" to "authenticated";

grant truncate on table "public"."company_campaigns" to "authenticated";

grant update on table "public"."company_campaigns" to "authenticated";

grant delete on table "public"."company_campaigns" to "service_role";

grant insert on table "public"."company_campaigns" to "service_role";

grant references on table "public"."company_campaigns" to "service_role";

grant select on table "public"."company_campaigns" to "service_role";

grant trigger on table "public"."company_campaigns" to "service_role";

grant truncate on table "public"."company_campaigns" to "service_role";

grant update on table "public"."company_campaigns" to "service_role";

grant delete on table "public"."company_content" to "anon";

grant insert on table "public"."company_content" to "anon";

grant references on table "public"."company_content" to "anon";

grant select on table "public"."company_content" to "anon";

grant trigger on table "public"."company_content" to "anon";

grant truncate on table "public"."company_content" to "anon";

grant update on table "public"."company_content" to "anon";

grant delete on table "public"."company_content" to "authenticated";

grant insert on table "public"."company_content" to "authenticated";

grant references on table "public"."company_content" to "authenticated";

grant select on table "public"."company_content" to "authenticated";

grant trigger on table "public"."company_content" to "authenticated";

grant truncate on table "public"."company_content" to "authenticated";

grant update on table "public"."company_content" to "authenticated";

grant delete on table "public"."company_content" to "service_role";

grant insert on table "public"."company_content" to "service_role";

grant references on table "public"."company_content" to "service_role";

grant select on table "public"."company_content" to "service_role";

grant trigger on table "public"."company_content" to "service_role";

grant truncate on table "public"."company_content" to "service_role";

grant update on table "public"."company_content" to "service_role";

grant delete on table "public"."company_task_statuses" to "anon";

grant insert on table "public"."company_task_statuses" to "anon";

grant references on table "public"."company_task_statuses" to "anon";

grant select on table "public"."company_task_statuses" to "anon";

grant trigger on table "public"."company_task_statuses" to "anon";

grant truncate on table "public"."company_task_statuses" to "anon";

grant update on table "public"."company_task_statuses" to "anon";

grant delete on table "public"."company_task_statuses" to "authenticated";

grant insert on table "public"."company_task_statuses" to "authenticated";

grant references on table "public"."company_task_statuses" to "authenticated";

grant select on table "public"."company_task_statuses" to "authenticated";

grant trigger on table "public"."company_task_statuses" to "authenticated";

grant truncate on table "public"."company_task_statuses" to "authenticated";

grant update on table "public"."company_task_statuses" to "authenticated";

grant delete on table "public"."company_task_statuses" to "service_role";

grant insert on table "public"."company_task_statuses" to "service_role";

grant references on table "public"."company_task_statuses" to "service_role";

grant select on table "public"."company_task_statuses" to "service_role";

grant trigger on table "public"."company_task_statuses" to "service_role";

grant truncate on table "public"."company_task_statuses" to "service_role";

grant update on table "public"."company_task_statuses" to "service_role";

grant delete on table "public"."content_personas" to "anon";

grant insert on table "public"."content_personas" to "anon";

grant references on table "public"."content_personas" to "anon";

grant select on table "public"."content_personas" to "anon";

grant trigger on table "public"."content_personas" to "anon";

grant truncate on table "public"."content_personas" to "anon";

grant update on table "public"."content_personas" to "anon";

grant delete on table "public"."content_personas" to "authenticated";

grant insert on table "public"."content_personas" to "authenticated";

grant references on table "public"."content_personas" to "authenticated";

grant select on table "public"."content_personas" to "authenticated";

grant trigger on table "public"."content_personas" to "authenticated";

grant truncate on table "public"."content_personas" to "authenticated";

grant update on table "public"."content_personas" to "authenticated";

grant delete on table "public"."content_personas" to "service_role";

grant insert on table "public"."content_personas" to "service_role";

grant references on table "public"."content_personas" to "service_role";

grant select on table "public"."content_personas" to "service_role";

grant trigger on table "public"."content_personas" to "service_role";

grant truncate on table "public"."content_personas" to "service_role";

grant update on table "public"."content_personas" to "service_role";

grant delete on table "public"."feature_usage" to "anon";

grant insert on table "public"."feature_usage" to "anon";

grant references on table "public"."feature_usage" to "anon";

grant select on table "public"."feature_usage" to "anon";

grant trigger on table "public"."feature_usage" to "anon";

grant truncate on table "public"."feature_usage" to "anon";

grant update on table "public"."feature_usage" to "anon";

grant delete on table "public"."feature_usage" to "authenticated";

grant insert on table "public"."feature_usage" to "authenticated";

grant references on table "public"."feature_usage" to "authenticated";

grant select on table "public"."feature_usage" to "authenticated";

grant trigger on table "public"."feature_usage" to "authenticated";

grant truncate on table "public"."feature_usage" to "authenticated";

grant update on table "public"."feature_usage" to "authenticated";

grant delete on table "public"."feature_usage" to "service_role";

grant insert on table "public"."feature_usage" to "service_role";

grant references on table "public"."feature_usage" to "service_role";

grant select on table "public"."feature_usage" to "service_role";

grant trigger on table "public"."feature_usage" to "service_role";

grant truncate on table "public"."feature_usage" to "service_role";

grant update on table "public"."feature_usage" to "service_role";

grant delete on table "public"."generated_research" to "anon";

grant insert on table "public"."generated_research" to "anon";

grant references on table "public"."generated_research" to "anon";

grant select on table "public"."generated_research" to "anon";

grant trigger on table "public"."generated_research" to "anon";

grant truncate on table "public"."generated_research" to "anon";

grant update on table "public"."generated_research" to "anon";

grant delete on table "public"."generated_research" to "authenticated";

grant insert on table "public"."generated_research" to "authenticated";

grant references on table "public"."generated_research" to "authenticated";

grant select on table "public"."generated_research" to "authenticated";

grant trigger on table "public"."generated_research" to "authenticated";

grant truncate on table "public"."generated_research" to "authenticated";

grant update on table "public"."generated_research" to "authenticated";

grant delete on table "public"."generated_research" to "service_role";

grant insert on table "public"."generated_research" to "service_role";

grant references on table "public"."generated_research" to "service_role";

grant select on table "public"."generated_research" to "service_role";

grant trigger on table "public"."generated_research" to "service_role";

grant truncate on table "public"."generated_research" to "service_role";

grant update on table "public"."generated_research" to "service_role";

grant delete on table "public"."linkedInState" to "anon";

grant insert on table "public"."linkedInState" to "anon";

grant references on table "public"."linkedInState" to "anon";

grant select on table "public"."linkedInState" to "anon";

grant trigger on table "public"."linkedInState" to "anon";

grant truncate on table "public"."linkedInState" to "anon";

grant update on table "public"."linkedInState" to "anon";

grant delete on table "public"."linkedInState" to "authenticated";

grant insert on table "public"."linkedInState" to "authenticated";

grant references on table "public"."linkedInState" to "authenticated";

grant select on table "public"."linkedInState" to "authenticated";

grant trigger on table "public"."linkedInState" to "authenticated";

grant truncate on table "public"."linkedInState" to "authenticated";

grant update on table "public"."linkedInState" to "authenticated";

grant delete on table "public"."linkedInState" to "service_role";

grant insert on table "public"."linkedInState" to "service_role";

grant references on table "public"."linkedInState" to "service_role";

grant select on table "public"."linkedInState" to "service_role";

grant trigger on table "public"."linkedInState" to "service_role";

grant truncate on table "public"."linkedInState" to "service_role";

grant update on table "public"."linkedInState" to "service_role";

grant delete on table "public"."onboarding" to "anon";

grant insert on table "public"."onboarding" to "anon";

grant references on table "public"."onboarding" to "anon";

grant select on table "public"."onboarding" to "anon";

grant trigger on table "public"."onboarding" to "anon";

grant truncate on table "public"."onboarding" to "anon";

grant update on table "public"."onboarding" to "anon";

grant delete on table "public"."onboarding" to "authenticated";

grant insert on table "public"."onboarding" to "authenticated";

grant references on table "public"."onboarding" to "authenticated";

grant select on table "public"."onboarding" to "authenticated";

grant trigger on table "public"."onboarding" to "authenticated";

grant truncate on table "public"."onboarding" to "authenticated";

grant update on table "public"."onboarding" to "authenticated";

grant delete on table "public"."onboarding" to "service_role";

grant select on table "public"."onboarding" to "service_role";

grant delete on table "public"."personas" to "anon";

grant insert on table "public"."personas" to "anon";

grant references on table "public"."personas" to "anon";

grant select on table "public"."personas" to "anon";

grant trigger on table "public"."personas" to "anon";

grant truncate on table "public"."personas" to "anon";

grant update on table "public"."personas" to "anon";

grant delete on table "public"."personas" to "authenticated";

grant insert on table "public"."personas" to "authenticated";

grant references on table "public"."personas" to "authenticated";

grant select on table "public"."personas" to "authenticated";

grant trigger on table "public"."personas" to "authenticated";

grant truncate on table "public"."personas" to "authenticated";

grant update on table "public"."personas" to "authenticated";

grant delete on table "public"."personas" to "service_role";

grant insert on table "public"."personas" to "service_role";

grant references on table "public"."personas" to "service_role";

grant select on table "public"."personas" to "service_role";

grant trigger on table "public"."personas" to "service_role";

grant truncate on table "public"."personas" to "service_role";

grant update on table "public"."personas" to "service_role";

grant delete on table "public"."product_documents" to "anon";

grant insert on table "public"."product_documents" to "anon";

grant references on table "public"."product_documents" to "anon";

grant select on table "public"."product_documents" to "anon";

grant trigger on table "public"."product_documents" to "anon";

grant truncate on table "public"."product_documents" to "anon";

grant update on table "public"."product_documents" to "anon";

grant delete on table "public"."product_documents" to "authenticated";

grant insert on table "public"."product_documents" to "authenticated";

grant references on table "public"."product_documents" to "authenticated";

grant select on table "public"."product_documents" to "authenticated";

grant trigger on table "public"."product_documents" to "authenticated";

grant truncate on table "public"."product_documents" to "authenticated";

grant update on table "public"."product_documents" to "authenticated";

grant delete on table "public"."product_documents" to "service_role";

grant insert on table "public"."product_documents" to "service_role";

grant references on table "public"."product_documents" to "service_role";

grant select on table "public"."product_documents" to "service_role";

grant trigger on table "public"."product_documents" to "service_role";

grant truncate on table "public"."product_documents" to "service_role";

grant update on table "public"."product_documents" to "service_role";

grant delete on table "public"."saved_research" to "anon";

grant insert on table "public"."saved_research" to "anon";

grant references on table "public"."saved_research" to "anon";

grant select on table "public"."saved_research" to "anon";

grant trigger on table "public"."saved_research" to "anon";

grant truncate on table "public"."saved_research" to "anon";

grant update on table "public"."saved_research" to "anon";

grant delete on table "public"."saved_research" to "authenticated";

grant insert on table "public"."saved_research" to "authenticated";

grant references on table "public"."saved_research" to "authenticated";

grant select on table "public"."saved_research" to "authenticated";

grant trigger on table "public"."saved_research" to "authenticated";

grant truncate on table "public"."saved_research" to "authenticated";

grant update on table "public"."saved_research" to "authenticated";

grant delete on table "public"."saved_research" to "service_role";

grant insert on table "public"."saved_research" to "service_role";

grant references on table "public"."saved_research" to "service_role";

grant select on table "public"."saved_research" to "service_role";

grant trigger on table "public"."saved_research" to "service_role";

grant truncate on table "public"."saved_research" to "service_role";

grant update on table "public"."saved_research" to "service_role";

grant delete on table "public"."twitterState" to "anon";

grant insert on table "public"."twitterState" to "anon";

grant references on table "public"."twitterState" to "anon";

grant select on table "public"."twitterState" to "anon";

grant trigger on table "public"."twitterState" to "anon";

grant truncate on table "public"."twitterState" to "anon";

grant update on table "public"."twitterState" to "anon";

grant delete on table "public"."twitterState" to "authenticated";

grant insert on table "public"."twitterState" to "authenticated";

grant references on table "public"."twitterState" to "authenticated";

grant select on table "public"."twitterState" to "authenticated";

grant trigger on table "public"."twitterState" to "authenticated";

grant truncate on table "public"."twitterState" to "authenticated";

grant update on table "public"."twitterState" to "authenticated";

grant delete on table "public"."twitterState" to "service_role";

grant insert on table "public"."twitterState" to "service_role";

grant references on table "public"."twitterState" to "service_role";

grant select on table "public"."twitterState" to "service_role";

grant trigger on table "public"."twitterState" to "service_role";

grant truncate on table "public"."twitterState" to "service_role";

grant update on table "public"."twitterState" to "service_role";

grant delete on table "public"."user_cache" to "anon";

grant insert on table "public"."user_cache" to "anon";

grant references on table "public"."user_cache" to "anon";

grant select on table "public"."user_cache" to "anon";

grant trigger on table "public"."user_cache" to "anon";

grant truncate on table "public"."user_cache" to "anon";

grant update on table "public"."user_cache" to "anon";

grant delete on table "public"."user_cache" to "authenticated";

grant insert on table "public"."user_cache" to "authenticated";

grant references on table "public"."user_cache" to "authenticated";

grant select on table "public"."user_cache" to "authenticated";

grant trigger on table "public"."user_cache" to "authenticated";

grant truncate on table "public"."user_cache" to "authenticated";

grant update on table "public"."user_cache" to "authenticated";

grant delete on table "public"."user_cache" to "service_role";

grant insert on table "public"."user_cache" to "service_role";

grant references on table "public"."user_cache" to "service_role";

grant select on table "public"."user_cache" to "service_role";

grant trigger on table "public"."user_cache" to "service_role";

grant truncate on table "public"."user_cache" to "service_role";

grant update on table "public"."user_cache" to "service_role";

create policy "ayrshare_user_profile_delete"
on "public"."ayrshare_user_profile"
as permissive
for delete
to authenticated
using ((user_id = auth.uid()));


create policy "ayrshare_user_profile_insert"
on "public"."ayrshare_user_profile"
as permissive
for insert
to authenticated
with check ((user_id = auth.uid()));


create policy "ayrshare_user_profile_select"
on "public"."ayrshare_user_profile"
as permissive
for select
to authenticated
using ((user_id = auth.uid()));


create policy "ayrshare_user_profile_update"
on "public"."ayrshare_user_profile"
as permissive
for update
to authenticated
using ((user_id = auth.uid()))
with check ((user_id = auth.uid()));


create policy "team_members_can_insert_campaign_ideas"
on "public"."campaign_ideas"
as permissive
for insert
to public
with check (has_role_on_account(company_id));


create policy "team_members_can_select_campaign_ideas"
on "public"."campaign_ideas"
as permissive
for select
to public
using (has_role_on_account(company_id));


create policy "team_members_can_update_campaign_ideas"
on "public"."campaign_ideas"
as permissive
for update
to public
using (has_role_on_account(company_id))
with check (has_role_on_account(company_id));


create policy "Enable read access for all users"
on "public"."campaign_templates"
as permissive
for select
to public
using (true);


create policy "Allow brand deletion for owners in the same company"
on "public"."company_brand"
as permissive
for delete
to authenticated
using (( SELECT (EXISTS ( SELECT 1
           FROM accounts_memberships
          WHERE ((accounts_memberships.account_id = company_brand.company_id) AND (accounts_memberships.user_id = auth.uid()) AND ((accounts_memberships.account_role)::text = 'owner'::text)))) AS "exists"));


create policy "team_members_can_insert_company_brand"
on "public"."company_brand"
as permissive
for insert
to public
with check (has_role_on_account(company_id));


create policy "team_members_can_select_company_brand"
on "public"."company_brand"
as permissive
for select
to public
using (has_role_on_account(company_id));


create policy "team_members_can_update_company_brand"
on "public"."company_brand"
as permissive
for update
to public
using (has_role_on_account(company_id))
with check (has_role_on_account(company_id));


create policy "team_members_can_delete_company_campaigns"
on "public"."company_campaigns"
as permissive
for delete
to public
using (has_role_on_account(company_id));


create policy "team_members_can_insert_company_campaigns"
on "public"."company_campaigns"
as permissive
for insert
to public
with check (has_role_on_account(company_id));


create policy "team_members_can_select_company_campaigns"
on "public"."company_campaigns"
as permissive
for select
to public
using (has_role_on_account(company_id));


create policy "team_members_can_update_company_campaigns"
on "public"."company_campaigns"
as permissive
for update
to public
using (has_role_on_account(company_id))
with check (has_role_on_account(company_id));


create policy "Enable delete for users based on user_id"
on "public"."company_content"
as permissive
for delete
to public
using (has_role_on_account(company_id));


create policy "team_members_can_insert_company_content"
on "public"."company_content"
as permissive
for insert
to public
with check (has_role_on_account(company_id));


create policy "team_members_can_select_company_content"
on "public"."company_content"
as permissive
for select
to public
using (has_role_on_account(company_id));


create policy "team_members_can_update_company_content"
on "public"."company_content"
as permissive
for update
to public
using (has_role_on_account(company_id))
with check (has_role_on_account(company_id));


create policy "team_members_can_delete_company_task_statuses"
on "public"."company_task_statuses"
as permissive
for delete
to public
using (has_role_on_account(company_id));


create policy "team_members_can_insert_company_task_statuses"
on "public"."company_task_statuses"
as permissive
for insert
to public
with check (has_role_on_account(company_id));


create policy "team_members_can_select_company_task_statuses"
on "public"."company_task_statuses"
as permissive
for select
to public
using (has_role_on_account(company_id));


create policy "team_members_can_update_company_task_statuses"
on "public"."company_task_statuses"
as permissive
for update
to public
using (has_role_on_account(company_id))
with check (has_role_on_account(company_id));


create policy "select_feature_usage"
on "public"."feature_usage"
as permissive
for select
to authenticated
using ((has_role_on_account(account_id) OR (( SELECT auth.uid() AS uid) = account_id)));


create policy "generated_research_delete"
on "public"."generated_research"
as permissive
for delete
to authenticated
using (has_role_on_account(account_id));


create policy "generated_research_insert"
on "public"."generated_research"
as permissive
for insert
to authenticated
with check (has_role_on_account(account_id));


create policy "generated_research_read"
on "public"."generated_research"
as permissive
for select
to authenticated
using (has_role_on_account(account_id));


create policy "generated_research_update"
on "public"."generated_research"
as permissive
for update
to authenticated
using (has_role_on_account(account_id));


create policy "Enable delete for users based on user_id"
on "public"."linkedInState"
as permissive
for delete
to public
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable insert for users based on user_id"
on "public"."linkedInState"
as permissive
for insert
to public
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to update their own data only"
on "public"."linkedInState"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to view their own data only"
on "public"."linkedInState"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "insert_onboarding"
on "public"."onboarding"
as permissive
for insert
to authenticated
with check ((account_id = ( SELECT auth.uid() AS uid)));


create policy "read_onboarding"
on "public"."onboarding"
as permissive
for select
to authenticated
using ((account_id = ( SELECT auth.uid() AS uid)));


create policy "update_onboarding"
on "public"."onboarding"
as permissive
for update
to authenticated
using ((account_id = ( SELECT auth.uid() AS uid)))
with check ((account_id = ( SELECT auth.uid() AS uid)));


create policy "saved_research_delete"
on "public"."saved_research"
as permissive
for delete
to authenticated
using (has_role_on_account(account_id));


create policy "saved_research_insert"
on "public"."saved_research"
as permissive
for insert
to authenticated
with check (has_role_on_account(account_id));


create policy "saved_research_read"
on "public"."saved_research"
as permissive
for select
to authenticated
using (has_role_on_account(account_id));


create policy "Enable delete for users based on user_id"
on "public"."twitterState"
as permissive
for delete
to public
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable insert for users based on user_id"
on "public"."twitterState"
as permissive
for insert
to public
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to update their own data only"
on "public"."twitterState"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to view their own data only"
on "public"."twitterState"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "invitations_read_self"
on "public"."invitations"
as permissive
for select
to authenticated
using (true);



CREATE TRIGGER create_feature_usage_row AFTER INSERT ON public.accounts FOR EACH ROW EXECUTE FUNCTION create_feature_usage_row();

CREATE TRIGGER init_task_statuses_trigger AFTER INSERT ON public.accounts FOR EACH ROW WHEN ((NOT new.is_personal_account)) EXECUTE FUNCTION init_task_statuses_for_new_company();

CREATE TRIGGER ayrshare_user_profile_set_timestamps BEFORE UPDATE ON public.ayrshare_user_profile FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();

CREATE TRIGGER set_generated_research_timestamps BEFORE INSERT OR UPDATE ON public.generated_research FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();

CREATE TRIGGER set_generated_research_user_tracking BEFORE INSERT OR UPDATE ON public.generated_research FOR EACH ROW EXECUTE FUNCTION trigger_set_user_tracking();


CREATE TRIGGER update_personas_updated_at BEFORE UPDATE ON public.personas FOR EACH ROW EXECUTE FUNCTION update_personas_updated_at();

CREATE TRIGGER saved_research_set_timestamps BEFORE UPDATE ON public.saved_research FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();


