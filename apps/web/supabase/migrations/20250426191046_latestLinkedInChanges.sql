drop policy "invitations_read_self" on "public"."invitations";

create table "public"."linkedInState" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "state" character varying,
    "access_token" character varying,
    "refresh_token" character varying,
    "scope" character varying,
    "user_id" uuid default auth.uid(),
    "first_name" character varying,
    "last_name" character varying,
    "profile_picture_url" character varying,
    "headline" character varying,
    "updated_at" timestamp without time zone,
    "expires_in" timestamp without time zone,
    "refresh_token_expires_in" timestamp without time zone,
    "person_urn" character varying
);


CREATE UNIQUE INDEX "linkedInState_pkey" ON public."linkedInState" USING btree (id);

CREATE UNIQUE INDEX "linkedInState_user_id_key" ON public."linkedInState" USING btree (user_id);

alter table "public"."linkedInState" add constraint "linkedInState_pkey" PRIMARY KEY using index "linkedInState_pkey";

alter table "public"."linkedInState" add constraint "linkedInState_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."linkedInState" validate constraint "linkedInState_user_id_fkey";

alter table "public"."linkedInState" add constraint "linkedInState_user_id_key" UNIQUE using index "linkedInState_user_id_key";

grant delete on table "public"."linkedInState" to "anon";

grant insert on table "public"."linkedInState" to "anon";

grant references on table "public"."linkedInState" to "anon";

grant select on table "public"."linkedInState" to "anon";

grant trigger on table "public"."linkedInState" to "anon";

grant truncate on table "public"."linkedInState" to "anon";

grant update on table "public"."linkedInState" to "anon";

grant delete on table "public"."linkedInState" to "authenticated";

grant insert on table "public"."linkedInState" to "authenticated";

grant references on table "public"."linkedInState" to "authenticated";

grant select on table "public"."linkedInState" to "authenticated";

grant trigger on table "public"."linkedInState" to "authenticated";

grant truncate on table "public"."linkedInState" to "authenticated";

grant update on table "public"."linkedInState" to "authenticated";

grant delete on table "public"."linkedInState" to "service_role";

grant insert on table "public"."linkedInState" to "service_role";

grant references on table "public"."linkedInState" to "service_role";

grant select on table "public"."linkedInState" to "service_role";

grant trigger on table "public"."linkedInState" to "service_role";

grant truncate on table "public"."linkedInState" to "service_role";

grant update on table "public"."linkedInState" to "service_role";

create policy "Allow brand deletion for owners in the same company"
on "public"."company_brand"
as permissive
for delete
to authenticated
using (( SELECT (EXISTS ( SELECT 1
           FROM accounts_memberships
          WHERE ((accounts_memberships.account_id = company_brand.company_id) AND (accounts_memberships.user_id = auth.uid()) AND ((accounts_memberships.account_role)::text = 'owner'::text)))) AS "exists"));


create policy "Enable delete for users based on user_id"
on "public"."linkedInState"
as permissive
for delete
to public
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable insert for users based on user_id"
on "public"."linkedInState"
as permissive
for insert
to public
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to update their own data only"
on "public"."linkedInState"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to view their own data only"
on "public"."linkedInState"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "invitations_read_self"
on "public"."invitations"
as permissive
for select
to authenticated
using (true);
