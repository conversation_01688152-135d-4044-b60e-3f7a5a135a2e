create table "public"."product_documents" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "title" text not null,
    "file_path" text not null,
    "company_id" uuid not null,
    "content" text,
    "file_type" text not null
);


grant delete on table "public"."product_documents" to "anon";

grant insert on table "public"."product_documents" to "anon";

grant references on table "public"."product_documents" to "anon";

grant select on table "public"."product_documents" to "anon";

grant trigger on table "public"."product_documents" to "anon";

grant truncate on table "public"."product_documents" to "anon";

grant update on table "public"."product_documents" to "anon";

grant delete on table "public"."product_documents" to "authenticated";

grant insert on table "public"."product_documents" to "authenticated";

grant references on table "public"."product_documents" to "authenticated";

grant select on table "public"."product_documents" to "authenticated";

grant trigger on table "public"."product_documents" to "authenticated";

grant truncate on table "public"."product_documents" to "authenticated";

grant update on table "public"."product_documents" to "authenticated";

grant delete on table "public"."product_documents" to "service_role";

grant insert on table "public"."product_documents" to "service_role";

grant references on table "public"."product_documents" to "service_role";

grant select on table "public"."product_documents" to "service_role";

grant trigger on table "public"."product_documents" to "service_role";

grant truncate on table "public"."product_documents" to "service_role";

grant update on table "public"."product_documents" to "service_role";

