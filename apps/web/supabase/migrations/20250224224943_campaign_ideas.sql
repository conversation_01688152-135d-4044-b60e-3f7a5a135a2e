create table "public"."campaign_ideas" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "company_id" uuid not null,
    "campaign_id" uuid not null,
    "content" text not null,
    "is_selected" boolean default false,
    "metadata" jsonb,
    "brief" jsonb,
    "content_types" jsonb,
    "languages" jsonb
);

-- RLS policies for campaign_ideas table
-- SELECT policy: Allow team members to select campaign_ideas records
create policy "team_members_can_select_campaign_ideas" on public.campaign_ideas
for select using (
  public.has_role_on_account(company_id)
);

-- INSERT policy: Allow team members to insert campaign_ideas records
create policy "team_members_can_insert_campaign_ideas" on public.campaign_ideas
for insert with check (
  public.has_role_on_account(company_id)
);

-- UPDATE policy: Allow team members to update campaign_ideas records
create policy "team_members_can_update_campaign_ideas" on public.campaign_ideas
for update using (
  public.has_role_on_account(company_id)
) with check (
  public.has_role_on_account(company_id)
);

alter table "public"."campaign_ideas" enable row level security;

CREATE UNIQUE INDEX campaign_ideas_pkey ON public.campaign_ideas USING btree (id);

alter table "public"."campaign_ideas" add constraint "campaign_ideas_pkey" PRIMARY KEY using index "campaign_ideas_pkey";

alter table "public"."campaign_ideas" add constraint "campaign_ideas_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES company_campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."campaign_ideas" validate constraint "campaign_ideas_campaign_id_fkey";

grant delete on table "public"."campaign_ideas" to "anon";

grant insert on table "public"."campaign_ideas" to "anon";

grant references on table "public"."campaign_ideas" to "anon";

grant select on table "public"."campaign_ideas" to "anon";

grant trigger on table "public"."campaign_ideas" to "anon";

grant truncate on table "public"."campaign_ideas" to "anon";

grant update on table "public"."campaign_ideas" to "anon";

grant delete on table "public"."campaign_ideas" to "authenticated";

grant insert on table "public"."campaign_ideas" to "authenticated";

grant references on table "public"."campaign_ideas" to "authenticated";

grant select on table "public"."campaign_ideas" to "authenticated";

grant trigger on table "public"."campaign_ideas" to "authenticated";

grant truncate on table "public"."campaign_ideas" to "authenticated";

grant update on table "public"."campaign_ideas" to "authenticated";

grant delete on table "public"."campaign_ideas" to "service_role";

grant insert on table "public"."campaign_ideas" to "service_role";

grant references on table "public"."campaign_ideas" to "service_role";

grant select on table "public"."campaign_ideas" to "service_role";

grant trigger on table "public"."campaign_ideas" to "service_role";

grant truncate on table "public"."campaign_ideas" to "service_role";

grant update on table "public"."campaign_ideas" to "service_role";


