revoke insert on table "public"."onboarding" from "service_role";

revoke references on table "public"."onboarding" from "service_role";

revoke trigger on table "public"."onboarding" from "service_role";

revoke truncate on table "public"."onboarding" from "service_role";

revoke update on table "public"."onboarding" from "service_role";

create table "public"."generated_research" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "icp_id" uuid not null,
    "persona_id" uuid not null,
    "research_type" text not null,
    "time_filter" text not null,
    "title" text not null,
    "results" jsonb not null default '[]'::jsonb,
    "content_suggestions" jsonb not null default '[]'::jsonb,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "created_by" uuid,
    "updated_by" uuid,
    "topic" text
);


alter table "public"."generated_research" enable row level security;

alter table "public"."ayrshare_user_profile" add column "description" text;

alter table "public"."ayrshare_user_profile" add column "is_active" boolean not null default true;

alter table "public"."ayrshare_user_profile" add column "profile_name" character varying(255) not null default 'Default Profile'::character varying;

alter table "public"."ayrshare_user_profile" enable row level security;

alter table "public"."saved_research" add column "topic" text;

CREATE INDEX ayrshare_user_profile_company_id_idx ON public.ayrshare_user_profile USING btree (company_id);

CREATE INDEX ayrshare_user_profile_is_active_idx ON public.ayrshare_user_profile USING btree (is_active);

CREATE UNIQUE INDEX ayrshare_user_profile_user_company_name_idx ON public.ayrshare_user_profile USING btree (user_id, company_id, profile_name);

CREATE INDEX ayrshare_user_profile_user_id_idx ON public.ayrshare_user_profile USING btree (user_id);

CREATE UNIQUE INDEX generated_research_pkey ON public.generated_research USING btree (id);

CREATE INDEX idx_generated_research_account_id ON public.generated_research USING btree (account_id);

CREATE INDEX idx_generated_research_created_at ON public.generated_research USING btree (created_at DESC);

CREATE INDEX idx_generated_research_icp_persona ON public.generated_research USING btree (icp_id, persona_id);

CREATE INDEX idx_generated_research_topic ON public.generated_research USING btree (topic);

CREATE INDEX idx_saved_research_topic ON public.saved_research USING btree (topic);

alter table "public"."generated_research" add constraint "generated_research_pkey" PRIMARY KEY using index "generated_research_pkey";

alter table "public"."generated_research" add constraint "generated_research_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."generated_research" validate constraint "generated_research_account_id_fkey";

alter table "public"."generated_research" add constraint "generated_research_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."generated_research" validate constraint "generated_research_created_by_fkey";

alter table "public"."generated_research" add constraint "generated_research_research_type_check" CHECK ((research_type = ANY (ARRAY['pain-points'::text, 'trending-topics'::text, 'recent-news'::text]))) not valid;

alter table "public"."generated_research" validate constraint "generated_research_research_type_check";

alter table "public"."generated_research" add constraint "generated_research_updated_by_fkey" FOREIGN KEY (updated_by) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."generated_research" validate constraint "generated_research_updated_by_fkey";

grant delete on table "public"."generated_research" to "anon";

grant insert on table "public"."generated_research" to "anon";

grant references on table "public"."generated_research" to "anon";

grant select on table "public"."generated_research" to "anon";

grant trigger on table "public"."generated_research" to "anon";

grant truncate on table "public"."generated_research" to "anon";

grant update on table "public"."generated_research" to "anon";

grant delete on table "public"."generated_research" to "authenticated";

grant insert on table "public"."generated_research" to "authenticated";

grant references on table "public"."generated_research" to "authenticated";

grant select on table "public"."generated_research" to "authenticated";

grant trigger on table "public"."generated_research" to "authenticated";

grant truncate on table "public"."generated_research" to "authenticated";

grant update on table "public"."generated_research" to "authenticated";

grant delete on table "public"."generated_research" to "service_role";

grant insert on table "public"."generated_research" to "service_role";

grant references on table "public"."generated_research" to "service_role";

grant select on table "public"."generated_research" to "service_role";

grant trigger on table "public"."generated_research" to "service_role";

grant truncate on table "public"."generated_research" to "service_role";

grant update on table "public"."generated_research" to "service_role";

create policy "ayrshare_user_profile_delete"
on "public"."ayrshare_user_profile"
as permissive
for delete
to authenticated
using (((user_id = auth.uid()) OR has_role_on_account(company_id, 'owner'::character varying)));


create policy "ayrshare_user_profile_insert"
on "public"."ayrshare_user_profile"
as permissive
for insert
to authenticated
with check (((user_id = auth.uid()) AND has_role_on_account(company_id)));


create policy "ayrshare_user_profile_select"
on "public"."ayrshare_user_profile"
as permissive
for select
to authenticated
using (((user_id = auth.uid()) OR has_role_on_account(company_id)));


create policy "ayrshare_user_profile_update"
on "public"."ayrshare_user_profile"
as permissive
for update
to authenticated
using (((user_id = auth.uid()) OR has_role_on_account(company_id)))
with check (((user_id = auth.uid()) AND has_role_on_account(company_id)));


create policy "generated_research_delete"
on "public"."generated_research"
as permissive
for delete
to authenticated
using (has_role_on_account(account_id));


create policy "generated_research_insert"
on "public"."generated_research"
as permissive
for insert
to authenticated
with check (has_role_on_account(account_id));


create policy "generated_research_read"
on "public"."generated_research"
as permissive
for select
to authenticated
using (has_role_on_account(account_id));


create policy "generated_research_update"
on "public"."generated_research"
as permissive
for update
to authenticated
using (has_role_on_account(account_id));


CREATE TRIGGER set_generated_research_timestamps BEFORE INSERT OR UPDATE ON public.generated_research FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();

CREATE TRIGGER set_generated_research_user_tracking BEFORE INSERT OR UPDATE ON public.generated_research FOR EACH ROW EXECUTE FUNCTION trigger_set_user_tracking();


