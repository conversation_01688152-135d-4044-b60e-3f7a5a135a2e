-- Add video editor fields to company_content table
ALTER TABLE "public"."company_content" 
  ADD COLUMN "video_editor_overlays" jsonb,
  ADD COLUMN "video_editor_aspect_ratio" jsonb,
  ADD COLUMN "video_editor_player_dimensions" jsonb;

-- Create index for video editor content for faster lookup
CREATE INDEX idx_company_content_video_editor ON public.company_content USING btree (id) 
  WHERE (video_editor_overlays IS NOT NULL);

-- Comment on columns for documentation
COMMENT ON COLUMN "public"."company_content"."video_editor_overlays" IS 'Stores the timeline overlays and their properties for the video editor';
COMMENT ON COLUMN "public"."company_content"."video_editor_aspect_ratio" IS 'Stores the aspect ratio settings for the video editor';
COMMENT ON COLUMN "public"."company_content"."video_editor_player_dimensions" IS 'Stores the player dimensions for the video editor';