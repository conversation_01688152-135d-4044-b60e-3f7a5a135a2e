alter table "public"."saved_research" add column "source_content" text;

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text, website text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    new_account public.accounts;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    insert into public.accounts(
        name,
        website,
        is_personal_account)
    values (
        account_name,
        website,
        false)
    returning * into new_account;

    return new_account;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text, website text, public_data text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    new_account public.accounts;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    insert into public.accounts(
        name,
        website,
        public_data,
        is_personal_account)
    values (
        account_name,
        website,
        public_data::jsonb,  -- cast text to jsonb here
        false)
    returning * into new_account;

    return new_account;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$declare
    new_account public.accounts;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    insert into public.accounts(
        name,
        website,
        is_personal_account)
    values (
        account_name,
        website,
        false)
returning
    * into new_account;

    return new_account;

end;$function$
;


