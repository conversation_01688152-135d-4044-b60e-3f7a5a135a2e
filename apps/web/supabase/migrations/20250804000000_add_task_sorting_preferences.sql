-- Add task sorting preferences to user_cache
-- Extend the existing task_list_columns to include sorting state
-- or add a new field for sorting preferences

-- Add a new column to store task sorting preferences separately
alter table public.user_cache add column "task_sorting_state" jsonb;

-- Add comment to explain the structure
comment on column "public"."user_cache"."task_sorting_state" is 'Stores user preferences for task table sorting in format: [{"id": "column_name", "desc": boolean}]';
comment on column "public"."user_cache"."task_list_columns" is 'Stores user preferences for task table column visibility in format: {"column_name": boolean}';