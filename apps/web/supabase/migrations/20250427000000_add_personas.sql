-- Create personas table
CREATE TABLE personas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  role VARCHAR(255) NOT NULL,
  avatar_url TEXT,
  department VARCHAR(255),
  management_level VARCHAR(50),
  status VARCHAR(20) DEFAULT 'Active',
  
  -- Company details
  industries JSONB,
  company_size VARCHAR(50),
  location VARCHAR(255),
  tech_stack JSONB,
  budget_range VARCHAR(50),
  
  -- Behavioral profile
  challenges JSONB,
  goals JSONB,
  decision_authority VARCHAR(50),
  buying_stage VARCHAR(50),
  info_preferences JSONB,
  
  -- Content preferences
  content_formats JSONB,
  communication_style VARCHAR(50),
  content_length VARCHAR(50),
  channels JSONB,
  topics JSONB,
  
  -- Metadata
  content_count INTEGER DEFAULT 0,
  last_used TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Company association
  company_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE
);

-- Create index for faster queries
CREATE INDEX personas_company_id_idx ON personas(company_id);
CREATE INDEX personas_name_idx ON personas(name);
CREATE INDEX personas_status_idx ON personas(status);

-- Create campaign-persona associations table
CREATE TABLE campaign_personas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID NOT NULL REFERENCES company_campaigns(id) ON DELETE CASCADE,
  persona_id UUID NOT NULL REFERENCES personas(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique campaign-persona pairs
  UNIQUE(campaign_id, persona_id)
);

-- Create content-persona associations table
CREATE TABLE content_personas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  content_id UUID NOT NULL REFERENCES company_content(id) ON DELETE CASCADE,
  persona_id UUID NOT NULL REFERENCES personas(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique content-persona pairs
  UNIQUE(content_id, persona_id)
);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_personas_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_personas_updated_at
BEFORE UPDATE ON personas
FOR EACH ROW EXECUTE FUNCTION update_personas_updated_at();

-- -- Add RLS policies
-- ALTER TABLE personas ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY personas_company_policy ON personas 
--   USING (company_id IN (
--     SELECT id FROM accounts WHERE id IN (
--       SELECT account_id FROM user_teams WHERE user_id = auth.uid()
--     )
--   ));

-- ALTER TABLE campaign_personas ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY campaign_personas_policy ON campaign_personas 
--   USING (campaign_id IN (
--     SELECT id FROM company_campaigns WHERE company_id IN (
--       SELECT account_id FROM user_teams WHERE user_id = auth.uid()
--     )
--   ));

-- ALTER TABLE content_personas ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY content_personas_policy ON content_personas 
--   USING (content_id IN (
--     SELECT id FROM company_content WHERE company_id IN (
--       SELECT account_id FROM user_teams WHERE user_id = auth.uid()
--     )
--   ));