
-- Give access to service_role in creating billing
grant usage on sequence public.billing_customers_id_seq to service_role;


-- Migration to restore OTP (One-Time Password) functionality
-- This restores the nonces table and related functions that were dropped

set check_function_bodies = off;

-- Create the nonces table
create table "public"."nonces" (
    "id" uuid not null default gen_random_uuid(),
    "client_token" text not null,
    "nonce" text not null,
    "user_id" uuid,
    "purpose" text not null,
    "expires_at" timestamptz not null,
    "created_at" timestamptz not null default now(),
    "used_at" timestamptz,
    "revoked" boolean not null default false,
    "revoked_reason" text,
    "verification_attempts" integer not null default 0,
    "last_verification_at" timestamptz,
    "last_verification_ip" inet,
    "last_verification_user_agent" text,
    "metadata" jsonb default '{}'::jsonb,
    "scopes" text[] default '{}'::text[]
);

-- Enable Row Level Security
alter table "public"."nonces" enable row level security;

-- Create indexes
CREATE INDEX idx_nonces_status ON public.nonces USING btree (client_token, user_id, purpose, expires_at) WHERE ((used_at IS NULL) AND (revoked = false));
CREATE UNIQUE INDEX nonces_pkey ON public.nonces USING btree (id);

-- Add constraints
alter table "public"."nonces" add constraint "nonces_pkey" PRIMARY KEY using index "nonces_pkey";
alter table "public"."nonces" add constraint "nonces_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;
alter table "public"."nonces" validate constraint "nonces_user_id_fkey";

-- Create the create_nonce function
CREATE OR REPLACE FUNCTION public.create_nonce(p_user_id uuid DEFAULT NULL::uuid, p_purpose text DEFAULT NULL::text, p_expires_in_seconds integer DEFAULT 3600, p_metadata jsonb DEFAULT NULL::jsonb, p_scopes text[] DEFAULT NULL::text[], p_revoke_previous boolean DEFAULT true)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_client_token TEXT;
    v_nonce TEXT;
    v_expires_at TIMESTAMPTZ;
    v_id UUID;
    v_plaintext_token TEXT;
    v_revoked_count INTEGER;
BEGIN
    -- Revoke previous tokens for the same user and purpose if requested
    -- This only applies if a user ID is provided (not for anonymous tokens)
    IF p_revoke_previous = TRUE AND p_user_id IS NOT NULL THEN
        WITH revoked AS (
            UPDATE public.nonces
                SET
                    revoked = TRUE,
                    revoked_reason = 'Superseded by new token with same purpose'
                WHERE
                    user_id = p_user_id
                        AND purpose = p_purpose
                        AND used_at IS NULL
                        AND revoked = FALSE
                        AND expires_at > NOW()
                RETURNING 1
        )
        SELECT COUNT(*) INTO v_revoked_count FROM revoked;
    END IF;

    -- Generate a 6-digit token
    v_plaintext_token := (100000 + floor(random() * 900000))::text;
    v_client_token := extensions.crypt(v_plaintext_token, extensions.gen_salt('bf'));

    -- Still generate a secure nonce for internal use
    v_nonce := encode(extensions.gen_random_bytes(24), 'base64');
    v_nonce := extensions.crypt(v_nonce, extensions.gen_salt('bf'));

    -- Calculate expiration time
    v_expires_at := NOW() + (p_expires_in_seconds * interval '1 second');

    -- Insert the new nonce
    INSERT INTO public.nonces (
        client_token,
        nonce,
        user_id,
        expires_at,
        metadata,
        purpose,
        scopes
    )
    VALUES (
               v_client_token,
               v_nonce,
               p_user_id,
               v_expires_at,
               COALESCE(p_metadata, '{}'::JSONB),
               p_purpose,
               COALESCE(p_scopes, '{}'::TEXT[])
           )
    RETURNING id INTO v_id;

    -- Return the token information
    -- Note: returning the plaintext token, not the hash
    RETURN jsonb_build_object(
            'id', v_id,
            'token', v_plaintext_token,
            'expires_at', v_expires_at,
            'revoked_previous_count', COALESCE(v_revoked_count, 0)
           );
END;
$function$;

-- Grant permissions for create_nonce
grant execute on function public.create_nonce to service_role;

-- Create the verify_nonce function
CREATE OR REPLACE FUNCTION public.verify_nonce(p_token text, p_purpose text, p_user_id uuid DEFAULT NULL::uuid, p_required_scopes text[] DEFAULT NULL::text[], p_max_verification_attempts integer DEFAULT 5, p_ip inet DEFAULT NULL::inet, p_user_agent text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_nonce          RECORD;
    v_matching_count INTEGER;
BEGIN
    -- Count how many matching tokens exist before verification attempt
    SELECT COUNT(*)
    INTO v_matching_count
    FROM public.nonces
    WHERE purpose = p_purpose;

    -- Update verification attempt counter and tracking info for all matching tokens
    UPDATE public.nonces
    SET verification_attempts        = verification_attempts + 1,
        last_verification_at         = NOW(),
        last_verification_ip         = COALESCE(p_ip, last_verification_ip),
        last_verification_user_agent = COALESCE(p_user_agent, last_verification_user_agent)
    WHERE client_token = extensions.crypt(p_token, client_token)
      AND purpose = p_purpose;

    -- Find the nonce by token and purpose
    -- Modified to handle user-specific tokens better
    SELECT *
    INTO v_nonce
    FROM public.nonces
    WHERE client_token = extensions.crypt(p_token, client_token)
      AND purpose = p_purpose
      -- Only apply user_id filter if the token was created for a specific user
      AND (
        -- Case 1: Anonymous token (user_id is NULL in DB)
        (user_id IS NULL)
            OR
            -- Case 2: User-specific token (check if user_id matches)
        (user_id = p_user_id)
        )
      AND used_at IS NULL
      AND NOT revoked
      AND expires_at > NOW();

    -- Check if nonce exists
    IF v_nonce.id IS NULL THEN
        RETURN jsonb_build_object(
                'valid', false,
                'message', 'Invalid or expired token'
               );
    END IF;

    -- Check if max verification attempts exceeded
    IF p_max_verification_attempts > 0 AND v_nonce.verification_attempts > p_max_verification_attempts THEN
        -- Automatically revoke the token
        UPDATE public.nonces
        SET revoked        = TRUE,
            revoked_reason = 'Maximum verification attempts exceeded'
        WHERE id = v_nonce.id;

        RETURN jsonb_build_object(
                'valid', false,
                'message', 'Token revoked due to too many verification attempts',
                'max_attempts_exceeded', true
               );
    END IF;

    -- Check scopes if required
    IF p_required_scopes IS NOT NULL AND array_length(p_required_scopes, 1) > 0 THEN
        -- Fix scope validation to properly check if token scopes contain all required scopes
        -- Using array containment check: array1 @> array2 (array1 contains array2)
        IF NOT (v_nonce.scopes @> p_required_scopes) THEN
            RETURN jsonb_build_object(
                    'valid', false,
                    'message', 'Token does not have required permissions',
                    'token_scopes', v_nonce.scopes,
                    'required_scopes', p_required_scopes
                   );
        END IF;
    END IF;

    -- Mark nonce as used
    UPDATE public.nonces
    SET used_at = NOW()
    WHERE id = v_nonce.id;

    -- Return success with metadata
    RETURN jsonb_build_object(
            'valid', true,
            'user_id', v_nonce.user_id,
            'metadata', v_nonce.metadata,
            'scopes', v_nonce.scopes,
            'purpose', v_nonce.purpose
           );
END;
$function$;

-- Grant permissions for verify_nonce
grant execute on function public.verify_nonce to authenticated, service_role;

-- Create the get_nonce_status function
CREATE OR REPLACE FUNCTION public.get_nonce_status(p_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  v_nonce public.nonces;
BEGIN
  SELECT * INTO v_nonce FROM public.nonces WHERE id = p_id;

  IF v_nonce.id IS NULL THEN
    RETURN jsonb_build_object('exists', false);
  END IF;

  RETURN jsonb_build_object(
    'exists', true,
    'purpose', v_nonce.purpose,
    'user_id', v_nonce.user_id,
    'created_at', v_nonce.created_at,
    'expires_at', v_nonce.expires_at,
    'used_at', v_nonce.used_at,
    'revoked', v_nonce.revoked,
    'revoked_reason', v_nonce.revoked_reason,
    'verification_attempts', v_nonce.verification_attempts,
    'last_verification_at', v_nonce.last_verification_at,
    'last_verification_ip', v_nonce.last_verification_ip,
    'is_valid', (v_nonce.used_at IS NULL AND NOT v_nonce.revoked AND v_nonce.expires_at > NOW())
  );
END;
$function$;

-- Create the revoke_nonce function
CREATE OR REPLACE FUNCTION public.revoke_nonce(p_id uuid, p_reason text DEFAULT NULL::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  v_affected_rows INTEGER;
BEGIN
  UPDATE public.nonces
  SET
    revoked = TRUE,
    revoked_reason = p_reason
  WHERE
    id = p_id
    AND used_at IS NULL
    AND NOT revoked
  RETURNING 1 INTO v_affected_rows;

  RETURN v_affected_rows > 0;
END;
$function$;

-- Grant permissions for revoke_nonce
grant execute on function public.revoke_nonce to service_role;

-- Create the cleanup function
CREATE OR REPLACE FUNCTION kit.cleanup_expired_nonces(p_older_than_days integer DEFAULT 1, p_include_used boolean DEFAULT true, p_include_revoked boolean DEFAULT true)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  v_count INTEGER;
BEGIN
  -- Count and delete expired or used nonces based on parameters
  WITH deleted AS (
    DELETE FROM public.nonces
    WHERE
      (
        -- Expired and unused tokens
        (expires_at < NOW() AND used_at IS NULL)

        -- Used tokens older than specified days (if enabled)
        OR (p_include_used = TRUE AND used_at < NOW() - (p_older_than_days * interval '1 day'))

        -- Revoked tokens older than specified days (if enabled)
        OR (p_include_revoked = TRUE AND revoked = TRUE AND created_at < NOW() - (p_older_than_days * interval '1 day'))
      )
    RETURNING 1
  )
  SELECT COUNT(*) INTO v_count FROM deleted;

  RETURN v_count;
END;
$function$;

-- Create MFA functions
CREATE OR REPLACE FUNCTION public.is_aal2()
 RETURNS boolean
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    is_aal2 boolean;
begin
    select auth.jwt() ->> 'aal' = 'aal2' into is_aal2;

    return coalesce(is_aal2, false);
end
$function$;

-- Grant access to the function to authenticated users
grant execute on function public.is_aal2() to authenticated;

CREATE OR REPLACE FUNCTION public.is_super_admin()
 RETURNS boolean
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    is_super_admin boolean;
begin
    if not public.is_aal2() then
        return false;
    end if;

    select (auth.jwt() ->> 'app_metadata')::jsonb ->> 'role' = 'super-admin' into is_super_admin;

    return coalesce(is_super_admin, false);
end
$function$;

-- Grant access to the function to authenticated users
grant execute on function public.is_super_admin() to authenticated;

CREATE OR REPLACE FUNCTION public.is_mfa_compliant()
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
begin
    return array[(select auth.jwt()->>'aal')] <@ (
        select
            case
                when count(id) > 0 then array['aal2']
                else array['aal1', 'aal2']
                end as aal
        from auth.mfa_factors
        where ((select auth.uid()) = auth.mfa_factors.user_id) and auth.mfa_factors.status = 'verified'
    );
end
$function$;

-- Grant access to the function to authenticated users
grant execute on function public.is_mfa_compliant() to authenticated;

-- Grant table permissions
grant delete on table "public"."nonces" to "anon";
grant insert on table "public"."nonces" to "anon";
grant references on table "public"."nonces" to "anon";
grant select on table "public"."nonces" to "anon";
grant trigger on table "public"."nonces" to "anon";
grant truncate on table "public"."nonces" to "anon";
grant update on table "public"."nonces" to "anon";
grant delete on table "public"."nonces" to "authenticated";
grant insert on table "public"."nonces" to "authenticated";
grant references on table "public"."nonces" to "authenticated";
grant select on table "public"."nonces" to "authenticated";
grant trigger on table "public"."nonces" to "authenticated";
grant truncate on table "public"."nonces" to "authenticated";
grant update on table "public"."nonces" to "authenticated";
grant delete on table "public"."nonces" to "service_role";
grant insert on table "public"."nonces" to "service_role";
grant references on table "public"."nonces" to "service_role";
grant select on table "public"."nonces" to "service_role";
grant trigger on table "public"."nonces" to "service_role";
grant truncate on table "public"."nonces" to "service_role";
grant update on table "public"."nonces" to "service_role";

-- Create RLS policy for nonces
create policy "Users can read their own nonces"
on "public"."nonces"
as permissive
for select
to authenticated
using (
  user_id = (select auth.uid())
);
