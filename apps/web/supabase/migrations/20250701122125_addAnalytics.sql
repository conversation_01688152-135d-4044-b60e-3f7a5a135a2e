drop policy "Enable delete for users based on user_id" on "public"."linkedInState";

drop policy "Enable insert for users based on user_id" on "public"."linkedInState";

drop policy "Enable users to update their own data only" on "public"."linkedInState";

drop policy "Enable users to view their own data only" on "public"."linkedInState";

revoke delete on table "public"."linkedInState" from "anon";

revoke insert on table "public"."linkedInState" from "anon";

revoke references on table "public"."linkedInState" from "anon";

revoke select on table "public"."linkedInState" from "anon";

revoke trigger on table "public"."linkedInState" from "anon";

revoke truncate on table "public"."linkedInState" from "anon";

revoke update on table "public"."linkedInState" from "anon";

revoke delete on table "public"."linkedInState" from "authenticated";

revoke insert on table "public"."linkedInState" from "authenticated";

revoke references on table "public"."linkedInState" from "authenticated";

revoke select on table "public"."linkedInState" from "authenticated";

revoke trigger on table "public"."linkedInState" from "authenticated";

revoke truncate on table "public"."linkedInState" from "authenticated";

revoke update on table "public"."linkedInState" from "authenticated";

revoke delete on table "public"."linkedInState" from "service_role";

revoke insert on table "public"."linkedInState" from "service_role";

revoke references on table "public"."linkedInState" from "service_role";

revoke select on table "public"."linkedInState" from "service_role";

revoke trigger on table "public"."linkedInState" from "service_role";

revoke truncate on table "public"."linkedInState" from "service_role";

revoke update on table "public"."linkedInState" from "service_role";

alter table "public"."linkedInState" drop constraint "linkedInState_user_id_fkey";

alter table "public"."linkedInState" drop constraint "linkedInState_user_id_key";

alter table "public"."linkedInState" drop constraint "linkedInState_pkey";

drop index if exists "public"."linkedInState_pkey";

drop index if exists "public"."linkedInState_user_id_key";

drop table "public"."linkedInState";


alter table "public"."ayrshare_social_profiles" enable row level security;

create table "public"."linkedin_uris" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "url" text,
    "profile" jsonb
);


alter table "public"."linkedin_uris" enable row level security;

create table "public"."post_engagement_details" (
    "id" uuid not null default gen_random_uuid(),
    "platform_post_id" text not null,
    "engagement_info" jsonb,
    "is_generating" boolean,
    "error_generating" boolean,
    "user_id" uuid,
    "profile_id" uuid not null,
    "engaged_users" jsonb,
    "company_id" uuid not null default gen_random_uuid()
);


alter table "public"."post_engagement_details" enable row level security;

alter table "public"."post_templates" enable row level security;

alter table "public"."ayrshare_user_profile" alter column "company_id" set not null;


alter table "public"."company_content" add column "kanban_order" integer;

alter table "public"."generated_research" alter column "persona_id" drop not null;


alter table "public"."post_engagement_details" add constraint "post_engagement_details_profile_id_fkey" FOREIGN KEY (profile_id) REFERENCES ayrshare_user_profile(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."post_engagement_details" validate constraint "post_engagement_details_profile_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text, website text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    new_account public.accounts;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    insert into public.accounts(
        name,
        website,
        is_personal_account)
    values (
        account_name,
        website,
        false)
    returning * into new_account;

    return new_account;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$declare
    new_account public.accounts;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    insert into public.accounts(
        name,
        website,
        is_personal_account)
    values (
        account_name,
        website,
        false)
returning
    * into new_account;

    return new_account;

end;$function$
;

grant delete on table "public"."accounts" to "anon";

grant insert on table "public"."accounts" to "anon";

grant references on table "public"."accounts" to "anon";

grant select on table "public"."accounts" to "anon";

grant trigger on table "public"."accounts" to "anon";

grant truncate on table "public"."accounts" to "anon";

grant update on table "public"."accounts" to "anon";

grant delete on table "public"."accounts_memberships" to "anon";

grant insert on table "public"."accounts_memberships" to "anon";

grant references on table "public"."accounts_memberships" to "anon";

grant select on table "public"."accounts_memberships" to "anon";

grant trigger on table "public"."accounts_memberships" to "anon";

grant truncate on table "public"."accounts_memberships" to "anon";

grant update on table "public"."accounts_memberships" to "anon";

grant delete on table "public"."ayrshare_social_profiles" to "anon";

grant insert on table "public"."ayrshare_social_profiles" to "anon";

grant references on table "public"."ayrshare_social_profiles" to "anon";

grant select on table "public"."ayrshare_social_profiles" to "anon";

grant trigger on table "public"."ayrshare_social_profiles" to "anon";

grant truncate on table "public"."ayrshare_social_profiles" to "anon";

grant update on table "public"."ayrshare_social_profiles" to "anon";

grant delete on table "public"."ayrshare_social_profiles" to "authenticated";

grant insert on table "public"."ayrshare_social_profiles" to "authenticated";

grant references on table "public"."ayrshare_social_profiles" to "authenticated";

grant select on table "public"."ayrshare_social_profiles" to "authenticated";

grant trigger on table "public"."ayrshare_social_profiles" to "authenticated";

grant truncate on table "public"."ayrshare_social_profiles" to "authenticated";

grant update on table "public"."ayrshare_social_profiles" to "authenticated";

grant delete on table "public"."ayrshare_social_profiles" to "service_role";

grant insert on table "public"."ayrshare_social_profiles" to "service_role";

grant references on table "public"."ayrshare_social_profiles" to "service_role";

grant select on table "public"."ayrshare_social_profiles" to "service_role";

grant trigger on table "public"."ayrshare_social_profiles" to "service_role";

grant truncate on table "public"."ayrshare_social_profiles" to "service_role";

grant update on table "public"."ayrshare_social_profiles" to "service_role";

grant delete on table "public"."billing_customers" to "anon";

grant insert on table "public"."billing_customers" to "anon";

grant references on table "public"."billing_customers" to "anon";

grant select on table "public"."billing_customers" to "anon";

grant trigger on table "public"."billing_customers" to "anon";

grant truncate on table "public"."billing_customers" to "anon";

grant update on table "public"."billing_customers" to "anon";

grant delete on table "public"."config" to "anon";

grant insert on table "public"."config" to "anon";

grant references on table "public"."config" to "anon";

grant select on table "public"."config" to "anon";

grant trigger on table "public"."config" to "anon";

grant truncate on table "public"."config" to "anon";

grant update on table "public"."config" to "anon";

grant delete on table "public"."invitations" to "anon";

grant insert on table "public"."invitations" to "anon";

grant references on table "public"."invitations" to "anon";

grant select on table "public"."invitations" to "anon";

grant trigger on table "public"."invitations" to "anon";

grant truncate on table "public"."invitations" to "anon";

grant update on table "public"."invitations" to "anon";

grant delete on table "public"."linkedin_uris" to "anon";

grant insert on table "public"."linkedin_uris" to "anon";

grant references on table "public"."linkedin_uris" to "anon";

grant select on table "public"."linkedin_uris" to "anon";

grant trigger on table "public"."linkedin_uris" to "anon";

grant truncate on table "public"."linkedin_uris" to "anon";

grant update on table "public"."linkedin_uris" to "anon";

grant delete on table "public"."linkedin_uris" to "authenticated";

grant insert on table "public"."linkedin_uris" to "authenticated";

grant references on table "public"."linkedin_uris" to "authenticated";

grant select on table "public"."linkedin_uris" to "authenticated";

grant trigger on table "public"."linkedin_uris" to "authenticated";

grant truncate on table "public"."linkedin_uris" to "authenticated";

grant update on table "public"."linkedin_uris" to "authenticated";

grant delete on table "public"."linkedin_uris" to "service_role";

grant insert on table "public"."linkedin_uris" to "service_role";

grant references on table "public"."linkedin_uris" to "service_role";

grant select on table "public"."linkedin_uris" to "service_role";

grant trigger on table "public"."linkedin_uris" to "service_role";

grant truncate on table "public"."linkedin_uris" to "service_role";

grant update on table "public"."linkedin_uris" to "service_role";

grant delete on table "public"."notifications" to "anon";

grant insert on table "public"."notifications" to "anon";

grant references on table "public"."notifications" to "anon";

grant select on table "public"."notifications" to "anon";

grant trigger on table "public"."notifications" to "anon";

grant truncate on table "public"."notifications" to "anon";

grant update on table "public"."notifications" to "anon";

grant insert on table "public"."onboarding" to "service_role";

grant references on table "public"."onboarding" to "service_role";

grant trigger on table "public"."onboarding" to "service_role";

grant truncate on table "public"."onboarding" to "service_role";

grant update on table "public"."onboarding" to "service_role";

grant delete on table "public"."order_items" to "anon";

grant insert on table "public"."order_items" to "anon";

grant references on table "public"."order_items" to "anon";

grant select on table "public"."order_items" to "anon";

grant trigger on table "public"."order_items" to "anon";

grant truncate on table "public"."order_items" to "anon";

grant update on table "public"."order_items" to "anon";

grant delete on table "public"."orders" to "anon";

grant insert on table "public"."orders" to "anon";

grant references on table "public"."orders" to "anon";

grant select on table "public"."orders" to "anon";

grant trigger on table "public"."orders" to "anon";

grant truncate on table "public"."orders" to "anon";

grant update on table "public"."orders" to "anon";

grant delete on table "public"."post_engagement_details" to "anon";

grant insert on table "public"."post_engagement_details" to "anon";

grant references on table "public"."post_engagement_details" to "anon";

grant select on table "public"."post_engagement_details" to "anon";

grant trigger on table "public"."post_engagement_details" to "anon";

grant truncate on table "public"."post_engagement_details" to "anon";

grant update on table "public"."post_engagement_details" to "anon";

grant delete on table "public"."post_engagement_details" to "authenticated";

grant insert on table "public"."post_engagement_details" to "authenticated";

grant references on table "public"."post_engagement_details" to "authenticated";

grant select on table "public"."post_engagement_details" to "authenticated";

grant trigger on table "public"."post_engagement_details" to "authenticated";

grant truncate on table "public"."post_engagement_details" to "authenticated";

grant update on table "public"."post_engagement_details" to "authenticated";

grant delete on table "public"."post_engagement_details" to "service_role";

grant insert on table "public"."post_engagement_details" to "service_role";

grant references on table "public"."post_engagement_details" to "service_role";

grant select on table "public"."post_engagement_details" to "service_role";

grant trigger on table "public"."post_engagement_details" to "service_role";

grant truncate on table "public"."post_engagement_details" to "service_role";

grant update on table "public"."post_engagement_details" to "service_role";

grant delete on table "public"."post_templates" to "anon";

grant insert on table "public"."post_templates" to "anon";

grant references on table "public"."post_templates" to "anon";

grant select on table "public"."post_templates" to "anon";

grant trigger on table "public"."post_templates" to "anon";

grant truncate on table "public"."post_templates" to "anon";

grant update on table "public"."post_templates" to "anon";

grant delete on table "public"."post_templates" to "authenticated";

grant insert on table "public"."post_templates" to "authenticated";

grant references on table "public"."post_templates" to "authenticated";

grant select on table "public"."post_templates" to "authenticated";

grant trigger on table "public"."post_templates" to "authenticated";

grant truncate on table "public"."post_templates" to "authenticated";

grant update on table "public"."post_templates" to "authenticated";

grant delete on table "public"."post_templates" to "service_role";

grant insert on table "public"."post_templates" to "service_role";

grant references on table "public"."post_templates" to "service_role";

grant select on table "public"."post_templates" to "service_role";

grant trigger on table "public"."post_templates" to "service_role";

grant truncate on table "public"."post_templates" to "service_role";

grant update on table "public"."post_templates" to "service_role";

grant delete on table "public"."role_permissions" to "anon";

grant insert on table "public"."role_permissions" to "anon";

grant references on table "public"."role_permissions" to "anon";

grant select on table "public"."role_permissions" to "anon";

grant trigger on table "public"."role_permissions" to "anon";

grant truncate on table "public"."role_permissions" to "anon";

grant update on table "public"."role_permissions" to "anon";

grant delete on table "public"."roles" to "anon";

grant insert on table "public"."roles" to "anon";

grant references on table "public"."roles" to "anon";

grant select on table "public"."roles" to "anon";

grant trigger on table "public"."roles" to "anon";

grant truncate on table "public"."roles" to "anon";

grant update on table "public"."roles" to "anon";

grant delete on table "public"."subscription_items" to "anon";

grant insert on table "public"."subscription_items" to "anon";

grant references on table "public"."subscription_items" to "anon";

grant select on table "public"."subscription_items" to "anon";

grant trigger on table "public"."subscription_items" to "anon";

grant truncate on table "public"."subscription_items" to "anon";

grant update on table "public"."subscription_items" to "anon";

grant delete on table "public"."subscriptions" to "anon";

grant insert on table "public"."subscriptions" to "anon";

grant references on table "public"."subscriptions" to "anon";

grant select on table "public"."subscriptions" to "anon";

grant trigger on table "public"."subscriptions" to "anon";

grant truncate on table "public"."subscriptions" to "anon";

grant update on table "public"."subscriptions" to "anon";


