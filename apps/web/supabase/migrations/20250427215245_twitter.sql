create table "public"."twitterState" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp without time zone,
    "state" character varying,
    "access_token" character varying,
    "refresh_token" character varying,
    "scope" character varying,
    "expires_in" timestamp without time zone,
    "refresh_token_expires_in" timestamp without time zone,
    "user_id" uuid not null default auth.uid(),
    "screen_name" character varying,
    "name" character varying,
    "profile_image_url" character varying,
    "description" character varying,
    "code_verifier" character varying
);


CREATE UNIQUE INDEX "twitterState_pkey" ON public."twitterState" USING btree (id);

CREATE UNIQUE INDEX "twitterState_user_id_key" ON public."twitterState" USING btree (user_id);

alter table "public"."twitterState" add constraint "twitterState_pkey" PRIMARY KEY using index "twitterState_pkey";

alter table "public"."twitterState" add constraint "twitterState_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."twitterState" validate constraint "twitterState_user_id_fkey";

alter table "public"."twitterState" add constraint "twitterState_user_id_key" UNIQUE using index "twitterState_user_id_key";

grant delete on table "public"."twitterState" to "anon";

grant insert on table "public"."twitterState" to "anon";

grant references on table "public"."twitterState" to "anon";

grant select on table "public"."twitterState" to "anon";

grant trigger on table "public"."twitterState" to "anon";

grant truncate on table "public"."twitterState" to "anon";

grant update on table "public"."twitterState" to "anon";

grant delete on table "public"."twitterState" to "authenticated";

grant insert on table "public"."twitterState" to "authenticated";

grant references on table "public"."twitterState" to "authenticated";

grant select on table "public"."twitterState" to "authenticated";

grant trigger on table "public"."twitterState" to "authenticated";

grant truncate on table "public"."twitterState" to "authenticated";

grant update on table "public"."twitterState" to "authenticated";

grant delete on table "public"."twitterState" to "service_role";

grant insert on table "public"."twitterState" to "service_role";

grant references on table "public"."twitterState" to "service_role";

grant select on table "public"."twitterState" to "service_role";

grant trigger on table "public"."twitterState" to "service_role";

grant truncate on table "public"."twitterState" to "service_role";

grant update on table "public"."twitterState" to "service_role";

create policy "Enable delete for users based on user_id"
on "public"."twitterState"
as permissive
for delete
to public
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable insert for users based on user_id"
on "public"."twitterState"
as permissive
for insert
to public
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to update their own data only"
on "public"."twitterState"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable users to view their own data only"
on "public"."twitterState"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));



