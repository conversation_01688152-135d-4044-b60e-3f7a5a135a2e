set check_function_bodies = off;

CREATE OR REPLACE FUNCTION kit.cleanup_expired_nonces(p_older_than_days integer DEFAULT 1, p_include_used boolean DEFAULT true, p_include_revoked boolean DEFAULT true)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  v_count INTEGER;
BEGIN
  -- Count and delete expired or used nonces based on parameters
  WITH deleted AS (
    DELETE FROM public.nonces
    WHERE
      (
        -- Expired and unused tokens
        (expires_at < NOW() AND used_at IS NULL)

        -- Used tokens older than specified days (if enabled)
        OR (p_include_used = TRUE AND used_at < NOW() - (p_older_than_days * interval '1 day'))

        -- Revoked tokens older than specified days (if enabled)
        OR (p_include_revoked = TRUE AND revoked = TRUE AND created_at < NOW() - (p_older_than_days * interval '1 day'))
      )
    RETURNING 1
  )
  SELECT COUNT(*) INTO v_count FROM deleted;

  RETURN v_count;
END;
$function$
;


drop trigger if exists "accounts_teardown" on "public"."accounts";

drop trigger if exists "create_feature_usage_row" on "public"."accounts";

drop trigger if exists "init_task_statuses_trigger" on "public"."accounts";

drop trigger if exists "ayrshare_user_profile_set_timestamps" on "public"."ayrshare_user_profile";

drop trigger if exists "set_generated_research_timestamps" on "public"."generated_research";

drop trigger if exists "set_generated_research_user_tracking" on "public"."generated_research";

drop trigger if exists "invitations_insert" on "public"."invitations";

drop trigger if exists "update_personas_updated_at" on "public"."personas";

drop trigger if exists "saved_research_set_timestamps" on "public"."saved_research";

drop trigger if exists "subscriptions_delete" on "public"."subscriptions";

drop policy "ayrshare_user_profile_delete" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_insert" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_select" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_update" on "public"."ayrshare_user_profile";

drop policy "team_members_can_insert_campaign_ideas" on "public"."campaign_ideas";

drop policy "team_members_can_select_campaign_ideas" on "public"."campaign_ideas";

drop policy "team_members_can_update_campaign_ideas" on "public"."campaign_ideas";

drop policy "Allow brand deletion for owners in the same company" on "public"."company_brand";

drop policy "team_members_can_insert_company_brand" on "public"."company_brand";

drop policy "team_members_can_select_company_brand" on "public"."company_brand";

drop policy "team_members_can_update_company_brand" on "public"."company_brand";

drop policy "team_members_can_delete_company_campaigns" on "public"."company_campaigns";

drop policy "team_members_can_insert_company_campaigns" on "public"."company_campaigns";

drop policy "team_members_can_select_company_campaigns" on "public"."company_campaigns";

drop policy "team_members_can_update_company_campaigns" on "public"."company_campaigns";

drop policy "Enable delete for users based on user_id" on "public"."company_content";

drop policy "team_members_can_insert_company_content" on "public"."company_content";

drop policy "team_members_can_select_company_content" on "public"."company_content";

drop policy "team_members_can_update_company_content" on "public"."company_content";

drop policy "team_members_can_delete_company_task_statuses" on "public"."company_task_statuses";

drop policy "team_members_can_insert_company_task_statuses" on "public"."company_task_statuses";

drop policy "team_members_can_select_company_task_statuses" on "public"."company_task_statuses";

drop policy "team_members_can_update_company_task_statuses" on "public"."company_task_statuses";

drop policy "select_feature_usage" on "public"."feature_usage";

drop policy "generated_research_delete" on "public"."generated_research";

drop policy "generated_research_insert" on "public"."generated_research";

drop policy "generated_research_read" on "public"."generated_research";

drop policy "generated_research_update" on "public"."generated_research";

drop policy "Enable delete for users based on user_id" on "public"."linkedInState";

drop policy "Enable insert for users based on user_id" on "public"."linkedInState";

drop policy "Enable users to update their own data only" on "public"."linkedInState";

drop policy "Enable users to view their own data only" on "public"."linkedInState";

drop policy "insert_onboarding" on "public"."onboarding";

drop policy "read_onboarding" on "public"."onboarding";

drop policy "update_onboarding" on "public"."onboarding";

drop policy "saved_research_delete" on "public"."saved_research";

drop policy "saved_research_insert" on "public"."saved_research";

drop policy "saved_research_read" on "public"."saved_research";

drop policy "Enable delete for users based on user_id" on "public"."twitterState";

drop policy "Enable insert for users based on user_id" on "public"."twitterState";

drop policy "Enable users to update their own data only" on "public"."twitterState";

drop policy "Enable users to view their own data only" on "public"."twitterState";

drop policy "invitations_read_self" on "public"."invitations";

revoke delete on table "public"."ayrshare_user_profile" from "anon";

revoke insert on table "public"."ayrshare_user_profile" from "anon";

revoke references on table "public"."ayrshare_user_profile" from "anon";

revoke select on table "public"."ayrshare_user_profile" from "anon";

revoke trigger on table "public"."ayrshare_user_profile" from "anon";

revoke truncate on table "public"."ayrshare_user_profile" from "anon";

revoke update on table "public"."ayrshare_user_profile" from "anon";

revoke delete on table "public"."ayrshare_user_profile" from "authenticated";

revoke insert on table "public"."ayrshare_user_profile" from "authenticated";

revoke references on table "public"."ayrshare_user_profile" from "authenticated";

revoke select on table "public"."ayrshare_user_profile" from "authenticated";

revoke trigger on table "public"."ayrshare_user_profile" from "authenticated";

revoke truncate on table "public"."ayrshare_user_profile" from "authenticated";

revoke update on table "public"."ayrshare_user_profile" from "authenticated";

revoke delete on table "public"."ayrshare_user_profile" from "service_role";

revoke insert on table "public"."ayrshare_user_profile" from "service_role";

revoke references on table "public"."ayrshare_user_profile" from "service_role";

revoke select on table "public"."ayrshare_user_profile" from "service_role";

revoke trigger on table "public"."ayrshare_user_profile" from "service_role";

revoke truncate on table "public"."ayrshare_user_profile" from "service_role";

revoke update on table "public"."ayrshare_user_profile" from "service_role";

revoke delete on table "public"."campaign_ideas" from "anon";

revoke insert on table "public"."campaign_ideas" from "anon";

revoke references on table "public"."campaign_ideas" from "anon";

revoke select on table "public"."campaign_ideas" from "anon";

revoke trigger on table "public"."campaign_ideas" from "anon";

revoke truncate on table "public"."campaign_ideas" from "anon";

revoke update on table "public"."campaign_ideas" from "anon";

revoke delete on table "public"."campaign_ideas" from "authenticated";

revoke insert on table "public"."campaign_ideas" from "authenticated";

revoke references on table "public"."campaign_ideas" from "authenticated";

revoke select on table "public"."campaign_ideas" from "authenticated";

revoke trigger on table "public"."campaign_ideas" from "authenticated";

revoke truncate on table "public"."campaign_ideas" from "authenticated";

revoke update on table "public"."campaign_ideas" from "authenticated";

revoke delete on table "public"."campaign_ideas" from "service_role";

revoke insert on table "public"."campaign_ideas" from "service_role";

revoke references on table "public"."campaign_ideas" from "service_role";

revoke select on table "public"."campaign_ideas" from "service_role";

revoke trigger on table "public"."campaign_ideas" from "service_role";

revoke truncate on table "public"."campaign_ideas" from "service_role";

revoke update on table "public"."campaign_ideas" from "service_role";

revoke delete on table "public"."campaign_personas" from "anon";

revoke insert on table "public"."campaign_personas" from "anon";

revoke references on table "public"."campaign_personas" from "anon";

revoke select on table "public"."campaign_personas" from "anon";

revoke trigger on table "public"."campaign_personas" from "anon";

revoke truncate on table "public"."campaign_personas" from "anon";

revoke update on table "public"."campaign_personas" from "anon";

revoke delete on table "public"."campaign_personas" from "authenticated";

revoke insert on table "public"."campaign_personas" from "authenticated";

revoke references on table "public"."campaign_personas" from "authenticated";

revoke select on table "public"."campaign_personas" from "authenticated";

revoke trigger on table "public"."campaign_personas" from "authenticated";

revoke truncate on table "public"."campaign_personas" from "authenticated";

revoke update on table "public"."campaign_personas" from "authenticated";

revoke delete on table "public"."campaign_personas" from "service_role";

revoke insert on table "public"."campaign_personas" from "service_role";

revoke references on table "public"."campaign_personas" from "service_role";

revoke select on table "public"."campaign_personas" from "service_role";

revoke trigger on table "public"."campaign_personas" from "service_role";

revoke truncate on table "public"."campaign_personas" from "service_role";

revoke update on table "public"."campaign_personas" from "service_role";

revoke delete on table "public"."company_brand" from "anon";

revoke insert on table "public"."company_brand" from "anon";

revoke references on table "public"."company_brand" from "anon";

revoke select on table "public"."company_brand" from "anon";

revoke trigger on table "public"."company_brand" from "anon";

revoke truncate on table "public"."company_brand" from "anon";

revoke update on table "public"."company_brand" from "anon";

revoke delete on table "public"."company_brand" from "authenticated";

revoke insert on table "public"."company_brand" from "authenticated";

revoke references on table "public"."company_brand" from "authenticated";

revoke select on table "public"."company_brand" from "authenticated";

revoke trigger on table "public"."company_brand" from "authenticated";

revoke truncate on table "public"."company_brand" from "authenticated";

revoke update on table "public"."company_brand" from "authenticated";

revoke delete on table "public"."company_brand" from "service_role";

revoke insert on table "public"."company_brand" from "service_role";

revoke references on table "public"."company_brand" from "service_role";

revoke select on table "public"."company_brand" from "service_role";

revoke trigger on table "public"."company_brand" from "service_role";

revoke truncate on table "public"."company_brand" from "service_role";

revoke update on table "public"."company_brand" from "service_role";

revoke delete on table "public"."company_campaigns" from "anon";

revoke insert on table "public"."company_campaigns" from "anon";

revoke references on table "public"."company_campaigns" from "anon";

revoke select on table "public"."company_campaigns" from "anon";

revoke trigger on table "public"."company_campaigns" from "anon";

revoke truncate on table "public"."company_campaigns" from "anon";

revoke update on table "public"."company_campaigns" from "anon";

revoke delete on table "public"."company_campaigns" from "authenticated";

revoke insert on table "public"."company_campaigns" from "authenticated";

revoke references on table "public"."company_campaigns" from "authenticated";

revoke select on table "public"."company_campaigns" from "authenticated";

revoke trigger on table "public"."company_campaigns" from "authenticated";

revoke truncate on table "public"."company_campaigns" from "authenticated";

revoke update on table "public"."company_campaigns" from "authenticated";

revoke delete on table "public"."company_campaigns" from "service_role";

revoke insert on table "public"."company_campaigns" from "service_role";

revoke references on table "public"."company_campaigns" from "service_role";

revoke select on table "public"."company_campaigns" from "service_role";

revoke trigger on table "public"."company_campaigns" from "service_role";

revoke truncate on table "public"."company_campaigns" from "service_role";

revoke update on table "public"."company_campaigns" from "service_role";

revoke delete on table "public"."company_content" from "anon";

revoke insert on table "public"."company_content" from "anon";

revoke references on table "public"."company_content" from "anon";

revoke select on table "public"."company_content" from "anon";

revoke trigger on table "public"."company_content" from "anon";

revoke truncate on table "public"."company_content" from "anon";

revoke update on table "public"."company_content" from "anon";

revoke delete on table "public"."company_content" from "authenticated";

revoke insert on table "public"."company_content" from "authenticated";

revoke references on table "public"."company_content" from "authenticated";

revoke select on table "public"."company_content" from "authenticated";

revoke trigger on table "public"."company_content" from "authenticated";

revoke truncate on table "public"."company_content" from "authenticated";

revoke update on table "public"."company_content" from "authenticated";

revoke delete on table "public"."company_content" from "service_role";

revoke insert on table "public"."company_content" from "service_role";

revoke references on table "public"."company_content" from "service_role";

revoke select on table "public"."company_content" from "service_role";

revoke trigger on table "public"."company_content" from "service_role";

revoke truncate on table "public"."company_content" from "service_role";

revoke update on table "public"."company_content" from "service_role";

revoke delete on table "public"."company_task_statuses" from "anon";

revoke insert on table "public"."company_task_statuses" from "anon";

revoke references on table "public"."company_task_statuses" from "anon";

revoke select on table "public"."company_task_statuses" from "anon";

revoke trigger on table "public"."company_task_statuses" from "anon";

revoke truncate on table "public"."company_task_statuses" from "anon";

revoke update on table "public"."company_task_statuses" from "anon";

revoke delete on table "public"."company_task_statuses" from "authenticated";

revoke insert on table "public"."company_task_statuses" from "authenticated";

revoke references on table "public"."company_task_statuses" from "authenticated";

revoke select on table "public"."company_task_statuses" from "authenticated";

revoke trigger on table "public"."company_task_statuses" from "authenticated";

revoke truncate on table "public"."company_task_statuses" from "authenticated";

revoke update on table "public"."company_task_statuses" from "authenticated";

revoke delete on table "public"."company_task_statuses" from "service_role";

revoke insert on table "public"."company_task_statuses" from "service_role";

revoke references on table "public"."company_task_statuses" from "service_role";

revoke select on table "public"."company_task_statuses" from "service_role";

revoke trigger on table "public"."company_task_statuses" from "service_role";

revoke truncate on table "public"."company_task_statuses" from "service_role";

revoke update on table "public"."company_task_statuses" from "service_role";

revoke delete on table "public"."content_personas" from "anon";

revoke insert on table "public"."content_personas" from "anon";

revoke references on table "public"."content_personas" from "anon";

revoke select on table "public"."content_personas" from "anon";

revoke trigger on table "public"."content_personas" from "anon";

revoke truncate on table "public"."content_personas" from "anon";

revoke update on table "public"."content_personas" from "anon";

revoke delete on table "public"."content_personas" from "authenticated";

revoke insert on table "public"."content_personas" from "authenticated";

revoke references on table "public"."content_personas" from "authenticated";

revoke select on table "public"."content_personas" from "authenticated";

revoke trigger on table "public"."content_personas" from "authenticated";

revoke truncate on table "public"."content_personas" from "authenticated";

revoke update on table "public"."content_personas" from "authenticated";

revoke delete on table "public"."content_personas" from "service_role";

revoke insert on table "public"."content_personas" from "service_role";

revoke references on table "public"."content_personas" from "service_role";

revoke select on table "public"."content_personas" from "service_role";

revoke trigger on table "public"."content_personas" from "service_role";

revoke truncate on table "public"."content_personas" from "service_role";

revoke update on table "public"."content_personas" from "service_role";

revoke delete on table "public"."feature_usage" from "anon";

revoke insert on table "public"."feature_usage" from "anon";

revoke references on table "public"."feature_usage" from "anon";

revoke select on table "public"."feature_usage" from "anon";

revoke trigger on table "public"."feature_usage" from "anon";

revoke truncate on table "public"."feature_usage" from "anon";

revoke update on table "public"."feature_usage" from "anon";

revoke delete on table "public"."feature_usage" from "authenticated";

revoke insert on table "public"."feature_usage" from "authenticated";

revoke references on table "public"."feature_usage" from "authenticated";

revoke select on table "public"."feature_usage" from "authenticated";

revoke trigger on table "public"."feature_usage" from "authenticated";

revoke truncate on table "public"."feature_usage" from "authenticated";

revoke update on table "public"."feature_usage" from "authenticated";

revoke delete on table "public"."feature_usage" from "service_role";

revoke insert on table "public"."feature_usage" from "service_role";

revoke references on table "public"."feature_usage" from "service_role";

revoke select on table "public"."feature_usage" from "service_role";

revoke trigger on table "public"."feature_usage" from "service_role";

revoke truncate on table "public"."feature_usage" from "service_role";

revoke update on table "public"."feature_usage" from "service_role";

revoke delete on table "public"."generated_research" from "anon";

revoke insert on table "public"."generated_research" from "anon";

revoke references on table "public"."generated_research" from "anon";

revoke select on table "public"."generated_research" from "anon";

revoke trigger on table "public"."generated_research" from "anon";

revoke truncate on table "public"."generated_research" from "anon";

revoke update on table "public"."generated_research" from "anon";

revoke delete on table "public"."generated_research" from "authenticated";

revoke insert on table "public"."generated_research" from "authenticated";

revoke references on table "public"."generated_research" from "authenticated";

revoke select on table "public"."generated_research" from "authenticated";

revoke trigger on table "public"."generated_research" from "authenticated";

revoke truncate on table "public"."generated_research" from "authenticated";

revoke update on table "public"."generated_research" from "authenticated";

revoke delete on table "public"."generated_research" from "service_role";

revoke insert on table "public"."generated_research" from "service_role";

revoke references on table "public"."generated_research" from "service_role";

revoke select on table "public"."generated_research" from "service_role";

revoke trigger on table "public"."generated_research" from "service_role";

revoke truncate on table "public"."generated_research" from "service_role";

revoke update on table "public"."generated_research" from "service_role";

revoke delete on table "public"."linkedInState" from "anon";

revoke insert on table "public"."linkedInState" from "anon";

revoke references on table "public"."linkedInState" from "anon";

revoke select on table "public"."linkedInState" from "anon";

revoke trigger on table "public"."linkedInState" from "anon";

revoke truncate on table "public"."linkedInState" from "anon";

revoke update on table "public"."linkedInState" from "anon";

revoke delete on table "public"."linkedInState" from "authenticated";

revoke insert on table "public"."linkedInState" from "authenticated";

revoke references on table "public"."linkedInState" from "authenticated";

revoke select on table "public"."linkedInState" from "authenticated";

revoke trigger on table "public"."linkedInState" from "authenticated";

revoke truncate on table "public"."linkedInState" from "authenticated";

revoke update on table "public"."linkedInState" from "authenticated";

revoke delete on table "public"."linkedInState" from "service_role";

revoke insert on table "public"."linkedInState" from "service_role";

revoke references on table "public"."linkedInState" from "service_role";

revoke select on table "public"."linkedInState" from "service_role";

revoke trigger on table "public"."linkedInState" from "service_role";

revoke truncate on table "public"."linkedInState" from "service_role";

revoke update on table "public"."linkedInState" from "service_role";

revoke delete on table "public"."onboarding" from "anon";

revoke insert on table "public"."onboarding" from "anon";

revoke references on table "public"."onboarding" from "anon";

revoke select on table "public"."onboarding" from "anon";

revoke trigger on table "public"."onboarding" from "anon";

revoke truncate on table "public"."onboarding" from "anon";

revoke update on table "public"."onboarding" from "anon";

revoke delete on table "public"."onboarding" from "authenticated";

revoke insert on table "public"."onboarding" from "authenticated";

revoke references on table "public"."onboarding" from "authenticated";

revoke select on table "public"."onboarding" from "authenticated";

revoke trigger on table "public"."onboarding" from "authenticated";

revoke truncate on table "public"."onboarding" from "authenticated";

revoke update on table "public"."onboarding" from "authenticated";

revoke delete on table "public"."onboarding" from "service_role";

revoke select on table "public"."onboarding" from "service_role";

revoke delete on table "public"."personas" from "anon";

revoke insert on table "public"."personas" from "anon";

revoke references on table "public"."personas" from "anon";

revoke select on table "public"."personas" from "anon";

revoke trigger on table "public"."personas" from "anon";

revoke truncate on table "public"."personas" from "anon";

revoke update on table "public"."personas" from "anon";

revoke delete on table "public"."personas" from "authenticated";

revoke insert on table "public"."personas" from "authenticated";

revoke references on table "public"."personas" from "authenticated";

revoke select on table "public"."personas" from "authenticated";

revoke trigger on table "public"."personas" from "authenticated";

revoke truncate on table "public"."personas" from "authenticated";

revoke update on table "public"."personas" from "authenticated";

revoke delete on table "public"."personas" from "service_role";

revoke insert on table "public"."personas" from "service_role";

revoke references on table "public"."personas" from "service_role";

revoke select on table "public"."personas" from "service_role";

revoke trigger on table "public"."personas" from "service_role";

revoke truncate on table "public"."personas" from "service_role";

revoke update on table "public"."personas" from "service_role";

revoke delete on table "public"."product_documents" from "anon";

revoke insert on table "public"."product_documents" from "anon";

revoke references on table "public"."product_documents" from "anon";

revoke select on table "public"."product_documents" from "anon";

revoke trigger on table "public"."product_documents" from "anon";

revoke truncate on table "public"."product_documents" from "anon";

revoke update on table "public"."product_documents" from "anon";

revoke delete on table "public"."product_documents" from "authenticated";

revoke insert on table "public"."product_documents" from "authenticated";

revoke references on table "public"."product_documents" from "authenticated";

revoke select on table "public"."product_documents" from "authenticated";

revoke trigger on table "public"."product_documents" from "authenticated";

revoke truncate on table "public"."product_documents" from "authenticated";

revoke update on table "public"."product_documents" from "authenticated";

revoke delete on table "public"."product_documents" from "service_role";

revoke insert on table "public"."product_documents" from "service_role";

revoke references on table "public"."product_documents" from "service_role";

revoke select on table "public"."product_documents" from "service_role";

revoke trigger on table "public"."product_documents" from "service_role";

revoke truncate on table "public"."product_documents" from "service_role";

revoke update on table "public"."product_documents" from "service_role";

revoke delete on table "public"."saved_research" from "anon";

revoke insert on table "public"."saved_research" from "anon";

revoke references on table "public"."saved_research" from "anon";

revoke select on table "public"."saved_research" from "anon";

revoke trigger on table "public"."saved_research" from "anon";

revoke truncate on table "public"."saved_research" from "anon";

revoke update on table "public"."saved_research" from "anon";

revoke delete on table "public"."saved_research" from "authenticated";

revoke insert on table "public"."saved_research" from "authenticated";

revoke references on table "public"."saved_research" from "authenticated";

revoke select on table "public"."saved_research" from "authenticated";

revoke trigger on table "public"."saved_research" from "authenticated";

revoke truncate on table "public"."saved_research" from "authenticated";

revoke update on table "public"."saved_research" from "authenticated";

revoke delete on table "public"."saved_research" from "service_role";

revoke insert on table "public"."saved_research" from "service_role";

revoke references on table "public"."saved_research" from "service_role";

revoke select on table "public"."saved_research" from "service_role";

revoke trigger on table "public"."saved_research" from "service_role";

revoke truncate on table "public"."saved_research" from "service_role";

revoke update on table "public"."saved_research" from "service_role";

revoke delete on table "public"."twitterState" from "anon";

revoke insert on table "public"."twitterState" from "anon";

revoke references on table "public"."twitterState" from "anon";

revoke select on table "public"."twitterState" from "anon";

revoke trigger on table "public"."twitterState" from "anon";

revoke truncate on table "public"."twitterState" from "anon";

revoke update on table "public"."twitterState" from "anon";

revoke delete on table "public"."twitterState" from "authenticated";

revoke insert on table "public"."twitterState" from "authenticated";

revoke references on table "public"."twitterState" from "authenticated";

revoke select on table "public"."twitterState" from "authenticated";

revoke trigger on table "public"."twitterState" from "authenticated";

revoke truncate on table "public"."twitterState" from "authenticated";

revoke update on table "public"."twitterState" from "authenticated";

revoke delete on table "public"."twitterState" from "service_role";

revoke insert on table "public"."twitterState" from "service_role";

revoke references on table "public"."twitterState" from "service_role";

revoke select on table "public"."twitterState" from "service_role";

revoke trigger on table "public"."twitterState" from "service_role";

revoke truncate on table "public"."twitterState" from "service_role";

revoke update on table "public"."twitterState" from "service_role";

alter table "public"."ayrshare_user_profile" drop constraint "ayrshare_user_profile_company_id_fkey";

alter table "public"."ayrshare_user_profile" drop constraint "ayrshare_user_profile_user_id_fkey";

alter table "public"."campaign_ideas" drop constraint "campaign_ideas_campaign_id_fkey";

alter table "public"."campaign_ideas" drop constraint "campaign_ideas_company_id_fkey";

alter table "public"."campaign_personas" drop constraint "campaign_personas_campaign_id_fkey";

alter table "public"."campaign_personas" drop constraint "campaign_personas_campaign_id_persona_id_key";

alter table "public"."campaign_personas" drop constraint "campaign_personas_persona_id_fkey";

alter table "public"."company_brand" drop constraint "company_brand_company_id_fkey";

alter table "public"."company_campaigns" drop constraint "company_campaigns_company_id_fkey";

alter table "public"."company_campaigns" drop constraint "company_campaigns_slug_key";

alter table "public"."company_content" drop constraint "check_valid_status";

alter table "public"."company_content" drop constraint "company_content_campaign_id_fkey";

alter table "public"."company_content" drop constraint "company_content_company_id_fkey";

alter table "public"."company_content" drop constraint "company_content_idea_id_fkey";

alter table "public"."company_task_statuses" drop constraint "company_task_statuses_company_id_fkey";

alter table "public"."company_task_statuses" drop constraint "company_task_statuses_company_id_name_key";

alter table "public"."content_personas" drop constraint "content_personas_content_id_fkey";

alter table "public"."content_personas" drop constraint "content_personas_content_id_persona_id_key";

alter table "public"."content_personas" drop constraint "content_personas_persona_id_fkey";

alter table "public"."feature_usage" drop constraint "feature_usage_account_id_feature_key";

alter table "public"."feature_usage" drop constraint "feature_usage_account_id_fkey";

alter table "public"."generated_research" drop constraint "generated_research_account_id_fkey";

alter table "public"."generated_research" drop constraint "generated_research_created_by_fkey";

alter table "public"."generated_research" drop constraint "generated_research_research_type_check";

alter table "public"."generated_research" drop constraint "generated_research_updated_by_fkey";

alter table "public"."linkedInState" drop constraint "linkedInState_user_id_fkey";

alter table "public"."linkedInState" drop constraint "linkedInState_user_id_key";

alter table "public"."onboarding" drop constraint "onboarding_account_id_fkey";

alter table "public"."onboarding" drop constraint "onboarding_account_id_key";

alter table "public"."personas" drop constraint "personas_company_id_fkey";

alter table "public"."personas" drop constraint "personas_icp_id_fkey";

alter table "public"."saved_research" drop constraint "saved_research_account_id_fkey";

alter table "public"."saved_research" drop constraint "saved_research_account_id_icp_id_persona_id_title_research__key";

alter table "public"."saved_research" drop constraint "saved_research_relevance_score_check";

alter table "public"."saved_research" drop constraint "saved_research_research_type_check";

alter table "public"."twitterState" drop constraint "twitterState_user_id_fkey";

alter table "public"."twitterState" drop constraint "twitterState_user_id_key";

drop function if exists "public"."add_company_task_status"(p_company_id uuid, p_name text, p_display_name text, p_color text, p_icon text);

drop function if exists "public"."create_feature_usage_row"();

drop function if exists "public"."delete_company_task_status"(p_company_id uuid, p_status_id uuid, p_replacement_status text);

drop function if exists "public"."get_company_task_statuses"(p_company_id uuid);

drop function if exists "public"."init_task_statuses_for_new_company"();

drop function if exists "public"."initialize_company_task_statuses"(p_company_id uuid);

drop function if exists "public"."is_valid_task_status"(p_company_id uuid, p_status text);

drop function if exists "public"."reorder_company_task_statuses"(p_company_id uuid, p_status_ids uuid[]);

drop function if exists "public"."update_company_task_status"(p_status_id uuid, p_display_name text, p_status_order integer, p_color text, p_icon text);

drop function if exists "public"."update_personas_updated_at"();

drop function if exists "public"."get_user_social_profiles"(target_account_id uuid, target_user_id uuid);

alter table "public"."ayrshare_user_profile" drop constraint "ayrshare_user_profile_pkey";

alter table "public"."campaign_ideas" drop constraint "campaign_ideas_pkey";

alter table "public"."campaign_personas" drop constraint "campaign_personas_pkey";

alter table "public"."company_brand" drop constraint "company_brand_pkey";

alter table "public"."company_campaigns" drop constraint "company_campaigns_pkey";

alter table "public"."company_content" drop constraint "company_content_pkey";

alter table "public"."company_task_statuses" drop constraint "company_task_statuses_pkey";

alter table "public"."content_personas" drop constraint "content_personas_pkey";

alter table "public"."feature_usage" drop constraint "feature_usage_pkey";

alter table "public"."generated_research" drop constraint "generated_research_pkey";

alter table "public"."linkedInState" drop constraint "linkedInState_pkey";

alter table "public"."onboarding" drop constraint "onboarding_pkey";

alter table "public"."personas" drop constraint "personas_pkey";

alter table "public"."product_documents" drop constraint "product_documents_pkey";

alter table "public"."saved_research" drop constraint "saved_research_pkey";

alter table "public"."twitterState" drop constraint "twitterState_pkey";

drop index if exists "public"."ayrshare_user_profile_company_id_idx";

drop index if exists "public"."ayrshare_user_profile_is_active_idx";

drop index if exists "public"."ayrshare_user_profile_pkey";

drop index if exists "public"."campaign_ideas_pkey";

drop index if exists "public"."campaign_personas_campaign_id_persona_id_key";

drop index if exists "public"."campaign_personas_pkey";

drop index if exists "public"."company_brand_pkey";

drop index if exists "public"."company_campaigns_pkey";

drop index if exists "public"."company_campaigns_slug_key";

drop index if exists "public"."company_content_pkey";

drop index if exists "public"."company_task_statuses_company_id_name_key";

drop index if exists "public"."company_task_statuses_pkey";

drop index if exists "public"."content_personas_content_id_persona_id_key";

drop index if exists "public"."content_personas_pkey";

drop index if exists "public"."feature_usage_account_id_feature_key";

drop index if exists "public"."feature_usage_pkey";

drop index if exists "public"."generated_research_pkey";

drop index if exists "public"."idx_company_content_campaign_idea";

drop index if exists "public"."idx_company_content_images";

drop index if exists "public"."idx_company_content_lookup";

drop index if exists "public"."idx_company_content_video_editor";

drop index if exists "public"."idx_company_task_statuses_company_id";

drop index if exists "public"."idx_feature_usage_account_id";

drop index if exists "public"."idx_generated_research_account_id";

drop index if exists "public"."idx_generated_research_created_at";

drop index if exists "public"."idx_generated_research_icp_persona";

drop index if exists "public"."idx_generated_research_topic";

drop index if exists "public"."idx_saved_research_account_id";

drop index if exists "public"."idx_saved_research_icp_persona";

drop index if exists "public"."idx_saved_research_topic";

drop index if exists "public"."linkedInState_pkey";

drop index if exists "public"."linkedInState_user_id_key";

drop index if exists "public"."onboarding_account_id_key";

drop index if exists "public"."onboarding_pkey";

drop index if exists "public"."personas_company_id_idx";

drop index if exists "public"."personas_icp_id_idx";

drop index if exists "public"."personas_name_idx";

drop index if exists "public"."personas_pkey";

drop index if exists "public"."personas_status_idx";

drop index if exists "public"."product_documents_pkey";

drop index if exists "public"."saved_research_account_id_icp_id_persona_id_title_research__key";

drop index if exists "public"."saved_research_pkey";

drop index if exists "public"."twitterState_pkey";

drop index if exists "public"."twitterState_user_id_key";

drop table "public"."ayrshare_user_profile";

drop table "public"."campaign_ideas";

drop table "public"."campaign_personas";

drop table "public"."company_brand";

drop table "public"."company_campaigns";

drop table "public"."company_content";

drop table "public"."company_task_statuses";

drop table "public"."content_personas";

drop table "public"."feature_usage";

drop table "public"."generated_research";

drop table "public"."linkedInState";

drop table "public"."onboarding";

drop table "public"."personas";

drop table "public"."product_documents";

drop table "public"."saved_research";

drop table "public"."twitterState";

create table "public"."nonces" (
    "id" uuid not null default gen_random_uuid(),
    "client_token" text not null,
    "nonce" text not null,
    "user_id" uuid,
    "purpose" text not null,
    "expires_at" timestamp with time zone not null,
    "created_at" timestamp with time zone not null default now(),
    "used_at" timestamp with time zone,
    "revoked" boolean not null default false,
    "revoked_reason" text,
    "verification_attempts" integer not null default 0,
    "last_verification_at" timestamp with time zone,
    "last_verification_ip" inet,
    "last_verification_user_agent" text,
    "metadata" jsonb default '{}'::jsonb,
    "scopes" text[] default '{}'::text[]
);


alter table "public"."nonces" enable row level security;

create table "public"."social_profiles" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "user_id" uuid not null,
    "title" text not null,
    "ref_id" text not null,
    "profile_key" text not null,
    "messaging_active" boolean default false,
    "permissions" jsonb default '{"can_post": false, "can_view": true, "can_analytics": false}'::jsonb,
    "is_shared" boolean default false,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."social_profiles" enable row level security;

alter table "public"."icps" enable row level security;

drop type "public"."campaign_status";

CREATE INDEX idx_nonces_status ON public.nonces USING btree (client_token, user_id, purpose, expires_at) WHERE ((used_at IS NULL) AND (revoked = false));

CREATE UNIQUE INDEX nonces_pkey ON public.nonces USING btree (id);

CREATE INDEX social_profiles_account_id_idx ON public.social_profiles USING btree (account_id);

CREATE UNIQUE INDEX social_profiles_pkey ON public.social_profiles USING btree (id);

CREATE INDEX social_profiles_profile_key_idx ON public.social_profiles USING btree (profile_key);

CREATE INDEX social_profiles_shared_idx ON public.social_profiles USING btree (account_id, is_shared) WHERE (is_shared = true);

CREATE INDEX social_profiles_user_id_idx ON public.social_profiles USING btree (user_id);

CREATE UNIQUE INDEX unique_user_account_title ON public.social_profiles USING btree (user_id, account_id, title);

alter table "public"."nonces" add constraint "nonces_pkey" PRIMARY KEY using index "nonces_pkey";

alter table "public"."social_profiles" add constraint "social_profiles_pkey" PRIMARY KEY using index "social_profiles_pkey";

alter table "public"."nonces" add constraint "nonces_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."nonces" validate constraint "nonces_user_id_fkey";

alter table "public"."social_profiles" add constraint "social_profiles_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."social_profiles" validate constraint "social_profiles_account_id_fkey";

alter table "public"."social_profiles" add constraint "social_profiles_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."social_profiles" validate constraint "social_profiles_user_id_fkey";

alter table "public"."social_profiles" add constraint "unique_user_account_title" UNIQUE using index "unique_user_account_title";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_nonce(p_user_id uuid DEFAULT NULL::uuid, p_purpose text DEFAULT NULL::text, p_expires_in_seconds integer DEFAULT 3600, p_metadata jsonb DEFAULT NULL::jsonb, p_scopes text[] DEFAULT NULL::text[], p_revoke_previous boolean DEFAULT true)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_client_token TEXT;
    v_nonce TEXT;
    v_expires_at TIMESTAMPTZ;
    v_id UUID;
    v_plaintext_token TEXT;
    v_revoked_count INTEGER;
BEGIN
    -- Revoke previous tokens for the same user and purpose if requested
    -- This only applies if a user ID is provided (not for anonymous tokens)
    IF p_revoke_previous = TRUE AND p_user_id IS NOT NULL THEN
        WITH revoked AS (
            UPDATE public.nonces
                SET
                    revoked = TRUE,
                    revoked_reason = 'Superseded by new token with same purpose'
                WHERE
                    user_id = p_user_id
                        AND purpose = p_purpose
                        AND used_at IS NULL
                        AND revoked = FALSE
                        AND expires_at > NOW()
                RETURNING 1
        )
        SELECT COUNT(*) INTO v_revoked_count FROM revoked;
    END IF;

    -- Generate a 6-digit token
    v_plaintext_token := (100000 + floor(random() * 900000))::text;
    v_client_token := extensions.crypt(v_plaintext_token, extensions.gen_salt('bf'));

    -- Still generate a secure nonce for internal use
    v_nonce := encode(extensions.gen_random_bytes(24), 'base64');
    v_nonce := extensions.crypt(v_nonce, extensions.gen_salt('bf'));

    -- Calculate expiration time
    v_expires_at := NOW() + (p_expires_in_seconds * interval '1 second');

    -- Insert the new nonce
    INSERT INTO public.nonces (
        client_token,
        nonce,
        user_id,
        expires_at,
        metadata,
        purpose,
        scopes
    )
    VALUES (
               v_client_token,
               v_nonce,
               p_user_id,
               v_expires_at,
               COALESCE(p_metadata, '{}'::JSONB),
               p_purpose,
               COALESCE(p_scopes, '{}'::TEXT[])
           )
    RETURNING id INTO v_id;

    -- Return the token information
    -- Note: returning the plaintext token, not the hash
    RETURN jsonb_build_object(
            'id', v_id,
            'token', v_plaintext_token,
            'expires_at', v_expires_at,
            'revoked_previous_count', COALESCE(v_revoked_count, 0)
           );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_nonce_status(p_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  v_nonce public.nonces;
BEGIN
  SELECT * INTO v_nonce FROM public.nonces WHERE id = p_id;

  IF v_nonce.id IS NULL THEN
    RETURN jsonb_build_object('exists', false);
  END IF;

  RETURN jsonb_build_object(
    'exists', true,
    'purpose', v_nonce.purpose,
    'user_id', v_nonce.user_id,
    'created_at', v_nonce.created_at,
    'expires_at', v_nonce.expires_at,
    'used_at', v_nonce.used_at,
    'revoked', v_nonce.revoked,
    'revoked_reason', v_nonce.revoked_reason,
    'verification_attempts', v_nonce.verification_attempts,
    'last_verification_at', v_nonce.last_verification_at,
    'last_verification_ip', v_nonce.last_verification_ip,
    'is_valid', (v_nonce.used_at IS NULL AND NOT v_nonce.revoked AND v_nonce.expires_at > NOW())
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_aal2()
 RETURNS boolean
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    is_aal2 boolean;
begin
    select auth.jwt() ->> 'aal' = 'aal2' into is_aal2;

    return coalesce(is_aal2, false);
end
$function$
;

CREATE OR REPLACE FUNCTION public.is_mfa_compliant()
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
begin
    return array[(select auth.jwt()->>'aal')] <@ (
        select
            case
                when count(id) > 0 then array['aal2']
                else array['aal1', 'aal2']
                end as aal
        from auth.mfa_factors
        where ((select auth.uid()) = auth.mfa_factors.user_id) and auth.mfa_factors.status = 'verified'
    );
end
$function$
;

CREATE OR REPLACE FUNCTION public.is_super_admin()
 RETURNS boolean
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    is_super_admin boolean;
begin
    if not public.is_aal2() then
        return false;
    end if;

    select (auth.jwt() ->> 'app_metadata')::jsonb ->> 'role' = 'super-admin' into is_super_admin;

    return coalesce(is_super_admin, false);
end
$function$
;

CREATE OR REPLACE FUNCTION public.revoke_nonce(p_id uuid, p_reason text DEFAULT NULL::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  v_affected_rows INTEGER;
BEGIN
  UPDATE public.nonces
  SET
    revoked = TRUE,
    revoked_reason = p_reason
  WHERE
    id = p_id
    AND used_at IS NULL
    AND NOT revoked
  RETURNING 1 INTO v_affected_rows;

  RETURN v_affected_rows > 0;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.verify_nonce(p_token text, p_purpose text, p_user_id uuid DEFAULT NULL::uuid, p_required_scopes text[] DEFAULT NULL::text[], p_max_verification_attempts integer DEFAULT 5, p_ip inet DEFAULT NULL::inet, p_user_agent text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_nonce          RECORD;
    v_matching_count INTEGER;
BEGIN
    -- Count how many matching tokens exist before verification attempt
    SELECT COUNT(*)
    INTO v_matching_count
    FROM public.nonces
    WHERE purpose = p_purpose;

    -- Update verification attempt counter and tracking info for all matching tokens
    UPDATE public.nonces
    SET verification_attempts        = verification_attempts + 1,
        last_verification_at         = NOW(),
        last_verification_ip         = COALESCE(p_ip, last_verification_ip),
        last_verification_user_agent = COALESCE(p_user_agent, last_verification_user_agent)
    WHERE client_token = extensions.crypt(p_token, client_token)
      AND purpose = p_purpose;

    -- Find the nonce by token and purpose
    -- Modified to handle user-specific tokens better
    SELECT *
    INTO v_nonce
    FROM public.nonces
    WHERE client_token = extensions.crypt(p_token, client_token)
      AND purpose = p_purpose
      -- Only apply user_id filter if the token was created for a specific user
      AND (
        -- Case 1: Anonymous token (user_id is NULL in DB)
        (user_id IS NULL)
            OR
            -- Case 2: User-specific token (check if user_id matches)
        (user_id = p_user_id)
        )
      AND used_at IS NULL
      AND NOT revoked
      AND expires_at > NOW();

    -- Check if nonce exists
    IF v_nonce.id IS NULL THEN
        RETURN jsonb_build_object(
                'valid', false,
                'message', 'Invalid or expired token'
               );
    END IF;

    -- Check if max verification attempts exceeded
    IF p_max_verification_attempts > 0 AND v_nonce.verification_attempts > p_max_verification_attempts THEN
        -- Automatically revoke the token
        UPDATE public.nonces
        SET revoked        = TRUE,
            revoked_reason = 'Maximum verification attempts exceeded'
        WHERE id = v_nonce.id;

        RETURN jsonb_build_object(
                'valid', false,
                'message', 'Token revoked due to too many verification attempts',
                'max_attempts_exceeded', true
               );
    END IF;

    -- Check scopes if required
    IF p_required_scopes IS NOT NULL AND array_length(p_required_scopes, 1) > 0 THEN
        -- Fix scope validation to properly check if token scopes contain all required scopes
        -- Using array containment check: array1 @> array2 (array1 contains array2)
        IF NOT (v_nonce.scopes @> p_required_scopes) THEN
            RETURN jsonb_build_object(
                    'valid', false,
                    'message', 'Token does not have required permissions',
                    'token_scopes', v_nonce.scopes,
                    'required_scopes', p_required_scopes
                   );
        END IF;
    END IF;

    -- Mark nonce as used
    UPDATE public.nonces
    SET used_at = NOW()
    WHERE id = v_nonce.id;

    -- Return success with metadata
    RETURN jsonb_build_object(
            'valid', true,
            'user_id', v_nonce.user_id,
            'metadata', v_nonce.metadata,
            'scopes', v_nonce.scopes,
            'purpose', v_nonce.purpose
           );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_social_profiles(target_account_id uuid, target_user_id uuid DEFAULT auth.uid())
 RETURNS TABLE(id uuid, title text, ref_id text, profile_key text, messaging_active boolean, permissions jsonb, is_shared boolean, created_at timestamp with time zone, updated_at timestamp with time zone)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  -- Check if user has access to the account
  IF NOT public.has_role_on_account(target_account_id) THEN
    RAISE EXCEPTION 'Access denied to account';
  END IF;

  RETURN QUERY
  SELECT 
    sp.id,
    sp.title,
    sp.ref_id,
    sp.profile_key,
    sp.messaging_active,
    sp.permissions,
    sp.is_shared,
    sp.created_at,
    sp.updated_at
  FROM public.social_profiles sp
  WHERE sp.account_id = target_account_id 
    AND (
      sp.user_id = target_user_id OR 
      (sp.is_shared = true AND public.has_role_on_account(target_account_id))
    )
  ORDER BY sp.created_at DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.toggle_social_profile_sharing(profile_id uuid, share_with_team boolean DEFAULT true)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  profile_owner UUID;
  profile_account UUID;
BEGIN
  -- Get profile owner and account
  SELECT user_id, account_id INTO profile_owner, profile_account
  FROM public.social_profiles
  WHERE id = profile_id;

  -- Check if current user is the profile owner
  IF profile_owner != auth.uid() THEN
    RAISE EXCEPTION 'Only profile owner can change sharing settings';
  END IF;

  -- Check if user has role on the account
  IF NOT public.has_role_on_account(profile_account) THEN
    RAISE EXCEPTION 'Access denied to account';
  END IF;

  -- Update sharing status
  UPDATE public.social_profiles
  SET 
    is_shared = share_with_team,
    updated_at = now()
  WHERE id = profile_id;

  RETURN share_with_team;
END;
$function$
;

grant delete on table "public"."nonces" to "anon";

grant insert on table "public"."nonces" to "anon";

grant references on table "public"."nonces" to "anon";

grant select on table "public"."nonces" to "anon";

grant trigger on table "public"."nonces" to "anon";

grant truncate on table "public"."nonces" to "anon";

grant update on table "public"."nonces" to "anon";

grant delete on table "public"."nonces" to "authenticated";

grant insert on table "public"."nonces" to "authenticated";

grant references on table "public"."nonces" to "authenticated";

grant select on table "public"."nonces" to "authenticated";

grant trigger on table "public"."nonces" to "authenticated";

grant truncate on table "public"."nonces" to "authenticated";

grant update on table "public"."nonces" to "authenticated";

grant delete on table "public"."nonces" to "service_role";

grant insert on table "public"."nonces" to "service_role";

grant references on table "public"."nonces" to "service_role";

grant select on table "public"."nonces" to "service_role";

grant trigger on table "public"."nonces" to "service_role";

grant truncate on table "public"."nonces" to "service_role";

grant update on table "public"."nonces" to "service_role";

grant delete on table "public"."social_profiles" to "anon";

grant insert on table "public"."social_profiles" to "anon";

grant references on table "public"."social_profiles" to "anon";

grant select on table "public"."social_profiles" to "anon";

grant trigger on table "public"."social_profiles" to "anon";

grant truncate on table "public"."social_profiles" to "anon";

grant update on table "public"."social_profiles" to "anon";

grant delete on table "public"."social_profiles" to "authenticated";

grant insert on table "public"."social_profiles" to "authenticated";

grant references on table "public"."social_profiles" to "authenticated";

grant select on table "public"."social_profiles" to "authenticated";

grant trigger on table "public"."social_profiles" to "authenticated";

grant truncate on table "public"."social_profiles" to "authenticated";

grant update on table "public"."social_profiles" to "authenticated";

grant delete on table "public"."social_profiles" to "service_role";

grant insert on table "public"."social_profiles" to "service_role";

grant references on table "public"."social_profiles" to "service_role";

grant select on table "public"."social_profiles" to "service_role";

grant trigger on table "public"."social_profiles" to "service_role";

grant truncate on table "public"."social_profiles" to "service_role";

grant update on table "public"."social_profiles" to "service_role";

create policy "restrict_mfa_accounts"
on "public"."accounts"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_accounts"
on "public"."accounts"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "restrict_mfa_accounts_memberships"
on "public"."accounts_memberships"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_accounts_memberships"
on "public"."accounts_memberships"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "restrict_mfa_invitations"
on "public"."invitations"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_invitations"
on "public"."invitations"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "Users can read their own nonces"
on "public"."nonces"
as permissive
for select
to public
using ((user_id = ( SELECT auth.uid() AS uid)));


create policy "restrict_mfa_notifications"
on "public"."notifications"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "restrict_mfa_order_items"
on "public"."order_items"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_order_items"
on "public"."order_items"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "restrict_mfa_orders"
on "public"."orders"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_orders"
on "public"."orders"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "restrict_mfa_role_permissions"
on "public"."role_permissions"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_role_permissions"
on "public"."role_permissions"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "social_profiles_delete"
on "public"."social_profiles"
as permissive
for delete
to authenticated
using ((user_id = auth.uid()));


create policy "social_profiles_insert"
on "public"."social_profiles"
as permissive
for insert
to authenticated
with check (((user_id = auth.uid()) AND has_role_on_account(account_id)));


create policy "social_profiles_select"
on "public"."social_profiles"
as permissive
for select
to authenticated
using (((user_id = auth.uid()) OR ((is_shared = true) AND has_role_on_account(account_id))));


create policy "social_profiles_update"
on "public"."social_profiles"
as permissive
for update
to authenticated
using (((user_id = auth.uid()) OR ((is_shared = true) AND has_role_on_account(account_id))))
with check (((user_id = auth.uid()) OR ((is_shared = true) AND has_role_on_account(account_id))));


create policy "restrict_mfa_subscription_items"
on "public"."subscription_items"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_subscription_items"
on "public"."subscription_items"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "restrict_mfa_subscriptions"
on "public"."subscriptions"
as restrictive
for all
to authenticated
using (is_mfa_compliant());


create policy "super_admins_access_subscriptions"
on "public"."subscriptions"
as permissive
for select
to authenticated
using (is_super_admin());


create policy "invitations_read_self"
on "public"."invitations"
as permissive
for select
to authenticated
using (has_role_on_account(account_id));


CREATE TRIGGER social_profiles_set_timestamps BEFORE UPDATE ON public.social_profiles FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();


