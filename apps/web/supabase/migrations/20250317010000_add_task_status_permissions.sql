-- Drop and recreate the task_status enum with both case variants
DROP TYPE IF EXISTS task_status CASCADE;
CREATE TYPE task_status AS ENUM (
  'draft', 'Draft',
  'to do', 'To Do',
  'in progress', 'In Progress',
  'done', 'Done'
);

-- Grant permissions to all task status functions
GRANT EXECUTE ON FUNCTION public.is_valid_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_valid_task_status TO service_role;

GRANT EXECUTE ON FUNCTION public.get_company_task_statuses TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_company_task_statuses TO service_role;

GRANT EXECUTE ON FUNCTION public.add_company_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_company_task_status TO service_role;

GRANT EXECUTE ON FUNCTION public.update_company_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_company_task_status TO service_role;

GRANT EXECUTE ON FUNCTION public.reorder_company_task_statuses TO authenticated;
GRANT EXECUTE ON FUNCTION public.reorder_company_task_statuses TO service_role;

GRANT EXECUTE ON FUNCTION public.delete_company_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_company_task_status TO service_role;

-- Recreate the is_valid_task_status function to be case-insensitive
CREATE OR REPLACE FUNCTION public.is_valid_task_status(p_company_id UUID, p_status TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  try_status TEXT;
BEGIN
  -- Handle NULL or empty status
  IF p_status IS NULL OR p_status = '' THEN
    RETURN TRUE;
  END IF;
  
  -- Try both the original case and lowercase
  BEGIN
    -- First try as-is
    try_status := p_status;
    PERFORM try_status::task_status;
    RETURN TRUE;
  EXCEPTION WHEN invalid_text_representation THEN
    -- Then try lowercase
    BEGIN
      try_status := lower(p_status);
      PERFORM try_status::task_status;
      RETURN TRUE;
    EXCEPTION WHEN invalid_text_representation THEN
      -- Not a valid enum value, check custom statuses
      RETURN EXISTS (
        SELECT 1 FROM public.company_task_statuses
        WHERE company_id = p_company_id 
        AND (name = p_status OR name = lower(p_status))
      );
    END;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the constraint to avoid permission errors
ALTER TABLE public.company_content DROP CONSTRAINT IF EXISTS check_valid_status;
ALTER TABLE public.company_content ADD CONSTRAINT check_valid_status
  CHECK (status IS NULL OR status = '' OR public.is_valid_task_status(company_id, status));