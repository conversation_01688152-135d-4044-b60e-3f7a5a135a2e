-- Drop the task_status enum since we'll handle all statuses through the table
DROP TYPE IF EXISTS task_status CASCADE;

-- Modify the is_valid_task_status function to only check the company_task_statuses table
CREATE OR REPLACE FUNCTION public.is_valid_task_status(p_company_id UUID, p_status TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Handle NULL or empty status
  IF p_status IS NULL OR p_status = '' THEN
    RETURN TRUE;
  END IF;
  
  -- Check if status exists in company_task_statuses
  RETURN EXISTS (
    SELECT 1 FROM public.company_task_statuses
    WHERE company_id = p_company_id 
    AND (name = p_status OR name = lower(p_status))
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.is_valid_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_valid_task_status TO service_role;

-- Drop and recreate the constraint
ALTER TABLE public.company_content DROP CONSTRAINT IF EXISTS check_valid_status;
ALTER TABLE public.company_content ADD CONSTRAINT check_valid_status
  CHECK (status IS NULL OR status = '' OR public.is_valid_task_status(company_id, status)); 