-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "updated_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "name" text not null,
    "description" text,
    "target_audience" jsonb default '[]'::jsonb,
    "key_features" jsonb default '[]'::jsonb,
    "company_id" uuid not null,
    "custom_fields" jsonb default '{}'::jsonb
);

-- Add primary key constraint
ALTER TABLE public.products ADD CONSTRAINT products_pkey PRIMARY KEY (id);

-- Add foreign key constraint to accounts table
ALTER TABLE public.products ADD CONSTRAINT products_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.accounts(id) ON DELETE CASCADE;

-- Enable Row Level Security
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for products table
CREATE POLICY "products_read" ON public.products FOR SELECT
  TO authenticated USING (
    public.has_role_on_account(company_id)
  );

CREATE POLICY "products_insert" ON public.products FOR INSERT
  TO authenticated WITH CHECK (
    public.has_role_on_account(company_id)
  );

CREATE POLICY "products_update" ON public.products FOR UPDATE
  TO authenticated USING (
    public.has_role_on_account(company_id)
  ) WITH CHECK (
    public.has_role_on_account(company_id)
  );

CREATE POLICY "products_delete" ON public.products FOR DELETE
  TO authenticated USING (
    public.has_role_on_account(company_id)
  );

-- Add product_id column to product_documents table (nullable for backwards compatibility)
ALTER TABLE public.product_documents ADD COLUMN IF NOT EXISTS "product_id" uuid;

-- Add foreign key constraint from product_documents to products
ALTER TABLE public.product_documents ADD CONSTRAINT product_documents_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id) ON DELETE CASCADE;

-- Update RLS policies for product_documents to include product_id access
DROP POLICY IF EXISTS "product_documents_read" ON public.product_documents;
DROP POLICY IF EXISTS "product_documents_insert" ON public.product_documents;
DROP POLICY IF EXISTS "product_documents_update" ON public.product_documents;
DROP POLICY IF EXISTS "product_documents_delete" ON public.product_documents;

-- Recreate RLS policies for product_documents with product_id support
CREATE POLICY "product_documents_read" ON public.product_documents FOR SELECT
  TO authenticated USING (
    public.has_role_on_account(company_id) OR
    (product_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.products 
      WHERE products.id = product_documents.product_id 
      AND public.has_role_on_account(products.company_id)
    ))
  );

CREATE POLICY "product_documents_insert" ON public.product_documents FOR INSERT
  TO authenticated WITH CHECK (
    public.has_role_on_account(company_id) OR
    (product_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.products 
      WHERE products.id = product_documents.product_id 
      AND public.has_role_on_account(products.company_id)
    ))
  );

CREATE POLICY "product_documents_update" ON public.product_documents FOR UPDATE
  TO authenticated USING (
    public.has_role_on_account(company_id) OR
    (product_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.products 
      WHERE products.id = product_documents.product_id 
      AND public.has_role_on_account(products.company_id)
    ))
  ) WITH CHECK (
    public.has_role_on_account(company_id) OR
    (product_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.products 
      WHERE products.id = product_documents.product_id 
      AND public.has_role_on_account(products.company_id)
    ))
  );

CREATE POLICY "product_documents_delete" ON public.product_documents FOR DELETE
  TO authenticated USING (
    public.has_role_on_account(company_id) OR
    (product_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.products 
      WHERE products.id = product_documents.product_id 
      AND public.has_role_on_account(products.company_id)
    ))
  );

-- Add comments to document the schema
COMMENT ON TABLE public.products IS 'Products managed by companies for marketing content creation';
COMMENT ON COLUMN public.products.target_audience IS 'Array of ICP IDs that this product targets';
COMMENT ON COLUMN public.products.key_features IS 'Array of key product features';
COMMENT ON COLUMN public.products.custom_fields IS 'Additional custom fields for product metadata';
COMMENT ON COLUMN public.product_documents.product_id IS 'Optional foreign key to associate document with specific product';