create table "public"."socials_research" (
    "created_at" timestamp with time zone not null default now(),
    "results" jsonb,
    "platform" text,
    "is_generating" boolean,
    "error_generating" boolean,
    "keywords" jsonb,
    "company_id" uuid,
    "id" uuid not null default gen_random_uuid()
);


CREATE UNIQUE INDEX socials_research_pkey ON public.socials_research USING btree (id);

alter table "public"."socials_research" add constraint "socials_research_pkey" PRIMARY KEY using index "socials_research_pkey";

alter table "public"."socials_research" add constraint "socials_research_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."socials_research" validate constraint "socials_research_company_id_fkey";

grant delete on table "public"."socials_research" to "anon";

grant insert on table "public"."socials_research" to "anon";

grant references on table "public"."socials_research" to "anon";

grant select on table "public"."socials_research" to "anon";

grant trigger on table "public"."socials_research" to "anon";

grant truncate on table "public"."socials_research" to "anon";

grant update on table "public"."socials_research" to "anon";

grant delete on table "public"."socials_research" to "authenticated";

grant insert on table "public"."socials_research" to "authenticated";

grant references on table "public"."socials_research" to "authenticated";

grant select on table "public"."socials_research" to "authenticated";

grant trigger on table "public"."socials_research" to "authenticated";

grant truncate on table "public"."socials_research" to "authenticated";

grant update on table "public"."socials_research" to "authenticated";

grant delete on table "public"."socials_research" to "service_role";

grant insert on table "public"."socials_research" to "service_role";

grant references on table "public"."socials_research" to "service_role";

grant select on table "public"."socials_research" to "service_role";

grant trigger on table "public"."socials_research" to "service_role";

grant truncate on table "public"."socials_research" to "service_role";

grant update on table "public"."socials_research" to "service_role";


