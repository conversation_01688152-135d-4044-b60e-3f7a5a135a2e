alter table "public"."company_campaigns" add column "error_generating" boolean;

alter table "public"."company_campaigns" add column "updated_at" date;

alter table "public"."icps" add column "error_generating" boolean;

alter table "public"."icps" add column "is_generating" boolean;

alter table "public"."icps" alter column "name" drop not null;

alter table "public"."personas" add column "data" jsonb;

alter table "public"."personas" add column "error_generating" boolean;

alter table "public"."personas" add column "is_generating" boolean;

alter table "public"."personas" alter column "name" drop not null;

alter table "public"."personas" alter column "role" drop not null;

CREATE TRIGGER accounts_teardown AFTER DELETE ON public.accounts FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('http://host.docker.internal:3000/api/db/webhook', 'POST', '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}', '{}', '5000');

CREATE TRIGGER invitations_insert AFTER INSERT ON public.invitations FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('http://host.docker.internal:3000/api/db/webhook', 'POST', '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}', '{}', '5000');

CREATE TRIGGER subscriptions_delete AFTER DELETE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('http://host.docker.internal:3000/api/db/webhook', 'POST', '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}', '{}', '5000');


create schema if not exists "zero";

create table "zero"."permissions" (
    "permissions" jsonb,
    "hash" text,
    "lock" boolean not null default true
);


create table "zero"."schemaVersions" (
    "minSupportedVersion" integer,
    "maxSupportedVersion" integer,
    "lock" boolean not null default true
);


CREATE UNIQUE INDEX permissions_pkey ON zero.permissions USING btree (lock);

CREATE UNIQUE INDEX "schemaVersions_pkey" ON zero."schemaVersions" USING btree (lock);

alter table "zero"."permissions" add constraint "permissions_pkey" PRIMARY KEY using index "permissions_pkey";

alter table "zero"."schemaVersions" add constraint "schemaVersions_pkey" PRIMARY KEY using index "schemaVersions_pkey";

alter table "zero"."permissions" add constraint "permissions_lock_check" CHECK (lock) not valid;

alter table "zero"."permissions" validate constraint "permissions_lock_check";

alter table "zero"."schemaVersions" add constraint "schemaVersions_lock_check" CHECK (lock) not valid;

alter table "zero"."schemaVersions" validate constraint "schemaVersions_lock_check";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION zero.set_permissions_hash()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
  BEGIN
      NEW.hash = md5(NEW.permissions::text);
      RETURN NEW;
  END;
  $function$
;

CREATE TRIGGER on_set_permissions BEFORE INSERT OR UPDATE ON zero.permissions FOR EACH ROW EXECUTE FUNCTION zero.set_permissions_hash();


create schema if not exists "zero_0";

create table "zero_0"."clients" (
    "clientGroupID" text not null,
    "clientID" text not null,
    "lastMutationID" bigint not null,
    "userID" text
);


create table "zero_0"."replicas" (
    "slot" text not null,
    "version" text not null,
    "initialSchema" json not null
);


create table "zero_0"."shardConfig" (
    "publications" text[] not null,
    "ddlDetection" boolean not null,
    "lock" boolean not null default true
);


create table "zero_0"."versionHistory" (
    "dataVersion" integer not null,
    "schemaVersion" integer not null,
    "minSafeVersion" integer not null,
    "lock" character(1) not null default 'v'::bpchar
);


CREATE UNIQUE INDEX clients_pkey ON zero_0.clients USING btree ("clientGroupID", "clientID");

CREATE UNIQUE INDEX pk_schema_meta_lock ON zero_0."versionHistory" USING btree (lock);

CREATE UNIQUE INDEX replicas_pkey ON zero_0.replicas USING btree (slot);

CREATE UNIQUE INDEX "shardConfig_pkey" ON zero_0."shardConfig" USING btree (lock);

alter table "zero_0"."clients" add constraint "clients_pkey" PRIMARY KEY using index "clients_pkey";

alter table "zero_0"."replicas" add constraint "replicas_pkey" PRIMARY KEY using index "replicas_pkey";

alter table "zero_0"."shardConfig" add constraint "shardConfig_pkey" PRIMARY KEY using index "shardConfig_pkey";

alter table "zero_0"."versionHistory" add constraint "pk_schema_meta_lock" PRIMARY KEY using index "pk_schema_meta_lock";

alter table "zero_0"."shardConfig" add constraint "shardConfig_lock_check" CHECK (lock) not valid;

alter table "zero_0"."shardConfig" validate constraint "shardConfig_lock_check";

alter table "zero_0"."versionHistory" add constraint "ck_schema_meta_lock" CHECK ((lock = 'v'::bpchar)) not valid;

alter table "zero_0"."versionHistory" validate constraint "ck_schema_meta_lock";


create schema if not exists "zero_0/cdc";

create table "zero_0/cdc"."changeLog" (
    "watermark" text not null,
    "pos" bigint not null,
    "change" json not null,
    "precommit" text
);


create table "zero_0/cdc"."replicationConfig" (
    "replicaVersion" text not null,
    "publications" text[] not null,
    "resetRequired" boolean,
    "lock" integer not null default 1
);


create table "zero_0/cdc"."replicationState" (
    "lastWatermark" text not null,
    "owner" text,
    "ownerAddress" text,
    "lock" integer not null default 1
);


create table "zero_0/cdc"."versionHistory" (
    "dataVersion" integer not null,
    "schemaVersion" integer not null,
    "minSafeVersion" integer not null,
    "lock" character(1) not null default 'v'::bpchar
);


CREATE UNIQUE INDEX "changeLog_pkey" ON "zero_0/cdc"."changeLog" USING btree (watermark, pos);

CREATE UNIQUE INDEX pk_schema_meta_lock ON "zero_0/cdc"."versionHistory" USING btree (lock);

CREATE UNIQUE INDEX "replicationConfig_pkey" ON "zero_0/cdc"."replicationConfig" USING btree (lock);

CREATE UNIQUE INDEX "replicationState_pkey" ON "zero_0/cdc"."replicationState" USING btree (lock);

alter table "zero_0/cdc"."changeLog" add constraint "changeLog_pkey" PRIMARY KEY using index "changeLog_pkey";

alter table "zero_0/cdc"."replicationConfig" add constraint "replicationConfig_pkey" PRIMARY KEY using index "replicationConfig_pkey";

alter table "zero_0/cdc"."replicationState" add constraint "replicationState_pkey" PRIMARY KEY using index "replicationState_pkey";

alter table "zero_0/cdc"."versionHistory" add constraint "pk_schema_meta_lock" PRIMARY KEY using index "pk_schema_meta_lock";

alter table "zero_0/cdc"."replicationConfig" add constraint "replicationConfig_lock_check" CHECK ((lock = 1)) not valid;

alter table "zero_0/cdc"."replicationConfig" validate constraint "replicationConfig_lock_check";

alter table "zero_0/cdc"."replicationState" add constraint "replicationState_lock_check" CHECK ((lock = 1)) not valid;

alter table "zero_0/cdc"."replicationState" validate constraint "replicationState_lock_check";

alter table "zero_0/cdc"."versionHistory" add constraint "ck_schema_meta_lock" CHECK ((lock = 'v'::bpchar)) not valid;

alter table "zero_0/cdc"."versionHistory" validate constraint "ck_schema_meta_lock";


create schema if not exists "zero_0/cvr";

create table "zero_0/cvr"."clients" (
    "clientGroupID" text not null,
    "clientID" text not null,
    "patchVersion" text not null,
    "deleted" boolean
);


create table "zero_0/cvr"."desires" (
    "clientGroupID" text not null,
    "clientID" text not null,
    "queryHash" text not null,
    "patchVersion" text not null,
    "deleted" boolean,
    "ttl" interval,
    "expiresAt" timestamp with time zone,
    "inactivatedAt" timestamp with time zone
);


create table "zero_0/cvr"."instances" (
    "clientGroupID" text not null,
    "version" text not null,
    "lastActive" timestamp with time zone not null,
    "replicaVersion" text,
    "owner" text,
    "grantedAt" timestamp with time zone,
    "clientSchema" jsonb
);


create table "zero_0/cvr"."queries" (
    "clientGroupID" text not null,
    "queryHash" text not null,
    "clientAST" jsonb not null,
    "patchVersion" text,
    "transformationHash" text,
    "transformationVersion" text,
    "internal" boolean,
    "deleted" boolean
);


create table "zero_0/cvr"."rows" (
    "clientGroupID" text not null,
    "schema" text not null,
    "table" text not null,
    "rowKey" jsonb not null,
    "rowVersion" text not null,
    "patchVersion" text not null,
    "refCounts" jsonb
);


create table "zero_0/cvr"."rowsVersion" (
    "clientGroupID" text not null,
    "version" text not null
);


create table "zero_0/cvr"."versionHistory" (
    "dataVersion" integer not null,
    "schemaVersion" integer not null,
    "minSafeVersion" integer not null,
    "lock" character(1) not null default 'v'::bpchar
);


CREATE INDEX client_patch_version ON "zero_0/cvr".clients USING btree ("patchVersion");

CREATE UNIQUE INDEX clients_pkey ON "zero_0/cvr".clients USING btree ("clientGroupID", "clientID");

CREATE INDEX desires_expires_at ON "zero_0/cvr".desires USING btree ("expiresAt");

CREATE INDEX desires_inactivated_at ON "zero_0/cvr".desires USING btree ("inactivatedAt");

CREATE INDEX desires_patch_version ON "zero_0/cvr".desires USING btree ("patchVersion");

CREATE UNIQUE INDEX desires_pkey ON "zero_0/cvr".desires USING btree ("clientGroupID", "clientID", "queryHash");

CREATE UNIQUE INDEX instances_pkey ON "zero_0/cvr".instances USING btree ("clientGroupID");

CREATE UNIQUE INDEX pk_schema_meta_lock ON "zero_0/cvr"."versionHistory" USING btree (lock);

CREATE INDEX queries_patch_version ON "zero_0/cvr".queries USING btree ("patchVersion" NULLS FIRST);

CREATE UNIQUE INDEX queries_pkey ON "zero_0/cvr".queries USING btree ("clientGroupID", "queryHash");

CREATE INDEX row_patch_version ON "zero_0/cvr".rows USING btree ("patchVersion");

CREATE INDEX row_ref_counts ON "zero_0/cvr".rows USING gin ("refCounts");

CREATE UNIQUE INDEX "rowsVersion_pkey" ON "zero_0/cvr"."rowsVersion" USING btree ("clientGroupID");

CREATE UNIQUE INDEX rows_pkey ON "zero_0/cvr".rows USING btree ("clientGroupID", schema, "table", "rowKey");

alter table "zero_0/cvr"."clients" add constraint "clients_pkey" PRIMARY KEY using index "clients_pkey";

alter table "zero_0/cvr"."desires" add constraint "desires_pkey" PRIMARY KEY using index "desires_pkey";

alter table "zero_0/cvr"."instances" add constraint "instances_pkey" PRIMARY KEY using index "instances_pkey";

alter table "zero_0/cvr"."queries" add constraint "queries_pkey" PRIMARY KEY using index "queries_pkey";

alter table "zero_0/cvr"."rows" add constraint "rows_pkey" PRIMARY KEY using index "rows_pkey";

alter table "zero_0/cvr"."rowsVersion" add constraint "rowsVersion_pkey" PRIMARY KEY using index "rowsVersion_pkey";

alter table "zero_0/cvr"."versionHistory" add constraint "pk_schema_meta_lock" PRIMARY KEY using index "pk_schema_meta_lock";

alter table "zero_0/cvr"."clients" add constraint "fk_clients_client_group" FOREIGN KEY ("clientGroupID") REFERENCES "zero_0/cvr".instances("clientGroupID") not valid;

alter table "zero_0/cvr"."clients" validate constraint "fk_clients_client_group";

alter table "zero_0/cvr"."desires" add constraint "fk_desires_query" FOREIGN KEY ("clientGroupID", "queryHash") REFERENCES "zero_0/cvr".queries("clientGroupID", "queryHash") ON DELETE CASCADE not valid;

alter table "zero_0/cvr"."desires" validate constraint "fk_desires_query";

alter table "zero_0/cvr"."queries" add constraint "fk_queries_client_group" FOREIGN KEY ("clientGroupID") REFERENCES "zero_0/cvr".instances("clientGroupID") not valid;

alter table "zero_0/cvr"."queries" validate constraint "fk_queries_client_group";

alter table "zero_0/cvr"."versionHistory" add constraint "ck_schema_meta_lock" CHECK ((lock = 'v'::bpchar)) not valid;

alter table "zero_0/cvr"."versionHistory" validate constraint "ck_schema_meta_lock";


