create table "public"."company_campaigns" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "company_id" uuid not null,
    "user_id" uuid not null,
    "name" text not null,
    "slug" text not null,
    "messaging" text,
    "value_prop" text,
    "objective" text,
    "identity" text,
    "kpis" text,
    "objectives" text,
    "guidelines" text,
    "personas" text,
    "personality" text,
    "targetAudience" text,
    "tone" text,
    "visualStyle" text,
    "voice" text,
    "documents" jsonb
);


CREATE UNIQUE INDEX company_campaigns_pkey ON public.company_campaigns USING btree (id);

CREATE UNIQUE INDEX company_campaigns_slug_key ON public.company_campaigns USING btree (slug);

alter table "public"."company_campaigns" add constraint "company_campaigns_pkey" PRIMARY KEY using index "company_campaigns_pkey";

alter table "public"."company_campaigns" add constraint "company_campaigns_company_id_fkey" FOREIGN KEY (company_id) REFERENCES company_campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."company_campaigns" validate constraint "company_campaigns_company_id_fkey";

alter table "public"."company_campaigns" add constraint "company_campaigns_slug_key" UNIQUE using index "company_campaigns_slug_key";

grant delete on table "public"."company_campaigns" to "anon";

grant insert on table "public"."company_campaigns" to "anon";

grant references on table "public"."company_campaigns" to "anon";

grant select on table "public"."company_campaigns" to "anon";

grant trigger on table "public"."company_campaigns" to "anon";

grant truncate on table "public"."company_campaigns" to "anon";

grant update on table "public"."company_campaigns" to "anon";

grant delete on table "public"."company_campaigns" to "authenticated";

grant insert on table "public"."company_campaigns" to "authenticated";

grant references on table "public"."company_campaigns" to "authenticated";

grant select on table "public"."company_campaigns" to "authenticated";

grant trigger on table "public"."company_campaigns" to "authenticated";

grant truncate on table "public"."company_campaigns" to "authenticated";

grant update on table "public"."company_campaigns" to "authenticated";

grant delete on table "public"."company_campaigns" to "service_role";

grant insert on table "public"."company_campaigns" to "service_role";

grant references on table "public"."company_campaigns" to "service_role";

grant select on table "public"."company_campaigns" to "service_role";

grant trigger on table "public"."company_campaigns" to "service_role";

grant truncate on table "public"."company_campaigns" to "service_role";

grant update on table "public"."company_campaigns" to "service_role";

-- Enable RLS
alter table "public"."company_campaigns" enable row level security;

-- RLS policies for company_campaigns table
-- SELECT policy: Allow team members to select company_campaigns records
create policy "team_members_can_select_company_campaigns" on public.company_campaigns
for select using (
  public.has_role_on_account(company_id)
);

-- INSERT policy: Allow team members to insert company_campaigns records
create policy "team_members_can_insert_company_campaigns" on public.company_campaigns
for insert with check (
  public.has_role_on_account(company_id)
);

-- UPDATE policy: Allow team members to update company_campaigns records
create policy "team_members_can_update_company_campaigns" on public.company_campaigns
for update using (
  public.has_role_on_account(company_id)
) with check (
  public.has_role_on_account(company_id)
);

-- DELETE policy: Allow team members to delete company_campaigns records
create policy "team_members_can_delete_company_campaigns" on public.company_campaigns
for delete using (
  public.has_role_on_account(company_id)
);


