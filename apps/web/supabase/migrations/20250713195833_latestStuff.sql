alter table "public"."ayrshare_social_profiles" drop constraint "ayrshare_social_profiles_company_id_fkey";

alter table "public"."ayrshare_social_profiles" drop column "active_social_accounts";

alter table "public"."ayrshare_social_profiles" drop column "display_names";

alter table "public"."ayrshare_social_profiles" add column "connected_at" timestamp with time zone default now();

alter table "public"."ayrshare_social_profiles" add column "display_name" text;

alter table "public"."ayrshare_social_profiles" add column "headline" text;

alter table "public"."ayrshare_social_profiles" add column "is_connected" boolean default true;

alter table "public"."ayrshare_social_profiles" add column "platform" text not null;

alter table "public"."ayrshare_social_profiles" add column "profile_key" text;

alter table "public"."ayrshare_social_profiles" add column "profile_url" text;

alter table "public"."ayrshare_social_profiles" add column "refresh_days_remaining" integer;

alter table "public"."ayrshare_social_profiles" add column "refresh_required" timestamp with time zone;

alter table "public"."ayrshare_social_profiles" add column "subscription_type" text;

alter table "public"."ayrshare_social_profiles" add column "updated_at" timestamp with time zone default now();

alter table "public"."ayrshare_social_profiles" add column "user_image" text;

alter table "public"."ayrshare_social_profiles" add column "username" text;

alter table "public"."ayrshare_social_profiles" add column "verified_type" text;

alter table "public"."ayrshare_social_profiles" disable row level security;

alter table "public"."company_campaigns" add column "ayrshare_post_id" text;

alter table "public"."company_content" drop column "published_at";

alter table "public"."company_content" add column "assigned_to" uuid;

alter table "public"."company_content" add column "ayrshare_post_id" text;

alter table "public"."company_content" add column "ayrshare_post_status" text;

alter table "public"."company_content" add column "is_paused" boolean default false;

alter table "public"."company_content" add column "schedule_date" timestamp with time zone;

alter table "public"."company_content" add column "scheduled_at" timestamp with time zone;

alter table "public"."user_cache" add column "task_list_columns" jsonb;

CREATE INDEX ayrshare_social_profiles_connected_idx ON public.ayrshare_social_profiles USING btree (is_connected);

CREATE INDEX ayrshare_social_profiles_platform_idx ON public.ayrshare_social_profiles USING btree (platform);

CREATE UNIQUE INDEX ayrshare_social_profiles_profile_platform_unique ON public.ayrshare_social_profiles USING btree ("refId", platform, user_id, company_id);

CREATE INDEX ayrshare_social_profiles_user_company_idx ON public.ayrshare_social_profiles USING btree (user_id, company_id);

CREATE UNIQUE INDEX "ayrshare_user_profile_profileKey_key" ON public.ayrshare_user_profile USING btree ("profileKey");

CREATE INDEX idx_company_content_ayrshare_post_id ON public.company_content USING btree (ayrshare_post_id) WHERE (ayrshare_post_id IS NOT NULL);

CREATE INDEX idx_company_content_scheduled ON public.company_content USING btree (is_scheduled, schedule_date) WHERE (is_scheduled = true);

alter table "public"."ayrshare_social_profiles" add constraint "ayrshare_social_profiles_profile_key_fkey" FOREIGN KEY (profile_key) REFERENCES ayrshare_user_profile("profileKey") ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."ayrshare_social_profiles" validate constraint "ayrshare_social_profiles_profile_key_fkey";

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_profileKey_key" UNIQUE using index "ayrshare_user_profile_profileKey_key";

alter table "public"."company_content" add constraint "company_content_assigned_to_fkey" FOREIGN KEY (assigned_to) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."company_content" validate constraint "company_content_assigned_to_fkey";

alter table "public"."ayrshare_social_profiles" add constraint "ayrshare_social_profiles_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE CASCADE NOT VALID not valid;

alter table "public"."ayrshare_social_profiles" validate constraint "ayrshare_social_profiles_company_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.trigger_set_timestamps()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$
;

CREATE TRIGGER set_ayrshare_social_profiles_updated_at BEFORE UPDATE ON public.ayrshare_social_profiles FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();