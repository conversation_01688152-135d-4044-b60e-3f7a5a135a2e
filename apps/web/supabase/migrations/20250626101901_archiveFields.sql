create table "public"."ayrshare_social_profiles" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "active_social_accounts" json,
    "display_names" jsonb,
    "refId" character varying,
    "is_shared" boolean default false,
    "user_id" uuid,
    "company_id" uuid
);


alter table "public"."ayrshare_social_profiles" enable row level security;

create table "public"."post_templates" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "title" character varying not null,
    "description" character varying,
    "style" jsonb,
    "image_url" character varying,
    "content_type" text default ''::text,
    "channel" text
);


alter table "public"."post_templates" enable row level security;

alter table "public"."ayrshare_user_profile" alter column "company_id" set not null;

alter table "public"."company_campaigns" add column "target_icps" jsonb;

alter table "public"."company_campaigns" add column "target_personas" jsonb;

alter table "public"."company_content" add column "archived" boolean default false;

alter table "public"."company_content" add column "image_urls" jsonb;

alter table "public"."generated_research" alter column "persona_id" drop not null;

alter table "public"."icps" add column "reference_description" text;

alter table "public"."icps" add column "reference_material" jsonb;

alter table "public"."saved_research" add column "archived" boolean default false;

alter table "public"."saved_research" alter column "persona_id" drop not null;

CREATE UNIQUE INDEX ayrshare_social_profiles_pkey ON public.ayrshare_social_profiles USING btree (id);

CREATE UNIQUE INDEX post_templates_pkey ON public.post_templates USING btree (id);

alter table "public"."ayrshare_social_profiles" add constraint "ayrshare_social_profiles_pkey" PRIMARY KEY using index "ayrshare_social_profiles_pkey";

alter table "public"."post_templates" add constraint "post_templates_pkey" PRIMARY KEY using index "post_templates_pkey";

alter table "public"."ayrshare_social_profiles" add constraint "ayrshare_social_profiles_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."ayrshare_social_profiles" validate constraint "ayrshare_social_profiles_company_id_fkey";

alter table "public"."ayrshare_social_profiles" add constraint "ayrshare_social_profiles_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."ayrshare_social_profiles" validate constraint "ayrshare_social_profiles_user_id_fkey";

grant delete on table "public"."ayrshare_social_profiles" to "anon";

grant insert on table "public"."ayrshare_social_profiles" to "anon";

grant references on table "public"."ayrshare_social_profiles" to "anon";

grant select on table "public"."ayrshare_social_profiles" to "anon";

grant trigger on table "public"."ayrshare_social_profiles" to "anon";

grant truncate on table "public"."ayrshare_social_profiles" to "anon";

grant update on table "public"."ayrshare_social_profiles" to "anon";

grant delete on table "public"."ayrshare_social_profiles" to "authenticated";

grant insert on table "public"."ayrshare_social_profiles" to "authenticated";

grant references on table "public"."ayrshare_social_profiles" to "authenticated";

grant select on table "public"."ayrshare_social_profiles" to "authenticated";

grant trigger on table "public"."ayrshare_social_profiles" to "authenticated";

grant truncate on table "public"."ayrshare_social_profiles" to "authenticated";

grant update on table "public"."ayrshare_social_profiles" to "authenticated";

grant delete on table "public"."ayrshare_social_profiles" to "service_role";

grant insert on table "public"."ayrshare_social_profiles" to "service_role";

grant references on table "public"."ayrshare_social_profiles" to "service_role";

grant select on table "public"."ayrshare_social_profiles" to "service_role";

grant trigger on table "public"."ayrshare_social_profiles" to "service_role";

grant truncate on table "public"."ayrshare_social_profiles" to "service_role";

grant update on table "public"."ayrshare_social_profiles" to "service_role";

grant delete on table "public"."post_templates" to "anon";

grant insert on table "public"."post_templates" to "anon";

grant references on table "public"."post_templates" to "anon";

grant select on table "public"."post_templates" to "anon";

grant trigger on table "public"."post_templates" to "anon";

grant truncate on table "public"."post_templates" to "anon";

grant update on table "public"."post_templates" to "anon";

grant delete on table "public"."post_templates" to "authenticated";

grant insert on table "public"."post_templates" to "authenticated";

grant references on table "public"."post_templates" to "authenticated";

grant select on table "public"."post_templates" to "authenticated";

grant trigger on table "public"."post_templates" to "authenticated";

grant truncate on table "public"."post_templates" to "authenticated";

grant update on table "public"."post_templates" to "authenticated";

grant delete on table "public"."post_templates" to "service_role";

grant insert on table "public"."post_templates" to "service_role";

grant references on table "public"."post_templates" to "service_role";

grant select on table "public"."post_templates" to "service_role";

grant trigger on table "public"."post_templates" to "service_role";

grant truncate on table "public"."post_templates" to "service_role";

grant update on table "public"."post_templates" to "service_role";


