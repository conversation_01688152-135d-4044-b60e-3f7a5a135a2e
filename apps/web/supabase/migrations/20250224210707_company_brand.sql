create table "public"."company_brand" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "user_id" uuid,
    "company_id" uuid not null,
    "mission" text,
    "vision" text,
    "value_proposition" text,
    "audience" text,
    "personality" text,
    "messaging_pillars" text,
    "identity" text,
    "guidelines" text,
    "voice" text,
    "brand_colors" jsonb,
    "has_brand_setup" boolean,
    "product_list" text,
    "updated_at" timestamp without time zone,
    "brand_fonts" jsonb
);

alter table "public"."company_brand" enable row level security;

CREATE UNIQUE INDEX company_brand_pkey ON public.company_brand USING btree (id);

alter table "public"."company_brand" add constraint "company_brand_pkey" PRIMARY KEY using index "company_brand_pkey";

alter table "public"."company_brand" add constraint "company_brand_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."company_brand" validate constraint "company_brand_company_id_fkey";

-- RLS policies for company_brand table
-- SELECT policy: Allow team members to select company_brand records
create policy "team_members_can_select_company_brand" on public.company_brand
for select using (
  public.has_role_on_account(company_id)
);

-- INSERT policy: Allow team members to insert company_brand records
create policy "team_members_can_insert_company_brand" on public.company_brand
for insert with check (
  public.has_role_on_account(company_id)
);

-- UPDATE policy: Allow team members to update company_brand records
create policy "team_members_can_update_company_brand" on public.company_brand
for update using (
  public.has_role_on_account(company_id)
) with check (
  public.has_role_on_account(company_id)
);

grant delete on table "public"."company_brand" to "anon";

grant insert on table "public"."company_brand" to "anon";

grant references on table "public"."company_brand" to "anon";

grant select on table "public"."company_brand" to "anon";

grant trigger on table "public"."company_brand" to "anon";

grant truncate on table "public"."company_brand" to "anon";

grant update on table "public"."company_brand" to "anon";

grant delete on table "public"."company_brand" to "authenticated";

grant insert on table "public"."company_brand" to "authenticated";

grant references on table "public"."company_brand" to "authenticated";

grant select on table "public"."company_brand" to "authenticated";

grant trigger on table "public"."company_brand" to "authenticated";

grant truncate on table "public"."company_brand" to "authenticated";

grant update on table "public"."company_brand" to "authenticated";

grant delete on table "public"."company_brand" to "service_role";

grant insert on table "public"."company_brand" to "service_role";

grant references on table "public"."company_brand" to "service_role";

grant select on table "public"."company_brand" to "service_role";

grant trigger on table "public"."company_brand" to "service_role";

grant truncate on table "public"."company_brand" to "service_role";

grant update on table "public"."company_brand" to "service_role";
