alter table "public"."linkedin_uris" drop constraint "linkedin_urispkey";

drop index if exists "public"."linkedin_urispkey";

alter table "public"."ayrshare_social_profiles" add column "post_history" jsonb;

alter table "public"."company_brand" drop column "audience";

alter table "public"."company_brand" drop column "brand_colors";

alter table "public"."company_brand" drop column "brand_fonts";

alter table "public"."company_brand" drop column "guidelines";

alter table "public"."company_brand" drop column "identity";

alter table "public"."company_brand" drop column "is_draft";

alter table "public"."company_brand" drop column "messaging_pillars";

alter table "public"."company_brand" drop column "mission";

alter table "public"."company_brand" drop column "personality";

alter table "public"."company_brand" drop column "product_list";

alter table "public"."company_brand" drop column "value_proposition";

alter table "public"."company_brand" drop column "vision";

alter table "public"."company_brand" drop column "voice";

alter table "public"."company_brand" add column "brand_profile" jsonb;

alter table "public"."company_brand" add column "error_generating" boolean;

alter table "public"."company_brand" add column "is_generating" boolean;

alter table "public"."company_brand" add column "messaging_strategy" jsonb;

alter table "public"."company_brand" add column "product_catalog" jsonb;

alter table "public"."company_brand" add column "prompt_library" jsonb;

alter table "public"."company_brand" add column "visual_identity" jsonb;

alter table "public"."company_content" add column "is_published" boolean;

alter table "public"."company_content" add column "published_at" date;

alter table "public"."company_content" add column "published_by" uuid;

alter table "public"."company_content" add column "published_url" text;

alter table "public"."icps" add column "linkedInUrls" jsonb;

alter table "public"."icps" add column "withAi" boolean;

alter table "public"."icps" add column "withLinkedIn" boolean;

alter table "public"."linkedin_uris" add column "uri" text;

CREATE UNIQUE INDEX "linked_in_URIS_pkey" ON public.linkedin_uris USING btree (id);

alter table "public"."linkedin_uris" add constraint "linked_in_URIS_pkey" PRIMARY KEY using index "linked_in_URIS_pkey";




