create table "public"."site_research" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "icps" jsonb,
    "personal" jsonb,
    "urls" jsonb,
    "instruction" text,
    "schema" jsonb,
    "enable_web_search" boolean,
    "agent_mode" boolean,
    "is_generating" boolean,
    "error_generating" boolean,
    "results" jsonb,
    "company_id" uuid not null
);


alter table "public"."site_research" enable row level security;

CREATE UNIQUE INDEX site_research_pkey ON public.site_research USING btree (id);

alter table "public"."site_research" add constraint "site_research_pkey" PRIMARY KEY using index "site_research_pkey";

alter table "public"."site_research" add constraint "site_research_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."site_research" validate constraint "site_research_company_id_fkey";

grant delete on table "public"."site_research" to "anon";

grant insert on table "public"."site_research" to "anon";

grant references on table "public"."site_research" to "anon";

grant select on table "public"."site_research" to "anon";

grant trigger on table "public"."site_research" to "anon";

grant truncate on table "public"."site_research" to "anon";

grant update on table "public"."site_research" to "anon";

grant delete on table "public"."site_research" to "authenticated";

grant insert on table "public"."site_research" to "authenticated";

grant references on table "public"."site_research" to "authenticated";

grant select on table "public"."site_research" to "authenticated";

grant trigger on table "public"."site_research" to "authenticated";

grant truncate on table "public"."site_research" to "authenticated";

grant update on table "public"."site_research" to "authenticated";

grant delete on table "public"."site_research" to "service_role";

grant insert on table "public"."site_research" to "service_role";

grant references on table "public"."site_research" to "service_role";

grant select on table "public"."site_research" to "service_role";

grant trigger on table "public"."site_research" to "service_role";

grant truncate on table "public"."site_research" to "service_role";

grant update on table "public"."site_research" to "service_role";




