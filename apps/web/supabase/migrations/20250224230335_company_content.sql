create table "public"."company_content" (
    "id" uuid not null default gen_random_uuid(),
    "campaign_id" uuid,
    "company_id" uuid,
    "idea_id" uuid,
    "content_type" text,
    "language" text,
    "content" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "image_path" text,
    "content_template" text,
    "image_url" text,
    "channel" text,
    "has_image" boolean default false,
    "visual_description" text,
    "avatar_script" text,
    "avatar_voice_id" text,
    "avatar_presenter_id" text,
    "avatar_video_id" text,
    "avatar_video_url" text,
    "is_avatar_ready" boolean,
    "has_avatar" boolean,
    "is_generating" boolean,
    "status" text default ''::text,
    "task_description" text,
    "task_id" text,
    "task_title" text,
    "has_video_presentation" boolean,
    "video_presentation_url" text,
    "video_presentation_render_params" jsonb,
    "video_presentation_script" text,
    "seo_keywords" jsonb,
    "trend_keywords" jsonb
);

-- RLS policies for company_content table
-- SELECT policy: Allow team members to select company_content records
create policy "team_members_can_select_company_content" on public.company_content
for select using (
  public.has_role_on_account(company_id)
);

-- INSERT policy: Allow team members to insert company_content records
create policy "team_members_can_insert_company_content" on public.company_content
for insert with check (
  public.has_role_on_account(company_id)
);

-- UPDATE policy: Allow team members to update company_content records
create policy "team_members_can_update_company_content" on public.company_content
for update using (
  public.has_role_on_account(company_id)
) with check (
  public.has_role_on_account(company_id)
);

CREATE UNIQUE INDEX company_content_pkey ON public.company_content USING btree (id);

CREATE INDEX idx_company_content_campaign_idea ON public.company_content USING btree (campaign_id, idea_id);

CREATE INDEX idx_company_content_images ON public.company_content USING btree (image_url) WHERE (image_url IS NOT NULL);

CREATE INDEX idx_company_content_lookup ON public.company_content USING btree (campaign_id, idea_id, content_type, language);

alter table "public"."company_content" add constraint "company_content_pkey" PRIMARY KEY using index "company_content_pkey";

alter table "public"."campaign_ideas" add constraint "campaign_ideas_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."campaign_ideas" validate constraint "campaign_ideas_company_id_fkey";

alter table "public"."company_content" add constraint "company_content_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES company_campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."company_content" validate constraint "company_content_campaign_id_fkey";

alter table "public"."company_content" add constraint "company_content_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."company_content" validate constraint "company_content_company_id_fkey";

alter table "public"."company_content" add constraint "company_content_idea_id_fkey" FOREIGN KEY (idea_id) REFERENCES campaign_ideas(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."company_content" validate constraint "company_content_idea_id_fkey";

grant delete on table "public"."company_content" to "anon";

grant insert on table "public"."company_content" to "anon";

grant references on table "public"."company_content" to "anon";

grant select on table "public"."company_content" to "anon";

grant trigger on table "public"."company_content" to "anon";

grant truncate on table "public"."company_content" to "anon";

grant update on table "public"."company_content" to "anon";

grant delete on table "public"."company_content" to "authenticated";

grant insert on table "public"."company_content" to "authenticated";

grant references on table "public"."company_content" to "authenticated";

grant select on table "public"."company_content" to "authenticated";

grant trigger on table "public"."company_content" to "authenticated";

grant truncate on table "public"."company_content" to "authenticated";

grant update on table "public"."company_content" to "authenticated";

grant delete on table "public"."company_content" to "service_role";

grant insert on table "public"."company_content" to "service_role";

grant references on table "public"."company_content" to "service_role";

grant select on table "public"."company_content" to "service_role";

grant trigger on table "public"."company_content" to "service_role";

grant truncate on table "public"."company_content" to "service_role";

grant update on table "public"."company_content" to "service_role";
