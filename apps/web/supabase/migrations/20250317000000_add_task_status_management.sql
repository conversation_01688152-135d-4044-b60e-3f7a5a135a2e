-- Create default task status enum
DROP TYPE IF EXISTS task_status CASCADE;

CREATE TYPE task_status AS ENUM (
  'draft', 'Draft',
  'to do', 'To Do',
  'in progress', 'In Progress',
  'done', 'Done'
);

-- Create company_task_statuses table to store custom statuses per company
CREATE TABLE IF NOT EXISTS public.company_task_statuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  status_order INT NOT NULL,
  color TEXT,
  icon TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  
  -- Ensure unique status names within a company
  UNIQUE (company_id, name)
);

-- Add RLS policies
ALTER TABLE public.company_task_statuses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "team_members_can_select_company_task_statuses" ON public.company_task_statuses
FOR SELECT USING (
  public.has_role_on_account(company_id)
);

CREATE POLICY "team_members_can_insert_company_task_statuses" ON public.company_task_statuses
FOR INSERT WITH CHECK (
  public.has_role_on_account(company_id)
);

CREATE POLICY "team_members_can_update_company_task_statuses" ON public.company_task_statuses
FOR UPDATE USING (
  public.has_role_on_account(company_id)
) WITH CHECK (
  public.has_role_on_account(company_id)
);

CREATE POLICY "team_members_can_delete_company_task_statuses" ON public.company_task_statuses
FOR DELETE USING (
  public.has_role_on_account(company_id)
);

-- Ensure basic permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.company_task_statuses TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.company_task_statuses TO service_role;

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_company_task_statuses_company_id ON public.company_task_statuses(company_id);

-- Function to initialize default statuses for a company
CREATE OR REPLACE FUNCTION public.initialize_company_task_statuses(p_company_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Only insert if no statuses exist for this company
  IF NOT EXISTS (SELECT 1 FROM public.company_task_statuses WHERE company_id = p_company_id) THEN
    INSERT INTO public.company_task_statuses (company_id, name, display_name, status_order, color, icon)
    VALUES
      (p_company_id, 'draft', 'Draft', 1, '#E2E8F0', 'Circle'),
      (p_company_id, 'to do', 'To Do', 2, '#3B82F6', 'Circle'),
      (p_company_id, 'in progress', 'In Progress', 3, '#F59E0B', 'Clock'),
      (p_company_id, 'done', 'Done', 4, '#10B981', 'Check');
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.initialize_company_task_statuses TO authenticated;
GRANT EXECUTE ON FUNCTION public.initialize_company_task_statuses TO service_role;

-- Trigger to initialize statuses when a new company is created
CREATE OR REPLACE FUNCTION public.init_task_statuses_for_new_company()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM public.initialize_company_task_statuses(NEW.id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.init_task_statuses_for_new_company TO authenticated;
GRANT EXECUTE ON FUNCTION public.init_task_statuses_for_new_company TO service_role;

-- Create trigger for new company accounts
DROP TRIGGER IF EXISTS init_task_statuses_trigger ON public.accounts;
CREATE TRIGGER init_task_statuses_trigger
AFTER INSERT ON public.accounts
FOR EACH ROW
WHEN (NOT NEW.is_personal_account)
EXECUTE FUNCTION public.init_task_statuses_for_new_company();

-- Initialize existing companies with default statuses
DO $$
DECLARE
  company_record RECORD;
BEGIN
  FOR company_record IN SELECT id FROM public.accounts WHERE NOT is_personal_account LOOP
    PERFORM public.initialize_company_task_statuses(company_record.id);
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Add validation function to check if a status name is valid for a company
CREATE OR REPLACE FUNCTION public.is_valid_task_status(p_company_id UUID, p_status TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  try_status TEXT;
BEGIN
  -- Handle NULL or empty status
  IF p_status IS NULL OR p_status = '' THEN
    RETURN TRUE;
  END IF;
  
  -- Try both the original case and lowercase
  BEGIN
    -- First try as-is
    try_status := p_status;
    PERFORM try_status::task_status;
    RETURN TRUE;
  EXCEPTION WHEN invalid_text_representation THEN
    -- Then try lowercase
    BEGIN
      try_status := lower(p_status);
      PERFORM try_status::task_status;
      RETURN TRUE;
    EXCEPTION WHEN invalid_text_representation THEN
      -- Not a valid enum value, check custom statuses
      RETURN EXISTS (
        SELECT 1 FROM public.company_task_statuses
        WHERE company_id = p_company_id 
        AND (name = p_status OR name = lower(p_status))
      );
    END;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.is_valid_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_valid_task_status TO service_role;

-- Create function to get all statuses for a company
CREATE OR REPLACE FUNCTION public.get_company_task_statuses(p_company_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  display_name TEXT,
  status_order INT,
  color TEXT,
  icon TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cts.id,
    cts.name,
    cts.display_name,
    cts.status_order,
    cts.color,
    cts.icon
  FROM 
    public.company_task_statuses cts
  WHERE 
    cts.company_id = p_company_id
  ORDER BY 
    cts.status_order;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_company_task_statuses TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_company_task_statuses TO service_role;

-- Create function to add a new status for a company
CREATE OR REPLACE FUNCTION public.add_company_task_status(
  p_company_id UUID,
  p_name TEXT,
  p_display_name TEXT,
  p_color TEXT DEFAULT '#E2E8F0',
  p_icon TEXT DEFAULT 'Circle'
)
RETURNS UUID AS $$
DECLARE
  new_position INT;
  new_status_id UUID;
BEGIN
  -- Get the next status_order
  SELECT COALESCE(MAX(status_order), 0) + 1 INTO new_position
  FROM public.company_task_statuses
  WHERE company_id = p_company_id;
  
  -- Insert new status
  INSERT INTO public.company_task_statuses (
    company_id, name, display_name, status_order, color, icon
  ) VALUES (
    p_company_id, LOWER(p_name), p_display_name, new_position, p_color, p_icon
  )
  RETURNING id INTO new_status_id;
  
  RETURN new_status_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.add_company_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_company_task_status TO service_role;

-- Function to update a status
CREATE OR REPLACE FUNCTION public.update_company_task_status(
  p_status_id UUID,
  p_display_name TEXT DEFAULT NULL,
  p_status_order INT DEFAULT NULL,
  p_color TEXT DEFAULT NULL,
  p_icon TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  update_query TEXT;
  params_count INT := 0;
BEGIN
  update_query := 'UPDATE public.company_task_statuses SET ';
  
  -- Build dynamic update query based on non-null parameters
  IF p_display_name IS NOT NULL THEN
    update_query := update_query || 'display_name = $1';
    params_count := params_count + 1;
  END IF;
  
  IF p_status_order IS NOT NULL THEN
    IF params_count > 0 THEN update_query := update_query || ', '; END IF;
    update_query := update_query || 'status_order = $' || (params_count + 1)::TEXT;
    params_count := params_count + 1;
  END IF;
  
  IF p_color IS NOT NULL THEN
    IF params_count > 0 THEN update_query := update_query || ', '; END IF;
    update_query := update_query || 'color = $' || (params_count + 1)::TEXT;
    params_count := params_count + 1;
  END IF;
  
  IF p_icon IS NOT NULL THEN
    IF params_count > 0 THEN update_query := update_query || ', '; END IF;
    update_query := update_query || 'icon = $' || (params_count + 1)::TEXT;
    params_count := params_count + 1;
  END IF;
  
  -- If no parameters were provided, return false
  IF params_count = 0 THEN
    RETURN FALSE;
  END IF;
  
  -- Complete the query with the WHERE clause
  update_query := update_query || ' WHERE id = $' || (params_count + 1)::TEXT;
  
  -- Execute the dynamic query
  EXECUTE update_query USING 
    p_display_name, 
    p_status_order, 
    p_color, 
    p_icon, 
    p_status_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.update_company_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_company_task_status TO service_role;

-- Function to reorder statuses
CREATE OR REPLACE FUNCTION public.reorder_company_task_statuses(
  p_company_id UUID,
  p_status_ids UUID[]
)
RETURNS BOOLEAN AS $$
DECLARE
  i INT;
  status_id UUID;
BEGIN
  -- Validate all status IDs belong to the company
  IF EXISTS (
    SELECT 1 FROM UNNEST(p_status_ids) AS s(id)
    LEFT JOIN public.company_task_statuses cts ON cts.id = s.id AND cts.company_id = p_company_id
    WHERE cts.id IS NULL
  ) THEN
    RAISE EXCEPTION 'One or more status IDs do not belong to this company';
    RETURN FALSE;
  END IF;
  
  -- Update status_order
  FOR i IN 1..array_length(p_status_ids, 1) LOOP
    status_id := p_status_ids[i];
    UPDATE public.company_task_statuses
    SET status_order = i
    WHERE id = status_id AND company_id = p_company_id;
  END LOOP;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.reorder_company_task_statuses TO authenticated;
GRANT EXECUTE ON FUNCTION public.reorder_company_task_statuses TO service_role;

-- Function to delete a status
CREATE OR REPLACE FUNCTION public.delete_company_task_status(
  p_company_id UUID,
  p_status_id UUID,
  p_replacement_status TEXT DEFAULT 'draft'
)
RETURNS BOOLEAN AS $$
DECLARE
  deleted_status_order INT;
  deleted_status_name TEXT;
BEGIN
  -- Check if replacement status exists for this company
  IF NOT public.is_valid_task_status(p_company_id, p_replacement_status) THEN
    RAISE EXCEPTION 'Replacement status is not valid for this company';
    RETURN FALSE;
  END IF;
  
  -- Get the status_order and name of the status to delete
  SELECT status_order, name INTO deleted_status_order, deleted_status_name
  FROM public.company_task_statuses
  WHERE id = p_status_id AND company_id = p_company_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Update any content using this status to the replacement status
  UPDATE public.company_content
  SET status = p_replacement_status
  WHERE company_id = p_company_id AND status = deleted_status_name;
  
  -- Delete the status
  DELETE FROM public.company_task_statuses
  WHERE id = p_status_id AND company_id = p_company_id;
  
  -- Reorder remaining statuses
  UPDATE public.company_task_statuses
  SET status_order = status_order - 1
  WHERE company_id = p_company_id AND status_order > deleted_status_order;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.delete_company_task_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_company_task_status TO service_role;

-- Drop and recreate the constraint to avoid permission errors
ALTER TABLE public.company_content DROP CONSTRAINT IF EXISTS check_valid_status;
ALTER TABLE public.company_content ADD CONSTRAINT check_valid_status
  CHECK (status IS NULL OR status = '' OR public.is_valid_task_status(company_id, status));