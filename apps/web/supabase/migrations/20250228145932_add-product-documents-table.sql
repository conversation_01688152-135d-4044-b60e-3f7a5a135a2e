CREATE TABLE IF NOT EXISTS "public"."product_documents" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "title" "text" NOT NULL,
    "file_path" "text",
    "company_id" "uuid" NOT NULL,
    "content" "text",
    "file_type" "text" NOT NULL
);

ALTER TABLE "public"."product_documents" OWNER TO "postgres";
ALTER TABLE ONLY "public"."product_documents" ADD CONSTRAINT "product_documents_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."product_documents" ADD CONSTRAINT "product_documents_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."accounts"("id");

GRANT ALL ON TABLE "public"."product_documents" TO "anon";
GRANT ALL ON TABLE "public"."product_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."product_documents" TO "service_role";
