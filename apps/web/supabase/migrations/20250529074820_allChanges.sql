drop policy "ayrshare_user_profile_delete" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_insert" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_select" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_update" on "public"."ayrshare_user_profile";

alter table "public"."ayrshare_user_profile" drop constraint "ayrshare_user_profile_pkey";

drop index if exists "public"."ayrshare_user_profile_user_company_name_idx";

drop index if exists "public"."ayrshare_user_profile_user_id_idx";

drop index if exists "public"."ayrshare_user_profile_pkey";

alter table "public"."ayrshare_user_profile" add column "is_shared" boolean default false;

alter table "public"."ayrshare_user_profile" add column "permissions" jsonb default '{"can_post": false, "can_view": true, "can_analytics": false}'::jsonb;

alter table "public"."ayrshare_user_profile" add column "updated_at" timestamp with time zone not null default now();

CREATE UNIQUE INDEX ayrshare_user_profile_pkey ON public.ayrshare_user_profile USING btree (id);

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_pkey" PRIMARY KEY using index "ayrshare_user_profile_pkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_user_social_profiles(target_account_id uuid, target_user_id uuid DEFAULT auth.uid())
 RETURNS TABLE(id uuid, title text, "refId" text, "profileKey" text, "messagingActive" boolean, permissions jsonb, is_shared boolean, created_at timestamp with time zone, updated_at timestamp with time zone)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  -- Check if user has access to the account
  IF NOT public.has_role_on_account(target_account_id) THEN
    RAISE EXCEPTION 'Access denied to account';
  END IF;

  RETURN QUERY
  SELECT 
    aup.id,
    aup.title,
    aup."refId",
    aup."profileKey",
    aup."messagingActive",
    aup.permissions,
    aup.is_shared,
    aup.created_at,
    aup.updated_at
  FROM public.ayrshare_user_profile aup
  WHERE aup.company_id = target_account_id 
    AND (
      aup.user_id = target_user_id OR 
      (aup.is_shared = true AND public.has_role_on_account(target_account_id))
    )
  ORDER BY aup.created_at DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.toggle_social_profile_sharing(profile_id uuid, share_with_team boolean DEFAULT true)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  profile_owner UUID;
  profile_account UUID;
BEGIN
  -- Get profile owner and account
  SELECT user_id, company_id INTO profile_owner, profile_account
  FROM public.ayrshare_user_profile
  WHERE id = profile_id;

  -- Check if current user is the profile owner
  IF profile_owner != auth.uid() THEN
    RAISE EXCEPTION 'Only profile owner can change sharing settings';
  END IF;

  -- Check if user has role on the account
  IF NOT public.has_role_on_account(profile_account) THEN
    RAISE EXCEPTION 'Access denied to account';
  END IF;

  -- Update sharing status
  UPDATE public.ayrshare_user_profile
  SET 
    is_shared = share_with_team,
    updated_at = now()
  WHERE id = profile_id;

  RETURN share_with_team;
END;
$function$
;

create policy "ayrshare_user_profile_delete"
on "public"."ayrshare_user_profile"
as permissive
for delete
to authenticated
using ((user_id = auth.uid()));


create policy "ayrshare_user_profile_insert"
on "public"."ayrshare_user_profile"
as permissive
for insert
to authenticated
with check ((user_id = auth.uid()));


create policy "ayrshare_user_profile_select"
on "public"."ayrshare_user_profile"
as permissive
for select
to authenticated
using ((user_id = auth.uid()));


create policy "ayrshare_user_profile_update"
on "public"."ayrshare_user_profile"
as permissive
for update
to authenticated
using ((user_id = auth.uid()))
with check ((user_id = auth.uid()));


CREATE TRIGGER ayrshare_user_profile_set_timestamps BEFORE UPDATE ON public.ayrshare_user_profile FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();


