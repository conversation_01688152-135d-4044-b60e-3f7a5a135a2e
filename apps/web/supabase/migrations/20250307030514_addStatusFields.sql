drop function if exists "kit"."cleanup_expired_nonces"(p_older_than_days integer, p_include_used boolean, p_include_revoked boolean);

DROP TYPE IF EXISTS campaign_status;

CREATE TYPE campaign_status AS ENUM ('Draft', 'Ready', 'In Progress', 'Completed', 'Archived');

drop policy "restrict_mfa_accounts" on "public"."accounts";

drop policy "super_admins_access_accounts" on "public"."accounts";

drop policy "restrict_mfa_accounts_memberships" on "public"."accounts_memberships";

drop policy "super_admins_access_accounts_memberships" on "public"."accounts_memberships";

drop policy "restrict_mfa_invitations" on "public"."invitations";

drop policy "super_admins_access_invitations" on "public"."invitations";

drop policy "Users can read their own nonces" on "public"."nonces";

drop policy "restrict_mfa_notifications" on "public"."notifications";

drop policy "restrict_mfa_order_items" on "public"."order_items";

drop policy "super_admins_access_order_items" on "public"."order_items";

drop policy "restrict_mfa_orders" on "public"."orders";

drop policy "super_admins_access_orders" on "public"."orders";

drop policy "restrict_mfa_role_permissions" on "public"."role_permissions";

drop policy "super_admins_access_role_permissions" on "public"."role_permissions";

drop policy "restrict_mfa_subscription_items" on "public"."subscription_items";

drop policy "super_admins_access_subscription_items" on "public"."subscription_items";

drop policy "restrict_mfa_subscriptions" on "public"."subscriptions";

drop policy "super_admins_access_subscriptions" on "public"."subscriptions";

revoke delete on table "public"."nonces" from "anon";

revoke insert on table "public"."nonces" from "anon";

revoke references on table "public"."nonces" from "anon";

revoke select on table "public"."nonces" from "anon";

revoke trigger on table "public"."nonces" from "anon";

revoke truncate on table "public"."nonces" from "anon";

revoke update on table "public"."nonces" from "anon";

revoke delete on table "public"."nonces" from "authenticated";

revoke insert on table "public"."nonces" from "authenticated";

revoke references on table "public"."nonces" from "authenticated";

revoke select on table "public"."nonces" from "authenticated";

revoke trigger on table "public"."nonces" from "authenticated";

revoke truncate on table "public"."nonces" from "authenticated";

revoke update on table "public"."nonces" from "authenticated";

revoke delete on table "public"."nonces" from "service_role";

revoke insert on table "public"."nonces" from "service_role";

revoke references on table "public"."nonces" from "service_role";

revoke select on table "public"."nonces" from "service_role";

revoke trigger on table "public"."nonces" from "service_role";

revoke truncate on table "public"."nonces" from "service_role";

revoke update on table "public"."nonces" from "service_role";

revoke delete on table "public"."product_documents" from "anon";

revoke insert on table "public"."product_documents" from "anon";

revoke references on table "public"."product_documents" from "anon";

revoke select on table "public"."product_documents" from "anon";

revoke trigger on table "public"."product_documents" from "anon";

revoke truncate on table "public"."product_documents" from "anon";

revoke update on table "public"."product_documents" from "anon";

revoke delete on table "public"."product_documents" from "authenticated";

revoke insert on table "public"."product_documents" from "authenticated";

revoke references on table "public"."product_documents" from "authenticated";

revoke select on table "public"."product_documents" from "authenticated";

revoke trigger on table "public"."product_documents" from "authenticated";

revoke truncate on table "public"."product_documents" from "authenticated";

revoke update on table "public"."product_documents" from "authenticated";

revoke delete on table "public"."product_documents" from "service_role";

revoke insert on table "public"."product_documents" from "service_role";

revoke references on table "public"."product_documents" from "service_role";

revoke select on table "public"."product_documents" from "service_role";

revoke trigger on table "public"."product_documents" from "service_role";

revoke truncate on table "public"."product_documents" from "service_role";

revoke update on table "public"."product_documents" from "service_role";

alter table "public"."nonces" drop constraint "nonces_user_id_fkey";

alter table "public"."product_documents" drop constraint "product_documents_company_id_fkey";

drop function if exists "public"."create_nonce"(p_user_id uuid, p_purpose text, p_expires_in_seconds integer, p_metadata jsonb, p_scopes text[], p_revoke_previous boolean);

drop function if exists "public"."get_nonce_status"(p_id uuid);

drop function if exists "public"."is_aal2"();

drop function if exists "public"."is_mfa_compliant"();

drop function if exists "public"."is_super_admin"();

drop function if exists "public"."revoke_nonce"(p_id uuid, p_reason text);

drop function if exists "public"."verify_nonce"(p_token text, p_purpose text, p_user_id uuid, p_required_scopes text[], p_max_verification_attempts integer, p_ip inet, p_user_agent text);

alter table "public"."nonces" drop constraint "nonces_pkey";

alter table "public"."product_documents" drop constraint "product_documents_pkey";

drop index if exists "public"."idx_nonces_status";

drop index if exists "public"."nonces_pkey";

drop index if exists "public"."product_documents_pkey";

drop table "public"."nonces";

drop table "public"."product_documents";

alter table "public"."company_campaigns" add column "status" campaign_status default 'Draft'::campaign_status;

alter table "public"."company_content" add column "is_draft" boolean default true;

alter table "public"."company_content" enable row level security;


