-- Create ayrshare_user_profile table
CREATE TABLE if not exists public.ayrshare_user_profile (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  title TEXT,
  "refId" TEXT,
  "profileKey" TEXT,
  "messagingActive" BOOLEAN,
  company_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE
);

-- Enable RLS
ALTER TABLE public.ayrshare_user_profile ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "ayrshare_user_profile_select" ON public.ayrshare_user_profile FOR SELECT
  TO authenticated USING (
    user_id = auth.uid() OR
    public.has_role_on_account(company_id)
  );

CREATE POLICY "ayrshare_user_profile_insert" ON public.ayrshare_user_profile FOR INSERT
  TO authenticated WITH CHECK (
    user_id = auth.uid() AND
    public.has_role_on_account(company_id)
  );

CREATE POLICY "ayrshare_user_profile_update" ON public.ayrshare_user_profile FOR UPDATE
  TO authenticated USING (
    user_id = auth.uid() OR
    public.has_role_on_account(company_id)
  ) WITH CHECK (
    user_id = auth.uid() AND
    public.has_role_on_account(company_id)
  );

CREATE POLICY "ayrshare_user_profile_delete" ON public.ayrshare_user_profile FOR DELETE
  TO authenticated USING (
    user_id = auth.uid() OR
    public.has_role_on_account(company_id, 'owner')
  );

-- Create indexes
CREATE INDEX IF NOT EXISTS ayrshare_user_profile_user_id_idx ON public.ayrshare_user_profile(user_id);
CREATE INDEX IF NOT EXISTS ayrshare_user_profile_company_id_idx ON public.ayrshare_user_profile(company_id);
CREATE UNIQUE INDEX IF NOT EXISTS ayrshare_user_profile_user_company_idx ON public.ayrshare_user_profile(user_id, company_id);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.ayrshare_user_profile TO authenticated;
GRANT SELECT ON public.ayrshare_user_profile TO anon; 