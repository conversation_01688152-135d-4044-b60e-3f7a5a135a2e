/**
 * Standardized error handling utilities for frontend
 */

// Error types enum
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  NETWORK = 'NETWORK',
  API = 'API',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT = 'RATE_LIMIT',
  CONTENT_GENERATION = 'CONTENT_GENERATION',
  UI = 'UI',
  UNKNOWN = 'UNKNOWN',
}

// Base error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode?: number;
  public readonly context?: Record<string, any>;
  public readonly timestamp: Date;
  public readonly userMessage?: string; // User-friendly message

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    statusCode?: number,
    context?: Record<string, any>,
    userMessage?: string,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.type = type;
    this.statusCode = statusCode;
    this.context = context;
    this.timestamp = new Date();
    this.userMessage = userMessage;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

// Specific error classes
export class ValidationError extends AppError {
  constructor(
    message: string,
    context?: Record<string, any>,
    userMessage?: string,
  ) {
    super(
      message,
      ErrorType.VALIDATION,
      400,
      context,
      userMessage || 'Please check your input and try again.',
    );
  }
}

export class NetworkError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(
      message,
      ErrorType.NETWORK,
      undefined,
      context,
      'Network error. Please check your connection and try again.',
    );
  }
}

export class APIError extends AppError {
  constructor(
    message: string,
    statusCode?: number,
    context?: Record<string, any>,
  ) {
    const userMessage =
      statusCode === 429
        ? 'Too many requests. Please wait a moment and try again.'
        : statusCode === 401
          ? 'Authentication required. Please log in and try again.'
          : statusCode === 403
            ? 'You do not have permission to perform this action.'
            : 'Server error. Please try again later.';

    super(message, ErrorType.API, statusCode, context, userMessage);
  }
}

export class ContentGenerationError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(
      message,
      ErrorType.CONTENT_GENERATION,
      undefined,
      context,
      'Failed to generate content. Please try again or adjust your settings.',
    );
  }
}

// Error handling utilities
export function isAppError(error: any): error is AppError {
  return error instanceof AppError;
}

// Error logging utility (client-side)
export function logError(error: Error, context?: Record<string, any>): void {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    context,
  };

  if (isAppError(error)) {
    errorInfo.context = {
      ...errorInfo.context,
      type: error.type,
      statusCode: error.statusCode,
      errorContext: error.context,
    };
  }

  console.error('Frontend error occurred:', errorInfo);

  // In production, you might want to send this to an error tracking service
  // Example: Sentry, LogRocket, etc.
}

// Safe async wrapper for UI operations
export async function safeAsync<T>(
  operation: () => Promise<T>,
  errorMessage?: string,
  context?: Record<string, any>,
): Promise<{ data: T | null; error: AppError | null }> {
  try {
    const data = await operation();
    return { data, error: null };
  } catch (error) {
    const appError = isAppError(error)
      ? error
      : new AppError(
          errorMessage || 'Operation failed',
          ErrorType.UNKNOWN,
          undefined,
          {
            originalError:
              error instanceof Error ? error.message : String(error),
            ...context,
          },
        );

    logError(appError, context);
    return { data: null, error: appError };
  }
}

// API response handler
export async function handleAPIResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    let errorData: any = null;

    try {
      errorData = await response.json();
      if (errorData.error?.message) {
        errorMessage = errorData.error.message;
      }
    } catch {
      // If we can't parse the error response, use the default message
    }

    throw new APIError(errorMessage, response.status, {
      url: response.url,
      errorData,
    });
  }

  try {
    return await response.json();
  } catch (error) {
    throw new APIError('Invalid JSON response', 500, {
      url: response.url,
      parseError: error instanceof Error ? error.message : String(error),
    });
  }
}

// Fetch wrapper with error handling
export async function safeFetch<T>(
  url: string,
  options?: RequestInit,
): Promise<{ data: T | null; error: AppError | null }> {
  return safeAsync(
    async () => {
      const response = await fetch(url, options);
      return handleAPIResponse<T>(response);
    },
    `Failed to fetch from ${url}`,
    { url, options },
  );
}

// User-friendly error message getter
export function getUserFriendlyMessage(error: Error): string {
  if (isAppError(error) && error.userMessage) {
    return error.userMessage;
  }

  // Fallback messages based on error type
  if (error.message.includes('fetch')) {
    return 'Network error. Please check your connection and try again.';
  }

  if (error.message.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }

  return 'An unexpected error occurred. Please try again.';
}

// React hook for error handling
export function useErrorHandler() {
  const handleError = (error: Error, context?: Record<string, any>) => {
    logError(error, context);

    // You can integrate with your toast/notification system here
    const message = getUserFriendlyMessage(error);
    console.warn('User-friendly error message:', message);

    return message;
  };

  const handleAsyncError = async <T>(
    operation: () => Promise<T>,
    errorMessage?: string,
    context?: Record<string, any>,
  ) => {
    const { data, error } = await safeAsync(operation, errorMessage, context);

    if (error) {
      const message = handleError(error, context);
      return { data: null, error: message };
    }

    return { data, error: null };
  };

  return { handleError, handleAsyncError };
}

// Error boundary helper
export function getErrorBoundaryInfo(e: Error, errorInfo: any) {
  const info = {
    error: {
      message: e.message,
      stack: e.stack,
      name: e.name,
    },
    errorInfo,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
  };

  if (isAppError(e)) {
    info.error = {
      ...info.error,
      type: e.type,
      statusCode: e.statusCode,
      context: e.context,
      userMessage: e.userMessage,
    };
  }

  return info;
}
