import { Block, BlockNoteEditor, PartialBlock } from '@blocknote/core';

export function convertTextToBlocks(
  text: string,
  type: string,
): PartialBlock[] {
  return text.split('\n').map(
    (line) =>
      ({
        type: type,
        content: [
          {
            type: 'text',
            text: line,
          },
        ],
      }) as PartialBlock,
  );
}

export function createInitialContent(
  editor: BlockNoteEditor,
  title: string,
  content: string,
  imageUrl?: string,
  content_type?: string,
): Block[] {
  const blocks: PartialBlock[] = [];

  // Add title block with content
  blocks.push({
    type: 'heading',
    props: {
      level: 1,
      backgroundColor: 'default',
      textColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: title,
        styles: {},
      },
    ],
  });

  // For blog posts, add image after title if provided
  if (content_type === 'Blog Post' && imageUrl) {
    blocks.push({
      type: 'image',
      props: {
        url: imageUrl,
        caption: '',
      },
    });
  }

  // Add the main content
  const contentBlocks: PartialBlock[] = content.split('\n').map(
    (line) =>
      ({
        type: 'paragraph',
        content: [{ type: 'text', text: line, styles: {} }],
      }) as PartialBlock,
  );
  blocks.push(...contentBlocks);

  // For non-blog posts, add image at the end if provided
  if (content_type !== 'Blog Post' && imageUrl) {
    blocks.push({
      type: 'image',
      props: {
        url: imageUrl,
        caption: '',
      },
    });
  }

  // Replace all content with new blocks
  const existingBlocks = editor.document;
  if (existingBlocks.length > 0) {
    editor.replaceBlocks(existingBlocks, blocks);
  } else {
    editor.insertBlocks(blocks, 'start', 'after');
  }

  return editor.document;
}

export async function createBlogPostBlocks(
  editor: BlockNoteEditor,
  title: string,
  rawContent: string,
  imageUrl?: string,
): Promise<Block[]> {
  const blocks: PartialBlock[] = [];

  // Create the title block with content
  blocks.push({
    type: 'heading',
    props: {
      level: 1,
      backgroundColor: 'default',
      textColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: title,
        styles: {},
      },
    ],
  });

  // If there's an image, add it after the title
  if (imageUrl) {
    blocks.push({
      type: 'image',
      props: {
        url: imageUrl,
        caption: '',
        backgroundColor: 'default',
        name: '',
        showPreview: true,
        textAlignment: 'left',
        previewWidth: 512,
      },
    });
  }

  // Parse the markdown content
  const parsedBlocks = await editor.tryParseMarkdownToBlocks(rawContent);
  const allBlocks = [...blocks, ...parsedBlocks];

  // Replace all content with new blocks
  const existingBlocks = editor.document;
  if (existingBlocks.length > 0) {
    editor.replaceBlocks(existingBlocks, allBlocks);
  } else {
    editor.insertBlocks(allBlocks, 'start', 'after');
  }

  return editor.document;
}

/**
 * Creates a video block compatible with BlockNote
 */
export function createVideoBlock(videoUrl: string): PartialBlock {
  return {
    type: 'video',
    props: {
      url: videoUrl,
      caption: '',
      backgroundColor: 'default',
      showPreview: true,
      textAlignment: 'left',
    },
  } as PartialBlock;
}
