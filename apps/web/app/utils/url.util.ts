/**
 * Checks if the given string is a valid URL.
 * @param url - The URL string to validate.
 * @returns True if the URL is valid, false otherwise.
 */
import {
  isValidDomain,
  isValidUrl,
  normalizeCompanyWebsiteUrl,
} from '@kit/shared/utils';

export { isValidUrl } from '@kit/shared/utils';

/**
 * Validates if a string could be a valid domain name.
 * @param domain - The domain string to validate.
 * @returns True if the domain appears valid, false otherwise.
 */
export { isValidDomain } from '@kit/shared/utils';

/**
 * Normalizes a company website URL to a standard format.
 * Accepts various input formats and converts them to https://www.domain.com
 *
 * @param input - The URL input from the user (can be domain, www.domain, http://domain, etc.)
 * @returns The normalized URL in format https://www.domain.com
 * @throws Error if the input cannot be normalized to a valid URL
 */
export { normalizeCompanyWebsiteUrl } from '@kit/shared/utils';

/**
 * Formats the given URL to ensure it has the correct prefix & validates it.
 * @param url - The URL string to format.
 * @returns The formatted URL.
 * @deprecated Use normalizeCompanyWebsiteUrl for better normalization
 */
export function getFormattedUrl(url: string): string {
  if (!/^https?:\/\//i.test(url)) {
    if (/^www\./i.test(url)) {
      url = `https://${url}`;
    } else {
      url = `https://www.${url}`;
    }
  }

  if (!isValidUrl(url)) {
    throw new Error('Invalid URL');
  }

  return url;
}
