/**
 * Utilities for generating content prompts in the Studio
 */
import { SelectedDocument } from '~/components/document-selector';

export interface PromptGenerationParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: string[];
  personas: any[];
  selectedIcps: string[];
  icps: any[];
  selectedResearch: string[];
  researchItems: any[];
  selectedDocuments: SelectedDocument[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

/**
 * Generates the content brief section of the prompt
 */
function generateContentBrief(
  taskDescription: string,
  selectedCompanyContent: any,
): string {
  return `
    <CONTENT_BRIEF>
        <Channel>${selectedCompanyContent?.channel || 'Not specified'}</Channel>
        <ContentType>${selectedCompanyContent?.content_type || 'Not specified'}</ContentType>
        <Topic>${taskDescription}</Topic>
    </CONTENT_BRIEF>
  `;
}

/**
 * Generates the audience context section of the prompt
 */
function generateAudienceContext(
  selectedPersonas: string[],
  personas: any[],
  selectedIcps: string[],
  icps: any[],
): string {
  const personasSection =
    selectedPersonas.length > 0
      ? `
    <Personas>
        ${personas
          .filter((p) => selectedPersonas.includes(p.id))
          .map((p) => {
            const personaData =
              p.data && typeof p.data === 'object' ? p.data : {};
            return `<Persona name="${p.name}">\n<Description>${JSON.stringify((personaData as any)?.data) || 'Target audience segment'}</Description>\n</Persona>`;
          })
          .join('\n')}
    </Personas>`
      : '';

  const icpsSection =
    selectedIcps.length > 0
      ? `
    <IdealCustomerProfiles>
        ${icps
          .filter((i) => selectedIcps.includes(i.id))
          .map((i) => {
            const icpData = i.data && typeof i.data === 'object' ? i.data : {};
            return `<ICP name="${i.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
          })
          .join('\n')}
    </IdealCustomerProfiles>`
      : '';

  return `
    <AUDIENCE_CONTEXT>
        ${personasSection}
        ${icpsSection}
    </AUDIENCE_CONTEXT>
  `;
}

/**
 * Generates the research materials section of the prompt
 */
function generateResearchMaterials(
  selectedResearch: string[],
  researchItems: any[],
): string {
  const researchContent =
    selectedResearch.length > 0
      ? researchItems
          .filter((r) => selectedResearch.includes(r.id))
          .map((r, index) => {
            const researchData =
              r.data && typeof r.data === 'object' ? r.data : {};
            return `<research_article_${index + 1}>
          <title>${r.name}</title>
          <description>${(researchData as any).description || (researchData as any).summary || 'External research insight'}</description>
          <full_content>${(researchData as any).source_content || ''}</full_content>
          </research_article_${index + 1}>`;
          })
          .join('\n\n')
      : '<Message>No third-party research was provided.</Message>';

  return `
    <RESEARCH_MATERIALS>
        ${researchContent}
    </RESEARCH_MATERIALS>
  `;
}

/**
 * Generates the company product knowledge base section of the prompt
 */
function generateProductKnowledgeBase(
  selectedDocuments: SelectedDocument[],
): string {
  const documentsContent =
    selectedDocuments.length > 0
      ? selectedDocuments
          .map(
            (doc) =>
              `<Document title="${doc.documentTitle}">\n${doc.content.substring(0, 1000)}${doc.content.length > 1000 ? '...' : ''}\n</Document>`,
          )
          .join('\n\n')
      : '<Message>No company documents were provided.</Message>';

  return `
    <COMPANY_PRODUCT_KNOWLEDGE_BASE>
        ${documentsContent}
    </COMPANY_PRODUCT_KNOWLEDGE_BASE>
  `;
}

/**
 * Generates the keyword strategy section of the prompt
 */
function generateKeywordStrategy(
  seoKeywords: string[],
  trendKeywords: string[],
): string {
  return `
    <KEYWORD_STRATEGY>
        <SEO_Keywords>${seoKeywords.join(', ')}</SEO_Keywords>
        <Trending_Keywords>${trendKeywords.join(', ')}</Trending_Keywords>
    </KEYWORD_STRATEGY>
  `;
}

/**
 * Generates the brand guidelines section of the prompt
 */
function generateBrandGuidelines(companyBrand: any): string {
  return `
    <BRAND_GUIDELINES>
       ${JSON.stringify(companyBrand)}
    </BRAND_GUIDELINES>
  `;
}

/**
 * Generates the complete content generation prompt for Studio
 */
export function generateStudioContentPrompt(
  params: PromptGenerationParams,
): string {
  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  const contextBlock = `
    <CONTEXT_BLOCK>
      ${generateContentBrief(taskDescription, selectedCompanyContent)}
      ${generateAudienceContext(selectedPersonas, personas, selectedIcps, icps)}
      ${generateResearchMaterials(selectedResearch, researchItems)}
      ${generateProductKnowledgeBase(selectedDocuments)}
      ${generateKeywordStrategy(seoKeywords, trendKeywords)}
      ${generateBrandGuidelines(companyBrand)}
    </CONTEXT_BLOCK>
  `;

  const instructions = `
    Ignore the previous instruction "Because the document is empty, first update the empty block before adding new blocks."
    Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
    This is the most critical instruction. Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
    Do not try to insert images or videos into the content. For Threads, carousels, or similar content, simply insert everything delineated by some character, then write out everything as if its one page. The user will handle creating the thread or similar on the appropriate platform.
  `;

  const taskInstructions = `
    You are "Cognitive Creator," an expert AI copywriter and content strategist. Your core function is to synthesize brand information, audience data, and research into high-performing content tailored for specific marketing channels. You follow all instructions with precision.

    <TASK>
    Synthesize all information within the <CONTEXT_BLOCK> to create engaging content.

    **PRIMARY DIRECTIVES:**
    1.  **Adhere to Brand:** The <BRAND_GUIDELINES> are the highest priority. The specified <Voice> and <Personality> must be perfectly reflected in the output. This is non-negotiable.
    2.  **Target the Audience:** Tailor the language, examples, and tone specifically to the <AUDIENCE_CONTEXT>. Address their needs and pain points directly.
    3.  **Position as the Solution:** Use the <RESEARCH_MATERIALS> for context, stats, and credibility. ALWAYS position the company/product from the <COMPANY_PRODUCT_KNOWLEDGE_BASE> as the primary solution to problems identified in the research. Never promote third parties.
    4.  **Incorporate Keywords:** Naturally weave terms from the <KEYWORD_STRATEGY> into the content.
    5.  **Be Factual:** Ensure any product or company claims are supported by the <COMPANY_PRODUCT_KNOWLEDGE_BASE>. Do not invent features or facts.
    6.  **Do Not Invent Social Proof:** Do not create fake customer names or quotes. You can suggest a placeholder like "[Insert customer testimonial here]" if appropriate for the content type.

    **CHANNEL-SPECIFIC RULES:**
    Based on the <Channel> specified in the <CONTENT_BRIEF>, you must follow these structural rules:

    *   **If Channel is "LinkedIn Post":**
        *   **Structure:** Start with a strong hook. Use short paragraphs (1-2 sentences). Use bullet points or numbered lists for readability. End with a question to drive engagement.
        *   **Length:** 150-250 words.
        *   **Tone:** Professional, insightful, and value-driven.
        *   **Hashtags:** Provide 3-5 relevant, professional hashtags.

    *   **If Channel is "Tweet" or "X Post":**
        *   **Structure:** A short, punchy, and engaging message.
        *   **Length:** Strictly under 280 characters.
        *   **Tone:** Conversational and concise. Emojis are acceptable if they match the brand personality.
        *   **Hashtags:** Provide 1-3 highly relevant hashtags.

    *   **If Channel is "Blog Post" or "Article":**
        *   **Structure:** Create a compelling H1 title. Write a short introduction. Structure the main content with 3-4 H2 subheadings. Conclude with a summary and a strong call_to_action.
        *   **Length:** 600-1000 words.
        *   **Tone:** Informative, in-depth, and authoritative, aligned with the brand voice.
        *   **SEO:** Suggest 5-7 relevant meta tags in the output.

    *   **If Channel is "Facebook Ad":**
        *   **Structure:** Provide three distinct components: a short, attention-grabbing headline, persuasive primary_text focusing on benefits, and a direct call_to_action_text.
        *   **Tone:** Persuasive, clear, and benefit-driven.

    *   **If Channel is not specified or doesn't match above:**
        *   **Structure:** Create a general-purpose piece of content with a clear beginning, middle, and end.
        *   **Tone:** Follow the brand voice.
        *   **Action:** End with a clear call-to-action.

    </TASK>
  `;

  return `${instructions}\n${contextBlock}\n${taskInstructions}`;
}
