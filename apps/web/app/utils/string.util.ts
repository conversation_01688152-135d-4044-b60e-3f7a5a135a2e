import ShortUniqueId from 'short-unique-id';

export const getUniqueId = () => {
  const { randomUUID } = new ShortUniqueId({ length: 10 });
  return randomUUID();
};
/**
 * Truncates a filename to a specified length while preserving the extension
 * Example: truncateFilename('very-long-filename.jpg', 10) => 'very-lo...jpg'
 *
 * @param filename - The filename to truncate
 * @param maxLength - The maximum length of the truncated filename (including ellipsis and extension)
 * @returns The truncated filename
 */
export function truncateFilename(
  filename: string,
  maxLength: number = 25,
): string {
  if (!filename) return '';
  if (filename.length <= maxLength) return filename;

  const extension = filename.includes('.') ? filename.split('.').pop() : '';
  const name = extension
    ? filename.substring(0, filename.lastIndexOf('.'))
    : filename;

  // Account for the ellipsis (...) and extension length in the max length
  const ellipsisLength = 3;
  const extensionLength = extension ? extension.length + 1 : 0; // +1 for the dot
  const maxNameLength = maxLength - ellipsisLength - extensionLength;

  if (maxNameLength <= 0) {
    // Edge case: if the extension is very long
    return `${name.substring(0, Math.max(1, maxLength - ellipsisLength - 1))}...`;
  }

  const truncatedName = name.substring(0, maxNameLength);
  return extension ? `${truncatedName}...${extension}` : `${truncatedName}...`;
}
