export const extractBrandBrief = (brandData: any) => {
  // concatenate specific fields from the brand data
  if (!brandData || !brandData.brand) return 'No Brand Brief Provided';
  return JSON.stringify({
    audience: brandData.brand.audience,
    value_proposition: brandData.brand.value_proposition,
    personality: brandData.brand.personality,
    voice: brandData.brand.voice,
    guidelines: brandData.brand.guidelines,
    messaging_pillars: brandData.brand.messaging_pillars,
    identity: brandData.brand.identity,
    product_list: brandData.brand.product_list,
    mission: brandData.brand.mission,
    vision: brandData.brand.vision,
    tone: brandData.brand.tone,
    visual_style: brandData.brand.visual_style,
    language: brandData.brand.language,
    brand_colors: brandData.brand.brand_colors,
  });
};

export const extractCampaignBrief = (campaignData: any) => {
  // concatenate specific fields from the campaign data
  return JSON.stringify({
    objectives: campaignData.objectives,
    targetAudience: campaignData.targetAudience,
    personas: campaignData.personas,
    messaging: campaignData.messaging,
    value_prop: campaignData.value_prop,
    visualStyle: campaignData.visualStyle,
    tone: campaignData.tone,
    voice: campaignData.voice,
    personality: campaignData.personality,
    identity: campaignData.identity,
    guidelines: campaignData.guidelines,
    kpis: campaignData.kpis,
    start_date: campaignData.start_date,
    end_date: campaignData.end_date,
  });
};
