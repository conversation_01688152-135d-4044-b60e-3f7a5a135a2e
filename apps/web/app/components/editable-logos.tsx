import React, { useRef, useState } from 'react';

import Image from 'next/image';

import { Pencil, Trash2, Upload, X } from 'lucide-react';

import { Button } from '@kit/ui/button';

interface EditableLogosProps {
  logos: Array<{ name: string; url: string }>;
  onAdd: (file: File) => Promise<void>;
  onDelete: (fileName: string) => Promise<void>;
}

const EditableLogos: React.FC<EditableLogosProps> = ({
  logos,
  onAdd,
  onDelete,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await onAdd(file);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Brand Logos</h2>
        {!isEditing ? (
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
        ) : (
          <div className="flex gap-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
            />
            <Button
              variant="default"
              size="sm"
              color="primary"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-4 w-4" />
            </Button>
            <Button
              variant="default"
              size="sm"
              color="default"
              onClick={() => setIsEditing(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
        {logos.map((logo, index) => (
          <div
            key={index}
            className="relative aspect-square rounded-xl bg-gray-50 p-4 transition-colors hover:bg-gray-100"
          >
            {isEditing && (
              <Button
                variant="default"
                size="sm"
                color="danger"
                className="absolute -top-2 -right-2 z-10"
                onClick={() => onDelete(logo.name)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
            <Image
              src={logo.url}
              alt={logo.name}
              fill
              className="object-contain p-4"
              unoptimized
              loading="lazy"
            />
            <div className="absolute right-2 bottom-2 left-2 text-center">
              <span className="rounded-full bg-white/90 px-2 py-1 text-sm">
                {logo.name}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EditableLogos;
