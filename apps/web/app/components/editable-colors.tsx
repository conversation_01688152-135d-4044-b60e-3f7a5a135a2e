import React, { useState } from 'react';

import { Check, Pencil, Plus, Trash2, X } from 'lucide-react';

import { Button } from '@kit/ui/button';

interface EditableColorsProps {
  colors: string[];
  onSave: (colors: string[]) => void;
}

const EditableColors: React.FC<EditableColorsProps> = ({ colors, onSave }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedColors, setEditedColors] = useState<string[]>(colors);

  const handleSave = () => {
    onSave(editedColors);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedColors(colors);
    setIsEditing(false);
  };

  const addColor = () => {
    setEditedColors([...editedColors, '#000000']);
  };

  const removeColor = (index: number) => {
    setEditedColors(editedColors.filter((_, i) => i !== index));
  };

  const updateColor = (index: number, value: string) => {
    const newColors = [...editedColors];
    newColors[index] = value;
    setEditedColors(newColors);
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Brand Colors</h2>
        {!isEditing ? (
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="default"
              size="sm"
              color="primary"
              onClick={addColor}
            >
              <Plus className="h-4 w-4" />
            </Button>
            <Button
              variant="default"
              size="sm"
              color="success"
              onClick={handleSave}
            >
              <Check className="h-4 w-4" />
            </Button>
            <Button
              variant="default"
              size="sm"
              color="danger"
              onClick={handleCancel}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
        {editedColors.map((color, index) => (
          <div key={index} className="group relative">
            {isEditing ? (
              <div className="space-y-2">
                <div className="relative">
                  <div
                    className="aspect-square w-full rounded-lg"
                    style={{ backgroundColor: color }}
                  />
                  <Button
                    variant="default"
                    size="sm"
                    color="danger"
                    className="absolute -top-2 -right-2"
                    onClick={() => removeColor(index)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
                <input
                  type="color"
                  value={color}
                  onChange={(e) => updateColor(index, e.target.value)}
                  className="h-8 w-full cursor-pointer rounded"
                />
                <input
                  type="text"
                  value={color}
                  onChange={(e) => updateColor(index, e.target.value)}
                  className="w-full rounded border px-2 py-1 font-mono text-sm"
                />
              </div>
            ) : (
              <div
                className="cursor-pointer"
                onClick={() => navigator.clipboard.writeText(color)}
              >
                <div
                  className="aspect-square w-full rounded-lg shadow-md transition-transform group-hover:scale-105"
                  style={{ backgroundColor: color }}
                />
                <div className="mt-2 text-center">
                  <span className="rounded bg-gray-100 px-2 py-1 font-mono text-sm">
                    {color}
                  </span>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EditableColors;
