import { ReactNode } from 'react';

import {
  AlertCircle,
  AlertTriangle,
  HelpCircle,
  type LucideIcon,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';

export type ConfirmVariant = 'danger' | 'warning' | 'info';

export interface ConfirmDialogProps {
  /**
   * Whether the dialog is open
   */
  isOpen: boolean;
  /**
   * Function called when the open state changes
   */
  onOpenChange: (isOpen: boolean) => void;
  /**
   * Dialog title
   */
  title: string;
  /**
   * Dialog description
   */
  description?: string | ReactNode;
  /**
   * Text for the confirm button
   */
  confirmText?: string;
  /**
   * Text for the cancel button
   */
  cancelText?: string;
  /**
   * Function called when the confirm button is clicked
   */
  onConfirm: () => void;
  /**
   * Whether the confirm action is in progress
   */
  isLoading?: boolean;
  /**
   * Visual variant of the dialog
   */
  variant?: ConfirmVariant;
  /**
   * Whether to disable the confirm button
   */
  isDisabled?: boolean;
}

/**
 * A reusable confirmation dialog component
 */
export function ConfirmDialog({
  isOpen,
  onOpenChange,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  isLoading = false,
  variant = 'info',
  isDisabled = false,
}: ConfirmDialogProps) {
  const handleConfirm = () => {
    onConfirm();
    if (!isLoading) {
      onOpenChange(false);
    }
  };

  // Map variant to appropriate styles and icon
  const variantMap: Record<
    ConfirmVariant,
    { icon: LucideIcon; confirmButtonStyle: string; iconColor: string }
  > = {
    danger: {
      icon: AlertCircle,
      confirmButtonStyle: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
      iconColor: 'text-red-600',
    },
    warning: {
      icon: AlertTriangle,
      confirmButtonStyle:
        'bg-amber-600 hover:bg-amber-700 focus:ring-amber-500',
      iconColor: 'text-amber-600',
    },
    info: {
      icon: HelpCircle,
      confirmButtonStyle: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      iconColor: 'text-blue-600',
    },
  };

  const { icon: Icon, confirmButtonStyle, iconColor } = variantMap[variant];

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="flex flex-row items-center gap-4">
          <div
            className={`rounded-full p-2 ${iconColor} bg-opacity-10 bg-current`}
          >
            <Icon className={iconColor} />
          </div>
          <div>
            <DialogTitle>{title}</DialogTitle>
            {description && (
              <DialogDescription>{description}</DialogDescription>
            )}
          </div>
        </DialogHeader>

        <DialogFooter className="mt-4 flex flex-row justify-end gap-2 sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            type="button"
            className={confirmButtonStyle}
            onClick={handleConfirm}
            disabled={isDisabled || isLoading}
          >
            {isLoading ? 'Processing...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
