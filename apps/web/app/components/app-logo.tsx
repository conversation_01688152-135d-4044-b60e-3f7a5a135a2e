'use client';

import Image from 'next/image';
import Link from 'next/link';

import { cn } from '@kit/ui/utils';

export function isDarkSystemTheme() {
  if (typeof window === 'undefined') {
    return false;
  }
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
}


function LogoImage({
  className,
  width = 105,
}: {
  className?: string;
  width?: number;
}) {
  const isDark = isDarkSystemTheme();


  const logoSrc = isDark 
    ? '/images/Axcels-whitetext-horizontal.png'
    : '/images/Axcels-blacktext-horizontal.png';

  return (
    <div className={cn(`w-[100px] lg:w-[140px]`, className)}>
      <Image
        src={logoSrc}
        alt="logo"
        width={width}
        height={width}
        layout="responsive"
      />
    </div>
  );
}

export function AppLogo({
  href,
  label,
  className,
}: {
  href?: string | null;
  className?: string;
  label?: string;
}) {
  if (href === null) {
    return <LogoImage className={className} />;
  }

  return (
    <Link aria-label={label ?? 'Home Page'} href={href ?? '/'}>
      <LogoImage className={className} />
    </Link>
  );
}
