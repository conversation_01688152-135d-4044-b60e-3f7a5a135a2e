'use client';

import React, { useEffect, useState } from 'react';

import Link from 'next/link';

import * as Icons from 'lucide-react';

interface EmptyStateProps {
  iconName: keyof typeof Icons;
  title: string;
  description: string;
  buttonText: string;
  action?: string;
  onClientAction?: () => void;
}

const EmptyState = ({
  iconName,
  title,
  description,
  buttonText,
  action,
  onClientAction,
}: EmptyStateProps) => {
  const [Icon, setIcon] = useState<React.ElementType | null>(null);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const IconComponent = Icons[iconName];
    //@ts-expect-error not sure why this is throwing an error
    setIcon(() => IconComponent);
    setLoading(false);
  }, [iconName]);

  if (loading) return null;

  const ButtonComponent = onClientAction ? (
    <button
      onClick={onClientAction}
      className="inline-flex items-center rounded-lg bg-black px-4 py-2 text-sm font-medium text-white transition-colors duration-150 hover:bg-black"
    >
      {buttonText}
    </button>
  ) : (
    <Link
      href={action || ''}
      className="inline-flex items-center rounded-lg bg-black px-4 py-2 text-sm font-medium text-white transition-colors duration-150 hover:bg-black"
    >
      {buttonText}
    </Link>
  );

  return (
    <div className="flex max-w-md grow flex-col justify-center">
      <div className="flex flex-col">
        <div className="flex p-4">{Icon && <Icon size={64} />}</div>
        <div className="align-center items-center justify-center p-4">
          <h1 className="text-md font-bold">{title}</h1>
          <p>{description}</p>
        </div>
      </div>
      <div className="ml-3">{ButtonComponent}</div>
    </div>
  );
};

export default EmptyState;
