import React, { useRef, useState } from 'react';

import { Pencil, Trash2, Upload, X } from 'lucide-react';

import { Button } from '@kit/ui/button';

interface EditableFontsProps {
  fonts: Array<{ name: string; url: string; fontFamily: string }>;
  loadedFonts: { [key: string]: boolean };
  onAdd: (file: File) => Promise<void>;
  onDelete: (fileName: string) => Promise<void>;
}

const EditableFonts: React.FC<EditableFontsProps> = ({
  fonts,
  loadedFonts,
  onAdd,
  onDelete,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await onAdd(file);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Typography</h2>
        {!isEditing ? (
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
        ) : (
          <div className="flex gap-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept=".ttf,.otf,.woff,.woff2"
              className="hidden"
            />
            <Button
              variant="default"
              size="sm"
              color="primary"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-4 w-4" />
            </Button>
            <Button
              variant="default"
              size="sm"
              color="default"
              onClick={() => setIsEditing(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-6">
        {fonts.map((font, index) => (
          <div key={index} className="relative rounded-lg bg-gray-50 p-6">
            {isEditing && (
              <Button
                variant="default"
                size="sm"
                color="danger"
                className="absolute -top-2 -right-2 z-10"
                onClick={() => onDelete(font.name)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
            <div className="mb-3 flex items-center justify-end">
              <span className="font-mono text-sm text-gray-500">
                {font.fontFamily}
              </span>
            </div>
            {loadedFonts[font.fontFamily] ? (
              <div className="space-y-2">
                <p style={{ fontFamily: font.fontFamily }} className="text-3xl">
                  ABCDEFGHIJKLMNOPQRSTUVWXYZ
                </p>
                <p style={{ fontFamily: font.fontFamily }} className="text-xl">
                  The quick brown fox jumps over the lazy dog
                </p>
                <p style={{ fontFamily: font.fontFamily }} className="text-sm">
                  1234567890!@#$%^&*()
                </p>
              </div>
            ) : (
              <p className="text-sm text-red-500">Failed to load font</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EditableFonts;
