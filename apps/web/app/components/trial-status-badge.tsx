'use client';

import Link from 'next/link';
import { Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@kit/ui/tooltip';
import { useTrialBadge } from '~/hooks/use-trial-status';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { cn } from '@kit/ui/utils';

interface TrialStatusBadgeProps {
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'default' | 'lg';
}

/**
 * Trial Status Badge Component
 * 
 * Displays trial expiration information with color coding:
 * - Red: Trial expired or about to expire (urgent)
 * - Orange: Trial expiring soon (warning) 
 * - Green: Trial active with time remaining (good status)
 */
export function TrialStatusBadge({ 
  className, 
  showIcon = true, 
  size = 'default' 
}: TrialStatusBadgeProps) {
  const workspace = useTeamAccountWorkspace();
  const trialBadge = useTrialBadge();
  const accountSlug = workspace.account.slug || workspace.account.id;

  // Don't render if badge shouldn't be shown
  if (!trialBadge.show) {
    return null;
  }

  const billingPath = `/home/<USER>/billing`;

  // Get icon based on variant
  const getIcon = () => {
    if (!showIcon) return null;
    
    switch (trialBadge.variant) {
      case 'destructive':
        return <AlertTriangle className="h-3 w-3" />;
      case 'warning':
        return <Clock className="h-3 w-3" />;
      case 'success':
        return <CheckCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  // Get badge variant mapping for UI components
  const getBadgeVariant = () => {
    switch (trialBadge.variant) {
      case 'destructive':
        return 'destructive';
      case 'warning':
        return 'secondary'; // Using secondary for orange/warning
      case 'success':
        return 'default'; // Using default for green/success
      default:
        return 'secondary';
    }
  };

  // Get custom colors for more precise styling
  const getCustomStyles = () => {
    switch (trialBadge.variant) {
      case 'destructive':
        return 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800';
      case 'warning':
        return 'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800';
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800';
      default:
        return '';
    }
  };

  const icon = getIcon();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link href={billingPath}>
            <Badge
              variant={getBadgeVariant()}
              className={cn(
                'cursor-pointer transition-colors duration-200 border',
                getCustomStyles(),
                {
                  'text-xs px-2 py-1': size === 'sm',
                  'text-sm px-3 py-1': size === 'default',
                  'text-base px-4 py-2': size === 'lg',
                },
                className
              )}
            >
              <div className="flex items-center gap-1.5">
                {icon}
                <span className="font-medium">{trialBadge.message}</span>
              </div>
            </Badge>
          </Link>
        </TooltipTrigger>
        <TooltipContent>
          <p>
            {trialBadge.variant === 'destructive' 
              ? 'Your trial has expired. Click to upgrade your subscription.'
              : trialBadge.variant === 'warning'
              ? 'Your trial is expiring soon. Click to upgrade your subscription.'
              : 'Your trial is active. Click to manage your subscription.'
            }
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Compact version of the trial badge for smaller spaces
 */
export function TrialStatusBadgeCompact({ className }: { className?: string }) {
  return (
    <TrialStatusBadge 
      className={className}
      showIcon={true}
      size="sm"
    />
  );
}

/**
 * Trial status as a button for call-to-action scenarios
 */
export function TrialStatusButton({ className }: { className?: string }) {
  const workspace = useTeamAccountWorkspace();
  const trialBadge = useTrialBadge();
  const accountSlug = workspace.account.slug || workspace.account.id;

  if (!trialBadge.show) {
    return null;
  }

  const billingPath = `/home/<USER>/billing`;

  const getButtonVariant = () => {
    switch (trialBadge.variant) {
      case 'destructive':
        return 'destructive';
      case 'warning':
        return 'secondary';
      case 'success':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  return (
    <Link href={billingPath}>
      <Button 
        variant={getButtonVariant()}
        size="sm"
        className={cn('text-xs', className)}
      >
        <Clock className="h-3 w-3 mr-1" />
        {trialBadge.message}
      </Button>
    </Link>
  );
}
