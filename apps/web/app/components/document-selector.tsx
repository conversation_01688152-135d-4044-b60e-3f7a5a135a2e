'use client';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Checkbox } from '@kit/ui/checkbox';
import { toast } from 'sonner';
import { ProductDocumentWithLink } from '~/types/product-document';
import { getProductDocuments } from '~/services/product-document';
import { useRouter } from 'next/navigation';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

export interface SelectedDocument {
  id: string;
  documentTitle: string;
  content: string;
}

interface DocumentSelectorProps {
  accountId: string;
  initialDocuments?: SelectedDocument[];
  onDocumentsChange: (documents: SelectedDocument[]) => void;
  description?: string;
}

export function DocumentSelector({
  accountId,
  initialDocuments = [],
  onDocumentsChange,
  description = 'Select documents to provide context. This information will be used to generate more relevant content.',
}: DocumentSelectorProps) {
  console.log("initialDocuments", description);
  const [selectedDocuments, setSelectedDocuments] = useState<Record<string, boolean>>({});
  const router = useRouter();
  const workspace = useTeamAccountWorkspace();
  // Fetch product documents for the company
  const { data: productDocuments, isLoading: isLoadingDocuments } = useQuery({
    queryKey: ['product-documents', accountId],
    queryFn: async () => {
      try {
        const documents = await getProductDocuments(accountId);
        return documents;
      } catch (error) {
        console.error('Error fetching product documents:', error);
        toast.error('Failed to load product documents');
        return [];
      }
    }
  });

  // console.log("productDocuments", productDocuments, initialDocuments);
  // Initialize selectedDocuments state based on initialDocuments
  useEffect(() => {
    if (initialDocuments.length > 0) {
      const selectionMap: Record<string, boolean> = {};
      console.log("initialDocuments", initialDocuments);
      initialDocuments.forEach((doc) => {
        selectionMap[doc.id] = true;
      });
      setSelectedDocuments(selectionMap);
    }
  }, [initialDocuments]);

  // Handle document selection
  const handleDocumentSelection = (
    docId: string,
    docTitle: string,
    docContent: string,
    checked: boolean
  ) => {
    // Update local state for checkbox control
    setSelectedDocuments(prev => ({
      ...prev,
      [docId]: checked
    }));

    // Get current selected documents
    const currentDocuments = productDocuments?.filter(
      doc => selectedDocuments[doc.id as string] || doc.id === docId
    ) || [];

    // Create updated documents array
    const updatedDocuments = currentDocuments
      .filter(doc => doc.id === docId ? checked : selectedDocuments[doc.id as string])
      .map(doc => ({
        id: doc.id as string,
        documentTitle: doc.title,
        content: doc.content || ''
      }));

    // Notify parent component of changes
    onDocumentsChange(updatedDocuments);
  };

  return (
    <div className="border rounded-md p-4 mb-6">
      <h3 className="text-lg font-medium mb-2">Add Context</h3>
      {/* <FormDescription className="mb-4">
        {description}
      </FormDescription> */}
      
      {isLoadingDocuments ? (
        <div className="py-2">Loading documents...</div>
      ) : !productDocuments || productDocuments.length === 0 ? (
        <div className="py-2 text-muted-foreground">No documents found. <span className="text-blue-500 underline cursor-pointer" onClick={() => router.push(`/home/<USER>/product-information`)}>Upload documents</span> to provide context.</div>
      ) : (
        <div className="space-y-2">
          {productDocuments.map((document: ProductDocumentWithLink) => (
            <div key={document.id} className="flex items-start space-x-2 p-2 hover:bg-muted/50 rounded">
              <Checkbox 
                id={`doc-${document.id}`}
                checked={selectedDocuments[document.id as string] || false}
                onCheckedChange={(checked) => {
                  handleDocumentSelection(
                    document.id as string, 
                    document.title, 
                    document.content || '', 
                    checked as boolean
                  );
                }}
              />
              <div className="grid gap-1.5 leading-none">
                <label
                  htmlFor={`doc-${document.id}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {document.title}
                </label>
                <p className="text-xs text-muted-foreground">
                  {document.file_type} • {document.content ? `${document.content.slice(0, 100)}...` : 'No content'}
                </p>
              </div>
            </div>
          ))}
              <p className="text-sm text-blue-500 underline cursor-pointer" onClick={() => router.push(`/home/<USER>/product-information`)}>Add documents for context.</p>
        </div>
      )}
    </div>
  );
} 