'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { useTrialStatus } from '~/hooks/use-trial-status';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface TrialGuardProps {
  children: React.ReactNode;
}

/**
 * Trial Guard Component
 *
 * Protects routes based on subscription/trial status.
 * Shows loading overlay while trial status is being determined to prevent content flash.
 * When trial has expired or no subscription exists:
 * - Allows access only to billing, account, and members pages
 * - Shows loading overlay and redirects to billing page for all other routes
 * - Prevents any flash of protected content before redirect
 */
export function TrialGuard({ children }: TrialGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const workspace = useTeamAccountWorkspace();
  const trialStatus = useTrialStatus();
  
  const accountSlug = workspace.account.slug || workspace.account.id;

  // Define allowed paths when trial is expired or no subscription
  const allowedPaths = [
    `/home/<USER>/billing`,
    `/home/<USER>/settings`, // account settings
    `/home/<USER>/members`,
    `/home/<USER>/user-settings`, // user account page
  ];

  // Check if current path is allowed
  const isAllowedPath = allowedPaths.some(allowedPath => 
    pathname.startsWith(allowedPath)
  );

  useEffect(() => {
    // Only redirect if we have loaded the trial status and access is denied
    if (!trialStatus.isLoading && !trialStatus.canAccessApp && !isAllowedPath) {
      // Redirect to billing page
      const billingPath = `/home/<USER>/billing`;
      router.push(billingPath);
    }
  }, [
    trialStatus.canAccessApp,
    trialStatus.isLoading,
    isAllowedPath,
    router,
    accountSlug,
    pathname
  ]);

  // Show loading overlay while trial status is being determined
  if (trialStatus.isLoading) {
    return <LoadingOverlay fullPage={true} />;
  }

  // If trial expired/no subscription and user is not on an allowed path, show loading
  // while redirect is happening to prevent any content flash
  if (!trialStatus.canAccessApp && !isAllowedPath) {
    return <LoadingOverlay fullPage={true} />;
  }

  // Only render children when we have confirmed access is allowed
  return <>{children}</>;
}

/**
 * Higher-order component version of TrialGuard
 * Can be used to wrap page components
 */
export function withTrialGuard<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  const WrappedComponent = (props: P) => {
    return (
      <TrialGuard>
        <Component {...props} />
      </TrialGuard>
    );
  };

  WrappedComponent.displayName = `withTrialGuard(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
