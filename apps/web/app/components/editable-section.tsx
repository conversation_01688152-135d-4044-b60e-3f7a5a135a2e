import React, { useEffect, useState } from 'react';
import { Pencil, Save, X } from 'lucide-react';
import { Button } from '@kit/ui/button';

interface EditableSectionProps {
  title: string;
  content: string;
  onSave: (content: string) => void;
  useTextarea?: boolean;
  placeholder?: string;
}

const EditableSection: React.FC<EditableSectionProps> = ({
  title,
  content,
  onSave,
  useTextarea = true,
  placeholder = 'Click to add content...',
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(content);

  // Update editedContent when content prop changes
  useEffect(() => {
    setEditedContent(content);
  }, [content]);

  const handleSave = () => {
    onSave(editedContent);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedContent(content);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Save on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      handleSave();
    }
    // Cancel on Escape
    if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (title) {
    // Render with title (legacy support)
    return (
      <div className="relative">
        <div className="mb-2">
          <h3 className="text-lg font-semibold">{title}</h3>
        </div>
  
        {isEditing ? (
          <div className="space-y-2">
            {useTextarea ? (
              <textarea
                className="w-full rounded-md border border-input bg-transparent p-2"
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                rows={4}
                placeholder={placeholder}
                autoFocus
                onKeyDown={handleKeyDown}
              />
            ) : (
              <input
                type="text"
                className="w-full rounded-md border border-input bg-transparent px-2 py-2"
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                placeholder={placeholder}
                autoFocus
                onKeyDown={handleKeyDown}
              />
            )}
            <div className="flex justify-end space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancel}
                className="flex items-center"
              >
                <X className="mr-1 h-3 w-3" />
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleSave}
                className="flex items-center"
              >
                <Save className="mr-1 h-3 w-3" />
                Save
              </Button>
            </div>
          </div>
        ) : (
          <div 
            className="group/editable relative cursor-pointer rounded-md border border-transparent p-3 hover:border-muted hover:bg-accent/20"
            onClick={() => setIsEditing(true)}
          >
            {content ? (
              <div className="flex items-start justify-between">
                <div className="w-full">
                  {content?.includes('<') ? (
                    <div
                      className="prose max-w-none text-foreground"
                      dangerouslySetInnerHTML={{ __html: content }}
                    />
                  ) : (
                    <p className="text-foreground whitespace-pre-wrap">{content}</p>
                  )}
                </div>
                <Pencil className="h-4 w-4 flex-shrink-0 text-muted-foreground opacity-0 transition-opacity group-hover/editable:opacity-100" />
              </div>
            ) : (
              <p className="text-muted-foreground">{placeholder}</p>
            )}
          </div>
        )}
      </div>
    );
  }

  // Render without title (new design)
  return (
    <div className="relative">
      {isEditing ? (
        <div className="space-y-2">
          {useTextarea ? (
            <textarea
              className="w-full rounded-md border border-input bg-transparent p-2"
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              rows={4}
              placeholder={placeholder}
              autoFocus
              onKeyDown={handleKeyDown}
            />
          ) : (
            <input
              type="text"
              className="w-full rounded-md border border-input bg-transparent px-2 py-2"
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              placeholder={placeholder}
              autoFocus
              onKeyDown={handleKeyDown}
            />
          )}
          <div className="flex justify-end space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={handleCancel}
              className="flex items-center"
            >
              <X className="mr-1 h-3 w-3" />
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="flex items-center"
            >
              <Save className="mr-1 h-3 w-3" />
              Save
            </Button>
          </div>
        </div>
      ) : (
        <div 
          className="group/editable relative cursor-pointer rounded-md border border-transparent p-3 hover:border-muted hover:bg-accent/20"
          onClick={() => setIsEditing(true)}
        >
          {content ? (
            <div className="flex items-start justify-between">
              <div className="w-full">
                {content?.includes('<') ? (
                  <div
                    className="prose max-w-none text-foreground"
                    dangerouslySetInnerHTML={{ __html: content }}
                  />
                ) : (
                  <p className="text-foreground whitespace-pre-wrap">{content}</p>
                )}
              </div>
              <Pencil className="h-4 w-4 flex-shrink-0 text-muted-foreground opacity-0 transition-opacity group-hover/editable:opacity-100" />
            </div>
          ) : (
            <p className="text-muted-foreground">{placeholder}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default EditableSection;
