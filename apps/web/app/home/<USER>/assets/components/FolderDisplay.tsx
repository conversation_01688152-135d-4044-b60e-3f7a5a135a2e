import { useState } from 'react';

import Image from 'next/image';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { AxiosResponse } from 'axios';
import {
  ChevronLeft,
  FileAudioIcon,
  FileIcon,
  ImageIcon,
  MoreVertical,
  Trash,
  Upload,
  VideoIcon,
  X,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import {
  deleteAssetFile,
  listFolderContents,
  uploadMultipleAssetFiles,
} from '~/services/storage';
import { StorageFile } from '~/types/assets';
import { getLocalApi } from '~/utils/api.util';
import { truncateFilename } from '~/utils/string.util';

import { AudioPlayer } from './AudioPlayer';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface FolderDisplayProps {
  folderName: string;
  onBack: () => void;
}

export function FolderDisplay({ folderName, onBack }: FolderDisplayProps) {
  const queryClient = useQueryClient();
  const [selectedFile, setSelectedFile] = useState<StorageFile | null>(null);
  const [imageAspect, setImageAspect] = useState<'portrait' | 'landscape'>(
    'landscape',
  );
  const workspace  = useTeamAccountWorkspace();

  const {
    data: files = [],
    isLoading,
    refetch,
  } = useQuery<StorageFile[]>({
    queryKey: ['folderContents', folderName],
    queryFn: () => listFolderContents(folderName, workspace.account.id),
  });

  const extractTranscript = useMutation({
    mutationFn: async (file: { url: string; name: string }) => {
      console.log('Extracting transcript for:', workspace.account.id);
      if (!workspace.account.id) {
        throw new Error('No company ID found');
      }

      const response: AxiosResponse = await getLocalApi().post(
        '/api/ai/extract-audio-transcript',
        {
          audioUrl: file.url,
          fileName: file.name,
          folderName,
          companyId: workspace.account.id,
        },
      );

      if (response.status !== 200) {
        throw new Error('Failed to extract transcript');
      }

      return response.data;
    },
  });

  const upload = useMutation({
    mutationFn: (files: FileList | File[]) =>
      uploadMultipleAssetFiles(folderName, Array.from(files), workspace.account.id).then((files) =>
        files.map((file) => ({
          ...file,
          type: file.path.split('.').pop()?.toLowerCase() || '',
          name: file.path.split('/').pop() || '',
        })), 
      ),
    onSuccess: async (uploadedFiles: StorageFile[]) => {
      queryClient.invalidateQueries({
        queryKey: ['folderContents', folderName],
      });

      // Filter audio files by checking file extension
      const audioFiles = uploadedFiles.filter((file: StorageFile) => {
        const extension = file.path.split('.').pop()?.toLowerCase();
        return ['mp3', 'wav', 'ogg', 'm4a'].includes(extension || '');
      });

      console.log('Audio files:', audioFiles);
      for (const file of audioFiles) {
        try {
          await extractTranscript.mutateAsync({
            url: file.url,
            name: file.path.split('/').pop() || '',
          });
        } catch (error) {
          console.error('Failed to extract transcript:', error);
        }
      }
    },
  });

  const deleteFile = useMutation({
    mutationFn: async (file: StorageFile) => {
      const fileName = file.path.split('/').pop() || '';
      return deleteAssetFile(folderName, fileName, workspace.account.id);
    },
    onSuccess: () => {
      // Invalidate and refetch to ensure UI is updated
      queryClient.invalidateQueries({
        queryKey: ['folderContents', folderName],
      });

      // Explicit refetch to ensure UI updates immediately
      refetch();

      // If a file was selected and it was deleted, clear the selection
      if (selectedFile && selectedFile.path === file.path) {
        setSelectedFile(null);
      }
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Check if any file is larger than 16MB
    const MAX_FILE_SIZE = 16 * 1024 * 1024;
    const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE);

    if (oversizedFiles.length > 0) {
      alert('Some files are larger than 16MB and cannot be uploaded');
      return;
    }

    upload.mutate(files);
  };

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    setImageAspect(
      img.naturalWidth > img.naturalHeight ? 'landscape' : 'portrait',
    );
  };

  const handleFileContentClick = (file: StorageFile) => {
    setSelectedFile(file);
  };

  const getFileName = (path: string): string => {
    return path.split('/').pop() || 'Unnamed file';
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="p-4">
      <div className="mb-6 flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={onBack}>
            <ChevronLeft className="h-5 w-5" />
            Back
          </Button>
          <Button
            color="primary"
            className="inline-flex items-center"
            disabled={upload.isPending}
          >
            <label className="flex cursor-pointer items-center">
              {upload.isPending ? (
                <>
                  <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-5 w-5" />
                  Upload Files
                </>
              )}
              <input
                type="file"
                className="hidden"
                onChange={handleFileChange}
                accept="image/*,video/*,audio/*"
                multiple
                disabled={upload.isPending}
              />
            </label>
          </Button>
        </div>
        <h2 className="px-2 text-2xl font-bold">{folderName}</h2>
      </div>

      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
        {files.map((file: StorageFile) => (
          <div
            key={file.path}
            className="group flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md"
          >
            {/* File header with title and menu */}
            <div className="flex items-center justify-between border-b border-gray-100 bg-gray-50 px-3 py-2">
              {
                // show icon based on file type
                file?.type?.startsWith('image/') ? (
                  <ImageIcon className="h-6 w-6" />
                ) : file?.type?.startsWith('video/') ? (
                  <VideoIcon className="h-6 w-6" />
                ) : file?.type?.startsWith('audio/') ? (
                  <FileAudioIcon className="h-6 w-6" />
                ) : (
                  <FileIcon className="h-6 w-6" />
                )
              }
              <h3
                className="truncate text-sm font-medium text-gray-700"
                title={getFileName(file.path)}
              >
                {truncateFilename(getFileName(file.path), 20)}
              </h3>
              <div onClick={(e) => e.stopPropagation()}>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="text-red-600 focus:text-red-600"
                      onClick={() => deleteFile.mutate(file)}
                      disabled={deleteFile.isPending}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      {deleteFile.isPending ? 'Deleting...' : 'Delete'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* File content */}
            <div
              className="flex aspect-square cursor-pointer items-center justify-center p-2"
              onClick={() => handleFileContentClick(file)}
            >
              {file.type?.startsWith('image/') ? (
                <Image
                  src={file.url}
                  alt={file.name || 'Asset image'}
                  width={200}
                  height={200}
                  className="h-full w-full rounded object-contain"
                  loading="lazy"
                  onLoad={handleImageLoad}
                />
              ) : file.type?.startsWith('video/') ? (
                <video
                  src={file.url}
                  className="h-full w-full rounded object-cover"
                  controls
                />
              ) : file.type?.startsWith('audio/') ? (
                <AudioPlayer url={file.url} />
              ) : (
                <div className="flex h-full w-full items-center justify-center rounded bg-gray-100 p-4">
                  <p className="text-sm text-gray-500">
                    File preview not available
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Image/Media Viewer Overlay */}
      {selectedFile && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 p-4"
          onClick={() => setSelectedFile(null)}
        >
          <button
            onClick={() => setSelectedFile(null)}
            className="absolute top-4 right-4 text-white hover:text-gray-300"
          >
            <X size={24} />
          </button>
          <div className="absolute top-4 left-4 text-white">
            <h2 className="text-lg font-medium">
              {getFileName(selectedFile.path)}
            </h2>
          </div>
          {selectedFile.type?.startsWith('image/') ? (
            <div className="relative flex h-full w-full items-center justify-center">
              <Image
                src={selectedFile.url}
                alt="Full size"
                width={1000}
                height={1000}
                className={`h-auto max-h-[90vh] w-auto max-w-[90vw] rounded-lg object-contain ${
                  imageAspect === 'landscape' ? 'w-full' : 'h-full'
                }`}
                onClick={(e) => e.stopPropagation()}
                onLoad={handleImageLoad}
              />
            </div>
          ) : selectedFile.type?.startsWith('video/') ? (
            <video
              src={selectedFile.url}
              className="max-h-[90vh] max-w-[90vw]"
              controls
              autoPlay
              onClick={(e) => e.stopPropagation()}
            />
          ) : selectedFile.type?.startsWith('audio/') ? (
            <div
              className="rounded-lg bg-gray-800 p-8"
              onClick={(e) => e.stopPropagation()}
            >
              <AudioPlayer url={selectedFile.url} />
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
