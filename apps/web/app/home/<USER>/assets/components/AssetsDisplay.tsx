import { useState } from 'react';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Folder, PlusCircle } from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';

import EmptyState from '~/components/empty-state';
import { AssetFolder } from '~/types/assets';
import { createAssetFolder, listAssetFolders } from '~/services/storage';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface AssetsDisplayProps {
  folders: AssetFolder[];
  onFolderSelect: (folder: string) => void;
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
}

export function AssetsDisplay({
  folders: initialFolders,
  onFolderSelect,
  isModalOpen,
  setIsModalOpen,
}: AssetsDisplayProps) {
  const [folderName, setFolderName] = useState('');
  const queryClient = useQueryClient();
  const { account } = useTeamAccountWorkspace();
  // Use initial data from server, but enable background updates
  const { data: folders = initialFolders } = useQuery<AssetFolder[]>({
    queryKey: ['assetFolders'],
    queryFn: () => listAssetFolders(account.id),
    initialData: initialFolders,
  });

  const createFolder = useMutation({
    mutationFn: () => createAssetFolder(folderName, account?.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assetFolders'] });
      setFolderName('');
      setIsModalOpen(false);
    },
  });

  const handleCreateFolder = () => {
    createFolder.mutate();
  };

  if (folders.length > 0) {
    return (
      <div className="p-4">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold">Assets</h2>
          <Button onClick={() => setIsModalOpen(true)} color="primary">
            New Folder <PlusCircle className="h-5 w-5" />
          </Button>
        </div>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Folder</DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <Input
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                placeholder="Enter folder name"
              />
            </div>
            <DialogFooter>
              <Button
                onClick={handleCreateFolder}
                disabled={!folderName || createFolder.isPending}
              >
                Create
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          {folders.map((folder) => (
            <div
              key={folder.name}
              onClick={() => onFolderSelect(folder.name)}
              className="flex cursor-pointer flex-col items-center rounded-lg border p-4 hover:bg-gray-50"
            >
              <Folder className="h-12 w-12 text-blue-500" />
              <span className="mt-2 text-sm">{folder.name}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex grow flex-row items-center justify-center">
      <EmptyState
        iconName="Images"
        title="Brand Assets"
        description="Upload any other assets, such as images you have used in the past, or videos you have created."
        buttonText="Add Assets"
        onClientAction={() => setIsModalOpen(true)}
      />
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Your First Asset Folder</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <Input
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              placeholder="Enter folder name"
            />
          </div>
          <DialogFooter>
            <Button
              onClick={handleCreateFolder}
              disabled={!folderName || createFolder.isPending}
            >
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
