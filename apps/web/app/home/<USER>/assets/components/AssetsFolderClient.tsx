'use client';

import { useState } from 'react';

import { AssetFolder } from '~/types/assets';
import { AssetsDisplay } from './AssetsDisplay';
import { FolderDisplay } from './FolderDisplay';

interface AssetsFolderClientProps {
  initialFolders: AssetFolder[];
}

export function AssetsFolderClient({
  initialFolders,
}: AssetsFolderClientProps) {
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (selectedFolder) {
    return (
      <FolderDisplay
        folderName={selectedFolder}
        onBack={() => setSelectedFolder(null)}
      />
    );
  }

  return (
    <AssetsDisplay
      folders={initialFolders}
      onFolderSelect={setSelectedFolder}
      isModalOpen={isModalOpen}
      setIsModalOpen={setIsModalOpen}
    />
  );
}
