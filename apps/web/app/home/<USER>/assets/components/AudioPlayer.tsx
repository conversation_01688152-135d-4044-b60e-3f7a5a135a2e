import { useRef, useState } from 'react';

import { Music, Pause, Play } from 'lucide-react';

export function AudioPlayer({ url }: { url: string }) {
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const togglePlay = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div className="flex h-full w-full items-center justify-center rounded-lg bg-gray-800">
      <audio
        ref={audioRef}
        src={url}
        onEnded={() => setIsPlaying(false)}
        className="hidden"
      />
      <button
        onClick={togglePlay}
        className="flex flex-col items-center justify-center space-y-2 text-white transition-colors hover:text-blue-400"
      >
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-700 hover:bg-gray-600">
          {isPlaying ? <Pause size={24} /> : <Play size={24} />}
        </div>
        <Music size={20} />
      </button>
    </div>
  );
}
