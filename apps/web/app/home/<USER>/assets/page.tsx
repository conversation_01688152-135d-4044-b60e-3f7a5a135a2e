import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { PageBody } from '@kit/ui/page';

import { listAssetFolders } from '~/services/storage';
import { AssetsFolderClient } from './components/AssetsFolderClient';
import { TeamAccountLayoutPageHeader } from '../_components/layout';

interface BrandAssetsPageProps {
  params: Promise<{ account: string }>;
}

async function BrandAssetsPage(props: BrandAssetsPageProps) {
  const api = createTeamAccountsApi(getSupabaseServerClient());
  const slug = (await props.params).account;
  const data = await api.getTeamAccount(slug);

  const account = {
    id: data.id,
    name: data.name,
    pictureUrl: data.picture_url,
    slug: data.slug as string,
    primaryOwnerUserId: data.primary_owner_user_id,
  };

  const initialFolders = await listAssetFolders(data.id);
  console.log("initialFolders", initialFolders);
  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={'Brand Assets'}
        description={'Brand Assets'}
      />
      <PageBody>
        <AssetsFolderClient initialFolders={initialFolders} />
      </PageBody>
    </>
  );
}

export default BrandAssetsPage;
