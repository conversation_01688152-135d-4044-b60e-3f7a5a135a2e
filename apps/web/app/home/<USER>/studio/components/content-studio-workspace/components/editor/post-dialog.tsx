//scaffold

import { <PERSON><PERSON> } from "@kit/ui/button";
import { <PERSON><PERSON>, DialogTrigger, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alogHeader, DialogTitle } from "@kit/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@kit/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { Input } from "@kit/ui/input";
import { Calendar } from "@kit/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@kit/ui/popover";
import { Switch } from "@kit/ui/switch";
import { PlusIcon, CalendarIcon, Clock } from "lucide-react";
import { useZero } from "~/hooks/use-zero";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useParams } from "next/navigation";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState } from "react";
import { CompanyContent } from "~/types/company-content";
import { toast } from "@kit/ui/sonner";
import { SocialProfile } from "~/home/<USER>/analytics/_components/analytics-client-wrapper";
import { format } from "date-fns";
import { cn } from "@kit/ui/utils";

// Note: For proper BlockNote integration, we need to import BlockNote
// import { useCreateBlockNote } from "@blocknote/react";

const PostScheduleSchema = z.object({
  taskStatus: z.string().optional(),
  postNow: z.boolean(),
  scheduleDate: z.date().optional(),
  scheduleTime: z.string().optional(),
  timezone: z.string().optional(),
  requireApproval: z.string().optional(),
}).refine((data) => {
  if (!data.postNow && !data.scheduleDate) {
    return false;
  }
  if (!data.postNow && (!data.scheduleTime || !data.timezone)) {
    return false;
  }
  return true;
}, {
  message: "When scheduling a post, date, time, and timezone are required",
  path: ["scheduleDate"]
});

type PostScheduleData = z.infer<typeof PostScheduleSchema>;

// Comprehensive timezone options
const TIMEZONE_OPTIONS = [
  // UTC and GMT
  { value: 'UTC', label: 'UTC (GMT+0)' },
  { value: 'GMT', label: 'GMT (GMT+0)' },
  
  // Americas
  { value: 'America/Los_Angeles', label: 'Pacific Time (GMT-8/-7)' },
  { value: 'America/Denver', label: 'Mountain Time (GMT-7/-6)' },
  { value: 'America/Phoenix', label: 'Arizona (GMT-7)' },
  { value: 'America/Chicago', label: 'Central Time (GMT-6/-5)' },
  { value: 'America/New_York', label: 'Eastern Time (GMT-5/-4)' },
  { value: 'America/Halifax', label: 'Atlantic Time (GMT-4/-3)' },
  { value: 'America/St_Johns', label: 'Newfoundland (GMT-3:30/-2:30)' },
  { value: 'America/Sao_Paulo', label: 'São Paulo (GMT-3/-2)' },
  { value: 'America/Argentina/Buenos_Aires', label: 'Buenos Aires (GMT-3)' },
  { value: 'America/Noronha', label: 'Fernando de Noronha (GMT-2)' },
  { value: 'Atlantic/Cape_Verde', label: 'Cape Verde (GMT-1)' },
  
  // Europe & Africa
  { value: 'Europe/London', label: 'London (GMT+0/+1)' },
  { value: 'Europe/Dublin', label: 'Dublin (GMT+0/+1)' },
  { value: 'Europe/Paris', label: 'Paris (GMT+1/+2)' },
  { value: 'Europe/Berlin', label: 'Berlin (GMT+1/+2)' },
  { value: 'Europe/Rome', label: 'Rome (GMT+1/+2)' },
  { value: 'Europe/Madrid', label: 'Madrid (GMT+1/+2)' },
  { value: 'Europe/Amsterdam', label: 'Amsterdam (GMT+1/+2)' },
  { value: 'Europe/Stockholm', label: 'Stockholm (GMT+1/+2)' },
  { value: 'Europe/Athens', label: 'Athens (GMT+2/+3)' },
  { value: 'Europe/Helsinki', label: 'Helsinki (GMT+2/+3)' },
  { value: 'Europe/Kiev', label: 'Kyiv (GMT+2/+3)' },
  { value: 'Europe/Istanbul', label: 'Istanbul (GMT+3)' },
  { value: 'Europe/Moscow', label: 'Moscow (GMT+3)' },
  { value: 'Africa/Cairo', label: 'Cairo (GMT+2/+3)' },
  { value: 'Africa/Johannesburg', label: 'Johannesburg (GMT+2)' },
  { value: 'Africa/Lagos', label: 'Lagos (GMT+1)' },
  { value: 'Africa/Casablanca', label: 'Casablanca (GMT+0/+1)' },
  
  // Middle East & Central Asia
  { value: 'Asia/Kuwait', label: 'Kuwait (GMT+3)' },
  { value: 'Asia/Riyadh', label: 'Riyadh (GMT+3)' },
  { value: 'Asia/Baghdad', label: 'Baghdad (GMT+3)' },
  { value: 'Asia/Tehran', label: 'Tehran (GMT+3:30/+4:30)' },
  { value: 'Asia/Dubai', label: 'Dubai (GMT+4)' },
  { value: 'Asia/Baku', label: 'Baku (GMT+4)' },
  { value: 'Asia/Kabul', label: 'Kabul (GMT+4:30)' },
  { value: 'Asia/Karachi', label: 'Karachi (GMT+5)' },
  { value: 'Asia/Kolkata', label: 'Mumbai/Delhi (GMT+5:30)' },
  { value: 'Asia/Kathmandu', label: 'Kathmandu (GMT+5:45)' },
  { value: 'Asia/Dhaka', label: 'Dhaka (GMT+6)' },
  { value: 'Asia/Yangon', label: 'Yangon (GMT+6:30)' },
  
  // East Asia
  { value: 'Asia/Bangkok', label: 'Bangkok (GMT+7)' },
  { value: 'Asia/Ho_Chi_Minh', label: 'Ho Chi Minh City (GMT+7)' },
  { value: 'Asia/Jakarta', label: 'Jakarta (GMT+7)' },
  { value: 'Asia/Shanghai', label: 'Shanghai (GMT+8)' },
  { value: 'Asia/Beijing', label: 'Beijing (GMT+8)' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong (GMT+8)' },
  { value: 'Asia/Singapore', label: 'Singapore (GMT+8)' },
  { value: 'Asia/Manila', label: 'Manila (GMT+8)' },
  { value: 'Asia/Seoul', label: 'Seoul (GMT+9)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' },
  { value: 'Australia/Adelaide', label: 'Adelaide (GMT+9:30/+10:30)' },
  { value: 'Australia/Darwin', label: 'Darwin (GMT+9:30)' },
  
  // Pacific
  { value: 'Australia/Sydney', label: 'Sydney (GMT+10/+11)' },
  { value: 'Australia/Melbourne', label: 'Melbourne (GMT+10/+11)' },
  { value: 'Australia/Brisbane', label: 'Brisbane (GMT+10)' },
  { value: 'Pacific/Guam', label: 'Guam (GMT+10)' },
  { value: 'Asia/Vladivostok', label: 'Vladivostok (GMT+10)' },
  { value: 'Pacific/Auckland', label: 'Auckland (GMT+12/+13)' },
  { value: 'Pacific/Fiji', label: 'Fiji (GMT+12/+13)' },
  { value: 'Pacific/Tongatapu', label: 'Tonga (GMT+13)' },
  { value: 'Pacific/Honolulu', label: 'Hawaii (GMT-10)' },
  { value: 'Pacific/Marquesas', label: 'Marquesas (GMT-9:30)' },
  { value: 'America/Anchorage', label: 'Alaska (GMT-9/-8)' },
];

// Function to detect user's timezone
const getUserTimezone = (): string => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    return 'UTC';
  }
};

interface PostDialogProps {
  companyContent?: CompanyContent;
  selectedProfile?: any;
}

export default function PostDialog({ selectedProfile, companyContent }: PostDialogProps) {
    const workspace = useTeamAccountWorkspace();
    const [isOpen, setIsOpen] = useState(false);
    const [isPosting, setIsPosting] = useState(false);

    const zero = useZero();

    // Note: For proper BlockNote integration, create editor instance here
    // const editor = useCreateBlockNote();

    // Function to convert BlockNote blocks to markdown
    const convertBlocksToMarkdown = async (blocks: any): Promise<string> => {
        try {
            // TODO: Replace with proper BlockNote conversion
            // const markdown = await editor.blocksToMarkdownLossy(blocks);
            // return markdown;

            // Temporary fallback - convert blocks to plain text
            if (Array.isArray(blocks)) {
                return blocks.map((block: any) => {
                    if (block.content) {
                        if (Array.isArray(block.content)) {
                            return block.content.map((item: any) => 
                                typeof item === 'string' ? item : item.text || ''
                            ).join('');
                        }
                        return typeof block.content === 'string' ? block.content : '';
                    }
                    return '';
                }).join('\n\n');
            }
            
            return typeof blocks === 'string' ? blocks : JSON.stringify(blocks);
        } catch (error) {
            console.error('Error converting blocks to markdown:', error);
            return '';
        }
    };

    const [accounts_memberships] = useZeroQuery(
        zero.query.accounts_memberships
        .where('account_id', workspace.account.id)
        ,
        {
          ttl: '10m'
        }
      );

      const [company_task_statuses] = useZeroQuery(
        zero.query.company_task_statuses
        .where("company_id", "=", workspace.account.id)
        ,{
            ttl: '10m'
        }
      );

    // Get user's timezone as default
    const userTimezone = getUserTimezone();
    const defaultTimezone = TIMEZONE_OPTIONS.find(tz => tz.value === userTimezone)?.value || 'UTC';
    
    // Parse scheduled_publishing_time if it exists (format: "YYYY-MM-DD")
    const getDefaultScheduleDate = () => {
        if (companyContent?.scheduled_publishing_time) {
            try {
                return new Date(companyContent.scheduled_publishing_time);
            } catch (error) {
                console.error('Error parsing scheduled_publishing_time:', error);
                return undefined;
            }
        }
        return undefined;
    };
   
    const form = useForm<PostScheduleData>({
        resolver: zodResolver(PostScheduleSchema),
        defaultValues: {
            taskStatus: "",
            postNow: true,
            scheduleDate: getDefaultScheduleDate(),
            scheduleTime: "",
            timezone: defaultTimezone,
            requireApproval: "",
        },
    });

    const watchPostNow = form.watch("postNow");
    const watchScheduleDate = form.watch("scheduleDate");
    const watchScheduleTime = form.watch("scheduleTime");
    
    // Check if selected date/time is in the past
    const isPastDateTime = () => {
        if (watchPostNow || !watchScheduleDate || !watchScheduleTime) return false;
        
        const timeParts = watchScheduleTime.split(':');
        const hours = parseInt(timeParts[0] || '0', 10);
        const minutes = parseInt(timeParts[1] || '0', 10);
        
        const selectedDateTime = new Date(watchScheduleDate);
        selectedDateTime.setHours(hours, minutes, 0, 0);
        
        return selectedDateTime < new Date();
    };

    const onSubmit = async (data: PostScheduleData) => {
        if (!companyContent?.content_editor_template || !companyContent?.channel) {
            toast.error('No content or channel specified for posting');
            return;
        }

        setIsPosting(true);
        
        try {
            // Convert BlockNote blocks to markdown
            const postContent = await convertBlocksToMarkdown(companyContent.content_editor_template);

            // Ensure platform name is correctly mapped for Ayrshare API
            let platform = companyContent.channel;
            
            // Map platform names to Ayrshare expected values
            const platformMapping: Record<string, string> = {
                'twitter': 'twitter',
                'x': 'twitter',  // In case it's stored as 'x'
                'linkedin': 'linkedin',
                'facebook': 'facebook',
                'instagram': 'instagram'
            };
            
            platform = platformMapping[platform.toLowerCase()] || platform;

            // Prepare schedule date if not posting now
            let scheduleDate: string | undefined;
            if (!data.postNow && data.scheduleDate && data.scheduleTime && data.timezone) {
                // Parse the time (HH:MM format)
                const timeParts = data.scheduleTime.split(':');
                const hours = parseInt(timeParts[0] || '0', 10);
                const minutes = parseInt(timeParts[1] || '0', 10);
                
                // Create date object with selected date and time
                const scheduledDateTime = new Date(data.scheduleDate);
                scheduledDateTime.setHours(hours, minutes, 0, 0);
                
                // Convert to UTC for Ayrshare API
                scheduleDate = scheduledDateTime.toISOString();
            }

            // Prepare the post data for Ayrshare API
            const postData = {
                post: postContent,
                platforms: [platform],
                mediaUrls: (companyContent.image_urls as string[]) || [],
                companyId: workspace.account.id,
                profileKey: selectedProfile.profile_key,
                scheduleDate: scheduleDate,
                requiresApproval: data.requireApproval ? true : false,
            };

            const actionType = data.postNow ? 'posting' : 'scheduling';
            console.log(`${actionType} to ${platform} via Ayrshare:`, {
                ...postData,
                profileKey: selectedProfile.profile_key,
                userId: workspace.account.id
            });

            // Call the Ayrshare API
            const response = await fetch('/api/integrations/ayrshare/post', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(postData),
            });

            const result = await response.json();
            console.log('Ayrshare API response:', result);

            if (response.ok && result.success) {
                const successMessage = data.postNow 
                    ? `Post published successfully to ${platform}!`
                    : `Post scheduled successfully for ${platform}!`;
                
                toast.success(successMessage);
                
                // Extract post URL from the structured response
                let publishedUrl = '';
                if (result.primaryPostUrl) {
                    publishedUrl = result.primaryPostUrl;
                } else if (result.postUrls && result.postUrls[platform]) {
                    publishedUrl = result.postUrls[platform];
                }
                
                console.log('Extracted published URL:', publishedUrl);
                console.log('Available URLs by platform:', result.postUrls);
                console.log('result.data', result.data);
                console.log('companyContent?.id', companyContent?.id);
                // Update company content with publishing info and optional task status
                if (companyContent?.id) {
                    const updateValues: any = {
                        published_by: workspace.user.id,
                        published_url: publishedUrl
                    };
                    
                    if (data.postNow) {
                        updateValues.is_published = true;
                        updateValues.published_at = Date.now();
                    } else {
                        updateValues.is_scheduled = true;
                        updateValues.scheduled_at = Date.now();
                        updateValues.schedule_date = new Date(scheduleDate || '').getTime();
                        updateValues.ayrshare_post_id = result.data.posts[0].id; // Store Ayrshare post ID for future operations
                    }
                    
                    // Add task status if selected
                    if (data.taskStatus) {
                        updateValues.status = data.taskStatus;
                    }
                    console.log('updateValues', updateValues);
                    
                    try {
                        await zero.mutate.company_content.update({
                            id: companyContent.id,
                            values: updateValues
                        });
                        console.log('✅ Database update successful for content:', companyContent.id);
                    } catch (error) {
                        console.error('❌ Database update failed:', error);
                        toast.error('Failed to update content status');
                    }
                }
                
                setIsOpen(false);
                form.reset();
            } else {
                const errorMessage = result.error || result.message || `Failed to ${actionType} post`;
                toast.error(errorMessage);
                console.error('Ayrshare API error:', {
                    status: response.status,
                    statusText: response.statusText,
                    result
                });
            }
        } catch (error) {
            console.error('Error posting to Ayrshare:', error);
            toast.error('Failed to publish post');
        } finally {
            setIsPosting(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Post
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>Publish Post</DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        
                        {/* Post Now vs Schedule Toggle */}
                        <FormField
                            control={form.control}
                            name="postNow"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                    <div className="space-y-0.5">
                                        <FormLabel className="text-base">Post Now</FormLabel>
                                        <div className="text-sm text-muted-foreground">
                                            Publish immediately or schedule for later
                                        </div>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        {/* Scheduling Fields - Only show when not posting now */}
                        {!watchPostNow && (
                            <div className="space-y-4">
                                {/* Date Picker */}
                                <FormField
                                    control={form.control}
                                    name="scheduleDate"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-col">
                                            <FormLabel>Schedule Date</FormLabel>
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <FormControl>
                                                        <Button
                                                            variant={"outline"}
                                                            className={cn(
                                                                "w-full pl-3 text-left font-normal",
                                                                !field.value && "text-muted-foreground"
                                                            )}
                                                        >
                                                            {field.value ? (
                                                                format(field.value, "PPP")
                                                            ) : (
                                                                <span>Pick a date</span>
                                                            )}
                                                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                                        </Button>
                                                    </FormControl>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-auto p-0" align="start">
                                                    <Calendar
                                                        mode="single"
                                                        selected={field.value}
                                                        onSelect={field.onChange}
                                                        disabled={(date) =>
                                                            date < new Date("1900-01-01")
                                                        }
                                                        initialFocus
                                                    />
                                                </PopoverContent>
                                            </Popover>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Time Picker */}
                                <FormField
                                    control={form.control}
                                    name="scheduleTime"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Schedule Time</FormLabel>
                                            <FormControl>
                                                <div className="flex items-center space-x-2">
                                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                                    <Input
                                                        type="time"
                                                        className="flex-1"
                                                        {...field}
                                                        placeholder="HH:MM"
                                                    />
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* Timezone Selector */}
                                <FormField
                                    control={form.control}
                                    name="timezone"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Timezone</FormLabel>
                                            <Select onValueChange={field.onChange} value={field.value}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select timezone" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {TIMEZONE_OPTIONS.map((timezone) => (
                                                        <SelectItem key={timezone.value} value={timezone.value}>
                                                            {timezone.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        )}

                        {/* Warning for past dates */}
                        {isPastDateTime() && (
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <div className="flex items-center space-x-2">
                                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                    <p className="text-yellow-800 font-medium">Past Date Selected</p>
                                </div>
                                <p className="text-yellow-700 text-sm mt-1">
                                    The selected date and time is in the past. The post will be published immediately when scheduled.
                                </p>
                            </div>
                        )}

                        {/* Task Status Dropdown */}
                        <FormField
                            control={form.control}
                            name="taskStatus"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Change Task Status (Optional)</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select task status" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {company_task_statuses?.map((status: any) => (
                                                <SelectItem key={status.id} value={status.name}>
                                                    {status.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Submit Button */}
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isPosting}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isPosting}>
                                {isPosting ? (watchPostNow ? 'Posting...' : 'Scheduling...') : (watchPostNow ? 'Post Now' : 'Schedule Post')}
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}