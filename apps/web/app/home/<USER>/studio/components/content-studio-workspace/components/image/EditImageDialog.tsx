'use client'
import React, { useEffect, useState } from 'react';
import { Dialog, DialogContent } from "@kit/ui/dialog";
// import { PinturaEditor } from "@pqina/react-pintura";
// import { getEditorDefaults, createMarkupEditorShapeStyleControls } from "@pqina/pintura";
// import './pintura.css'
import { listAssetFolders, listFolderContents } from '~/services/storage';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface EditImageDialogProps {
  imageUrl: string;
  isOpen: boolean;
  onClose: () => void;
  onSave: (file: File) => Promise<void>;
}

export function EditImageDialog({ imageUrl, isOpen, onClose, onSave }: EditImageDialogProps) {
    console.log({imageUrl})
  const workspace = useTeamAccountWorkspace();
  const [imageURLs, setImageURLs] = useState<string[]>([]);


  useEffect(() => {
    const fetchFolders = async () => {

      const folders = await listAssetFolders(workspace.account.id);
      if(folders) {
        folders.forEach(async (folder) => {
          const contents = await listFolderContents(folder.name, workspace.account.id);
          setImageURLs(contents.map((content) => content.url));
        });
      }
      console.log('folders', folders);
    };
    fetchFolders();
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-[90vw] h-screen max-h-[90vh] p-0">
        {/* <PinturaEditor
          {...getEditorDefaults()}
          markupEditorShapeStyleControls={createMarkupEditorShapeStyleControls({})}
          stickers={imageURLs ? imageURLs : [] }
          src={imageUrl}
          onProcess={async ({ dest }) => {
            // Make sure dest is a File object
            if (dest instanceof Blob) {
              const file = new File([dest], 'edited-image.jpg', { type: 'image/jpeg' });
              await onSave(file);
              onClose();
            }
          }}
        /> */}
      </DialogContent>
    </Dialog>
  );
} 