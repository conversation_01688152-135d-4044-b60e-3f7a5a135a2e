'use server';

import { uploadNew<PERSON>rand<PERSON>ont, deleteBrandFont, uploadNewBrandLogo, deleteBrandLogo } from '~/services/brand';
import { getLogger } from '@kit/shared/logger';

export async function uploadFontAction(companyId: string, file: File) {
  const logger = await getLogger();
  const ctx = { name: 'upload-font-action', companyId, fileName: file.name };
  
  logger.info(ctx, 'Starting font upload action...');
  
  try {
    const result = await uploadNewBrandFont(companyId, file);
    logger.info(ctx, 'Font upload action completed successfully');
    return { success: true, data: result };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Font upload action failed');
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
}

export async function deleteFontAction(companyId: string, fileName: string) {
  const logger = await getLogger();
  const ctx = { name: 'delete-font-action', companyId, fileName };
  
  logger.info(ctx, 'Starting font delete action...');
  
  try {
    const result = await deleteBrandFont(companyId, fileName);
    logger.info(ctx, 'Font delete action completed successfully');
    return { success: true, data: result };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Font delete action failed');
    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' };
  }
}

export async function uploadLogoAction(companyId: string, file: File) {
  const logger = await getLogger();
  const ctx = { name: 'upload-logo-action', companyId, fileName: file.name };
  
  logger.info(ctx, 'Starting logo upload action...');
  
  try {
    const result = await uploadNewBrandLogo(companyId, file);
    logger.info(ctx, 'Logo upload action completed successfully');
    return { success: true, data: result };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Logo upload action failed');
    return { success: false, error: error instanceof Error ? error.message : 'Upload failed' };
  }
}

export async function deleteLogoAction(companyId: string, fileName: string) {
  const logger = await getLogger();
  const ctx = { name: 'delete-logo-action', companyId, fileName };
  
  logger.info(ctx, 'Starting logo delete action...');
  
  try {
    const result = await deleteBrandLogo(companyId, fileName);
    logger.info(ctx, 'Logo delete action completed successfully');
    return { success: true, data: result };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Logo delete action failed');
    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' };
  }
} 