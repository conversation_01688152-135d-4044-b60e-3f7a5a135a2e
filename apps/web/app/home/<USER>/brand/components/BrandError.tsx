'use client';

import { AlertTriangle } from 'lucide-react';

interface BrandErrorProps {
  error?: Error;
}

const BrandError = ({ error }: BrandErrorProps) => {
  
  // If we're showing an error
  return (
    <div className="flex flex-col items-center justify-center rounded-lg border border-red-200 bg-red-50 p-6 text-center">
      <AlertTriangle className="mb-4 h-10 w-10 text-red-500" />
      <h3 className="mb-2 text-lg font-semibold text-red-700">Something went wrong</h3>
      <p className="mb-4 text-sm text-red-600">
        {error?.message || 'An unexpected error occurred while loading this section.'}
      </p>
      <p className="text-xs text-red-500">
        Please try refreshing the page or contact support if the problem persists.
      </p>
    </div>
  );
};

export default BrandError;
