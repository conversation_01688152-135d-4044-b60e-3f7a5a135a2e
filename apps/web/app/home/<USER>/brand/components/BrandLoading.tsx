'use client';

import { Skeleton } from '@kit/ui/skeleton';

export default function BrandLoading() {
  return (
    <div className="space-y-6 p-6">
      {/* Brand Info Section */}
      <div className="space-y-4">
        <Skeleton className="h-8 w-[250px]" />
        <Skeleton className="h-4 w-[300px]" />
      </div>

      {/* Logos Section */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-[150px]" />
        <div className="grid grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-40 w-full rounded-lg" />
          ))}
        </div>
      </div>

      {/* Fonts Section */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-[150px]" />
        <div className="grid grid-cols-2 gap-4">
          {[1, 2].map((i) => (
            <Skeleton key={i} className="h-24 w-full rounded-lg" />
          ))}
        </div>
      </div>
    </div>
  );
}
