'use client';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { If } from '@kit/ui/if';
import { AddBrand } from './add-brand';
import { Account } from '~/types/accounts';
import { BrandDisplayWithSidebar } from './BrandDisplayWithSidebar';
import { CompanyBrand } from '../types/brand';
import { BrandGeneratingLoader } from './BrandGeneratingLoader';
import { BrandErrorState } from './BrandErrorState';

export const BrandContent = () => {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();

  const [companyBrand, companyBrandResult] = useZeroQuery(
    zero.query.company_brand
    .where('company_id', '=', workspace.account.id),
    {
      ttl: '10m'
    }
  );
  const [ account ] = useZeroQuery(
    zero.query.accounts
    .where('id', workspace.account.id),
    {
      ttl: '10m'
    }
  );

  // Helper function to check if brand data is fundamentally corrupted (not just empty)
  const isBrandDataCorrupted = (brand: any): boolean => {
    // Only consider it corrupted if the brand object itself is null/undefined
    // or if it's missing basic identifying information
    if (!brand) return true;
    
    // Check if we have at least basic brand identification
    // Allow empty fields - they can be filled manually
    return false; // For now, don't treat any existing brand as corrupted
  };

  // Wait for data to load
  if (companyBrandResult.type !== 'complete') {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  // No brand exists - show add brand form
  if (companyBrand.length === 0) {
    return (
      <div className="h-full">
        <AddBrand account={account[0] as Account} />
      </div>
    );
  }

  const brandData = companyBrand[0] as any;

  // Brand is being generated - show loading state
  if (brandData?.is_generating) {
    return (
      <div className="h-full">
        <BrandGeneratingLoader />
      </div>
    );
  }

  // Only show error state if brand data is fundamentally corrupted
  if (isBrandDataCorrupted(brandData)) {
    return (
      <div className="h-full">
        <BrandErrorState 
          brandId={brandData?.id} 
          brandName={brandData?.brand_name || 'Unknown Brand'}
        />
      </div>
    );
  }

  // Show brand display with available data - empty fields can be filled manually
  const brand = brandData as CompanyBrand;
  return (
    <div className="h-full">
      <BrandDisplayWithSidebar companyBrand={brand} />
    </div>
  );
};
