'use client';

import React, { useState } from 'react';
import { AlertTriangle, RefreshCw, Trash2 } from 'lucide-react';
import { useZero } from '~/hooks/use-zero';

interface BrandErrorStateProps {
  brandId: string;
  brandName?: string;
}

export function BrandErrorState({ brandId, brandName }: BrandErrorStateProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const zero = useZero();

  const handleTryAgain = async () => {
    setIsDeleting(true);
    
    try {
      console.log(`Deleting corrupted brand: ${brandId}`);
      
      await zero.mutate.company_brand.delete({
        id: brandId
      });
      
      // The parent component will automatically re-render and show the AddBrand component
      
    } catch (error) {
      console.error('Error deleting corrupted brand:', error);
      setIsDeleting(false);
    }
  };

  return (
    <div className="h-full flex items-center justify-center">
      <div className="max-w-md text-center space-y-6">
        <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
          <AlertTriangle className="w-8 h-8 text-red-600" />
        </div>
        
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">
            Error loading brand
          </h2>
          <p className="text-gray-600">
            There was a problem loading your brand data. This might be due to corrupted or incomplete data.
          </p>
          {brandName && (
            <p className="text-sm text-gray-500">
              Brand: {brandName}
            </p>
          )}
        </div>
        
        <div className="space-y-3">
          <button
            onClick={handleTryAgain}
            disabled={isDeleting}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isDeleting ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                Removing corrupted data...
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4" />
                Try Again (Remove corrupted data)
              </>
            )}
          </button>
          
          <p className="text-xs text-gray-500">
            This will remove the corrupted brand data and allow you to create a new brand.
          </p>
        </div>
      </div>
    </div>
  );
}