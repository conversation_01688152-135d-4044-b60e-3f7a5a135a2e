'use client';

import React, { useState } from 'react';
import { Edit2, Save, X, Plus, Trash2, Package } from 'lucide-react';
import { Product } from '../../types/brand';
import { useZero } from '~/hooks/use-zero';

interface ProductCatalogSectionProps {
  products: Product[];
  brandId: string;
}

export function ProductCatalogSection({ products, brandId }: ProductCatalogSectionProps) {
  const [editingProduct, setEditingProduct] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<string, any>>({});
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  const [editingCustomField, setEditingCustomField] = useState<string | null>(null);
  const [customFieldProductId, setCustomFieldProductId] = useState<string | null>(null);
  const [isAddingCustomField, setIsAddingCustomField] = useState<Record<string, boolean>>({});
  const [customFieldName, setCustomFieldName] = useState('');
  const zero = useZero();

  // Ensure products is always an array
  const safeProducts = products || [];

  // Standard product fields that cannot be deleted
  const standardProductFields = ['id', 'name', 'description', 'target_audience', 'key_features'];

  const handleEditStart = (productId: string, product: Product) => {
    setEditingProduct(productId);
    setEditValues({ 
      ...product,
      key_features: product.key_features?.join('\n') || ''
    });
  };

  const handleEditSave = (productId: string) => {
    const updatedProduct = {
      ...editValues,
      key_features: editValues.key_features?.split('\n').filter((f: string) => f.trim()) || []
    };
    console.log(`Saving product ${productId}:`, updatedProduct);
    
    const updatedProducts = safeProducts.map(p => 
      p.id === productId ? updatedProduct : p
    );
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        product_catalog: updatedProducts
      }
    });
    
    setEditingProduct(null);
    setEditValues({});
  };

  const handleEditCancel = () => {
    setEditingProduct(null);
    setEditValues({});
  };

  const handleAddProduct = () => {
    setIsAddingProduct(true);
    setEditValues({
      id: `product-${Date.now()}`,
      name: '',
      description: '',
      target_audience: '',
      key_features: ''
    });
  };

  const handleAddProductSave = () => {
    const newProduct = {
      ...editValues,
      key_features: editValues.key_features?.split('\n').filter((f: string) => f.trim()) || []
    };
    console.log('Adding new product:', newProduct);
    
    const updatedProducts = [...safeProducts, newProduct];
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        product_catalog: updatedProducts
      }
    });
    
    setIsAddingProduct(false);
    setEditValues({});
  };

  const handleAddProductCancel = () => {
    setIsAddingProduct(false);
    setEditValues({});
  };

  const handleDeleteProduct = (productId: string) => {
    console.log('Deleting product:', productId);
    const updatedProducts = safeProducts.filter(p => p.id !== productId);
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        product_catalog: updatedProducts
      }
    });
  };

  // Product-level custom field handlers
  const getProductCustomFields = (product: Product) => {
    return Object.keys(product).filter(key => {
      if (standardProductFields.includes(key)) return false;
      const value = product[key];
      return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' || value === null || value === undefined;
    });
  };

  const handleProductCustomFieldEditStart = (productId: string, field: string, value: any) => {
    setEditingCustomField(field);
    setCustomFieldProductId(productId);
    setEditValues({ ...editValues, [field]: value });
  };

  const handleProductCustomFieldEditSave = (productId: string, field: string) => {
    console.log(`Saving product ${productId} field ${field}:`, editValues[field]);
    
    const updatedProducts = safeProducts.map(p => 
      p.id === productId ? { ...p, [field]: editValues[field] } : p
    );
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        product_catalog: updatedProducts
      }
    });
    
    setEditingCustomField(null);
    setCustomFieldProductId(null);
  };

  const handleProductCustomFieldEditCancel = () => {
    setEditingCustomField(null);
    setCustomFieldProductId(null);
    setEditValues({});
  };

  const handleProductCustomFieldValueChange = (field: string, value: any) => {
    setEditValues({ ...editValues, [field]: value });
  };

  const handleProductCustomFieldDelete = (productId: string, field: string) => {
    console.log(`Deleting product ${productId} field: ${field}`);
    
    const updatedProducts = safeProducts.map(p => {
      if (p.id === productId) {
        const updatedProduct = { ...p };
        delete updatedProduct[field];
        return updatedProduct;
      }
      return p;
    });
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        product_catalog: updatedProducts
      }
    });
  };

  const handleStartAddingCustomField = (productId: string) => {
    setIsAddingCustomField({ ...isAddingCustomField, [productId]: true });
    setCustomFieldName('');
  };

  const handleAddProductCustomField = (productId: string) => {
    if (customFieldName.trim() && !standardProductFields.includes(customFieldName)) {
      console.log(`Added custom field ${customFieldName} to product ${productId}`);
      
      const updatedProducts = safeProducts.map(p => 
        p.id === productId ? { ...p, [customFieldName]: '' } : p
      );
      
      zero.mutate.company_brand.update({
        id: brandId,
        values: {
          product_catalog: updatedProducts
        }
      });
      
      setEditingCustomField(customFieldName);
      setCustomFieldProductId(productId);
      setEditValues({ ...editValues, [customFieldName]: '' });
      setIsAddingCustomField({ ...isAddingCustomField, [productId]: false });
      setCustomFieldName('');
    }
  };

  const handleCancelAddingCustomField = (productId: string) => {
    setIsAddingCustomField({ ...isAddingCustomField, [productId]: false });
    setCustomFieldName('');
  };

  const renderProductCard = (product: Product) => {
    const isEditing = editingProduct === product.id;

    return (
      <div key={product.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
              <input
                type="text"
                value={editValues.name || ''}
                onChange={(e) => setEditValues({ ...editValues, name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter product name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={editValues.description || ''}
                onChange={(e) => setEditValues({ ...editValues, description: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Enter product description"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
              <input
                type="text"
                value={editValues.target_audience || ''}
                onChange={(e) => setEditValues({ ...editValues, target_audience: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter target audience"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Key Features</label>
              <textarea
                value={editValues.key_features || ''}
                onChange={(e) => setEditValues({ ...editValues, key_features: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
                placeholder="Enter key features, one per line"
              />
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave(product.id)}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div>
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                <Package size={18} className="text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => handleEditStart(product.id, product)}
                  className="text-gray-400 hover:text-gray-600 p-1"
                >
                  <Edit2 size={14} />
                </button>
                <button
                  onClick={() => handleDeleteProduct(product.id)}
                  className="text-gray-400 hover:text-red-600 p-1"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            </div>
            
            <p className="text-gray-700 mb-3">{product.description}</p>
            
            <div className="mb-3">
              <span className="text-sm font-medium text-gray-700">Target Audience: </span>
              <span className="text-sm text-gray-600">{product.target_audience}</span>
            </div>
            
            {product.key_features && product.key_features.length > 0 && (
              <div>
                <span className="text-sm font-medium text-gray-700">Key Features:</span>
                <ul className="mt-1 space-y-1">
                  {product.key_features.map((feature, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="mr-2">•</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Custom Fields for this product */}
            {getProductCustomFields(product).map(field => {
              const isEditingThisField = editingCustomField === field && customFieldProductId === product.id;
              
              return (
                <div key={field} className="mt-3">
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium text-gray-700">
                      {field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    {!isEditingThisField && (
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => handleProductCustomFieldEditStart(product.id, field, product[field])}
                          className="text-gray-400 hover:text-gray-600 p-1"
                        >
                          <Edit2 size={12} />
                        </button>
                        <button
                          onClick={() => handleProductCustomFieldDelete(product.id, field)}
                          className="text-gray-400 hover:text-red-600 p-1"
                        >
                          <Trash2 size={12} />
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {isEditingThisField ? (
                    <div className="space-y-2">
                      <input
                        type="text"
                        value={editValues[field] || ''}
                        onChange={(e) => handleProductCustomFieldValueChange(field, e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        autoFocus
                      />
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleProductCustomFieldEditSave(product.id, field)}
                          className="flex items-center gap-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs"
                        >
                          <Save size={12} />
                          Save
                        </button>
                        <button
                          onClick={handleProductCustomFieldEditCancel}
                          className="flex items-center gap-1 px-2 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-xs"
                        >
                          <X size={12} />
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div 
                      className="p-2 bg-gray-50 rounded-lg min-h-[32px] flex items-center cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleProductCustomFieldEditStart(product.id, field, product[field])}
                    >
                      <span className="text-sm text-gray-900">
                        {product[field] || <span className="text-gray-400 italic">Not set</span>}
                      </span>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Add Custom Field inline form */}
            {isAddingCustomField[product.id] && (
              <div className="mt-3 pt-2 border-t border-gray-200">
                <div className="space-y-2">
                  <input
                    type="text"
                    value={customFieldName}
                    onChange={(e) => setCustomFieldName(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddProductCustomField(product.id);
                      }
                    }}
                    placeholder="Enter field name (e.g., warranty_period, certification)"
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    autoFocus
                  />
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleAddProductCustomField(product.id)}
                      disabled={!customFieldName.trim() || standardProductFields.includes(customFieldName)}
                      className="flex items-center gap-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-xs"
                    >
                      <Plus size={12} />
                      Add Field
                    </button>
                    <button
                      onClick={() => handleCancelAddingCustomField(product.id)}
                      className="flex items-center gap-1 px-2 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-xs"
                    >
                      <X size={12} />
                      Cancel
                    </button>
                  </div>
                  {customFieldName.trim() && standardProductFields.includes(customFieldName) && (
                    <p className="text-xs text-red-600">This field name is already used. Please choose a different name.</p>
                  )}
                </div>
              </div>
            )}

            {/* Add Custom Field button */}
            {!isAddingCustomField[product.id] && (
              <div className="mt-3 pt-2 border-t border-gray-200">
                <button
                  onClick={() => handleStartAddingCustomField(product.id)}
                  className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800"
                >
                  <Plus size={12} />
                  Add Custom Field
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderAddProductForm = () => {
    return (
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
            <input
              type="text"
              value={editValues.name || ''}
              onChange={(e) => setEditValues({ ...editValues, name: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter product name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={editValues.description || ''}
              onChange={(e) => setEditValues({ ...editValues, description: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              placeholder="Enter product description"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
            <input
              type="text"
              value={editValues.target_audience || ''}
              onChange={(e) => setEditValues({ ...editValues, target_audience: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter target audience"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Key Features</label>
            <textarea
              value={editValues.key_features || ''}
              onChange={(e) => setEditValues({ ...editValues, key_features: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={4}
              placeholder="Enter key features, one per line"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleAddProductSave}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              <Save size={14} />
              Add Product
            </button>
            <button
              onClick={handleAddProductCancel}
              className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Product Catalog</h1>
        <p className="text-gray-600">Manage your products and services</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">Products & Services</h2>
          {!isAddingProduct && (
            <button
              onClick={handleAddProduct}
              className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              <Plus size={14} />
              Add Product
            </button>
          )}
        </div>

        <div className="space-y-4">
          {isAddingProduct && renderAddProductForm()}
          
          {safeProducts.map(renderProductCard)}
          
          {!isAddingProduct && safeProducts.length === 0 && (
            <div className="text-center py-12">
              <Package size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No products yet</h3>
              <p className="text-gray-600 mb-4">Add your first product or service to get started</p>
              <button
                onClick={handleAddProduct}
                className="flex items-center gap-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto"
              >
                <Plus size={16} />
                Add Product
              </button>
            </div>
          )}
        </div>

      </div>
    </div>
  );
}