'use client';

import React, { useState } from 'react';
import { Save, Upload, X, Plus } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@kit/ui/dialog';
import { Font } from '../../types/brand';

interface FontDialogProps {
  isOpen: boolean;
  font: Font | null;
  onClose: () => void;
  onSave: (font: Font) => void;
}

export function FontDialog({ isOpen, font, onClose, onSave }: FontDialogProps) {
  const [editingFontData, setEditingFontData] = useState<Font | null>(font);
  const [newRole, setNewRole] = useState('');

  // Initialize form data when font changes
  React.useEffect(() => {
    if (font) {
      // Ensure all required properties exist with defaults
      setEditingFontData({
        id: font.id || '',
        name: font.name || '',
        family: font.family || '',
        storage_path: font.storage_path || null,
        upload_url: font.upload_url || undefined,
        font_family: font.font_family || undefined,
        font_format: font.font_format || undefined,
        roles: font.roles || [],
        usage_notes: font.usage_notes || ''
      });
    }
  }, [font]);

  const handleSave = () => {
    if (!editingFontData) return;
    onSave(editingFontData);
  };

  const handleAddRole = () => {
    if (!newRole.trim() || !editingFontData) return;
    
    const currentRoles = editingFontData.roles || [];
    const updatedRoles = [...currentRoles, newRole.trim()];
    setEditingFontData({
      ...editingFontData,
      roles: updatedRoles
    });
    setNewRole('');
  };

  const handleRemoveRole = (index: number) => {
    if (!editingFontData) return;
    
    const currentRoles = editingFontData.roles || [];
    const updatedRoles = currentRoles.filter((_, i) => i !== index);
    setEditingFontData({
      ...editingFontData,
      roles: updatedRoles
    });
  };



  if (!editingFontData) return null;

  const commonRoles = [
    'Primary Heading (H1)',
    'Secondary Heading (H2)',
    'Body Text',
    'Caption Text',
    'Button Text',
    'Navigation',
    'Logo Text'
  ];

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{font?.id ? 'Edit Font' : 'Add New Font'}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Font Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Font Name</label>
            <input
              type="text"
              value={editingFontData.name}
              onChange={(e) => setEditingFontData({
                ...editingFontData,
                name: e.target.value
              })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Satoshi Variable"
            />
          </div>

          {/* Font Family */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Font Family</label>
            <input
              type="text"
              value={editingFontData.family}
              onChange={(e) => setEditingFontData({
                ...editingFontData,
                family: e.target.value
              })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Satoshi, sans-serif"
            />
          </div>

          {/* Font File Status */}
          {editingFontData.storage_path && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Font File</label>
              <div className="flex items-center gap-2">
                <span className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                  ✓ {editingFontData.storage_path}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Font file uploaded successfully. Use the upload button in the main view to change it.
              </p>
            </div>
          )}

          {/* Font Roles */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Font Roles</label>
            
            {/* Add Role Input */}
            <div className="flex gap-2 mb-2">
              <input
                type="text"
                value={newRole}
                onChange={(e) => setNewRole(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddRole();
                  }
                }}
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Add a role (e.g., Primary Heading)"
              />
              <button
                onClick={handleAddRole}
                disabled={!newRole.trim()}
                className="px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus size={16} />
              </button>
            </div>

            {/* Common Roles Quick Add */}
            <div className="mb-3">
              <p className="text-xs text-gray-500 mb-1">Quick add:</p>
              <div className="flex flex-wrap gap-1">
                {commonRoles
                  .filter(role => !(editingFontData.roles || []).includes(role))
                  .map((role) => (
                    <button
                      key={role}
                      onClick={() => {
                        const currentRoles = editingFontData.roles || [];
                        setEditingFontData({
                          ...editingFontData,
                          roles: [...currentRoles, role]
                        });
                      }}
                      className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                    >
                      + {role}
                    </button>
                  ))}
              </div>
            </div>

            {/* Current Roles */}
            {(editingFontData.roles || []).length > 0 && (
              <div className="space-y-1">
                {(editingFontData.roles || []).map((role, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-purple-50 rounded">
                    <span className="text-sm text-purple-800">{role}</span>
                    <button
                      onClick={() => handleRemoveRole(index)}
                      className="text-purple-600 hover:text-purple-800"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Usage Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Usage Notes</label>
            <textarea
              value={editingFontData.usage_notes || ''}
              onChange={(e) => setEditingFontData({
                ...editingFontData,
                usage_notes: e.target.value
              })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Any special usage instructions or restrictions..."
              rows={3}
            />
          </div>
        </div>
        
        <DialogFooter>
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium flex items-center gap-2"
          >
            <Save size={16} />
            Save Font
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}