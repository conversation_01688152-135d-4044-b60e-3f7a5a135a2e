'use client';

import React, { useState } from 'react';
import { Save, Upload, X, Plus, Image } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@kit/ui/dialog';
import { Logo } from '../../types/brand';

interface LogoDialogProps {
  isOpen: boolean;
  logo: Logo | null;
  onClose: () => void;
  onSave: (logo: Logo) => void;
}

export function LogoDialog({ isOpen, logo, onClose, onSave }: LogoDialogProps) {
  const [editingLogoData, setEditingLogoData] = useState<Logo | null>(logo);
  const [newGuideline, setNewGuideline] = useState('');

  // Initialize form data when logo changes
  React.useEffect(() => {
    if (logo) {
      // Ensure all required properties exist with defaults
      setEditingLogoData({
        id: logo.id || '',
        type: logo.type || '',
        description: logo.description || '',
        storage_path: logo.storage_path || null,
        upload_url: logo.upload_url || undefined,
        usage_guidelines: logo.usage_guidelines || []
      });
    }
  }, [logo]);

  const handleSave = () => {
    if (!editingLogoData) return;
    onSave(editingLogoData);
  };

  const handleAddGuideline = () => {
    if (!newGuideline.trim() || !editingLogoData) return;
    
    const currentGuidelines = editingLogoData.usage_guidelines || [];
    const updatedGuidelines = [...currentGuidelines, newGuideline.trim()];
    setEditingLogoData({
      ...editingLogoData,
      usage_guidelines: updatedGuidelines
    });
    setNewGuideline('');
  };

  const handleRemoveGuideline = (index: number) => {
    if (!editingLogoData) return;
    
    const currentGuidelines = editingLogoData.usage_guidelines || [];
    const updatedGuidelines = currentGuidelines.filter((_, i) => i !== index);
    setEditingLogoData({
      ...editingLogoData,
      usage_guidelines: updatedGuidelines
    });
  };



  if (!editingLogoData) return null;

  const commonLogoTypes = [
    'Primary Logo',
    'Secondary Logo', 
    'Brand Mark Icon',
    'Horizontal Logo',
    'Vertical Logo',
    'White Version',
    'Black Version',
    'Monochrome Version'
  ];

  const commonGuidelines = [
    'Use on light backgrounds',
    'Use on dark backgrounds',
    'Minimum size: 24px height',
    'Clear space: 2x logo height',
    'Do not stretch or distort',
    'Do not change colors',
    'Use for digital applications',
    'Use for print applications'
  ];

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{logo?.id ? 'Edit Logo' : 'Add New Logo'}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Logo Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Logo Type</label>
            <input
              type="text"
              value={editingLogoData.type}
              onChange={(e) => setEditingLogoData({
                ...editingLogoData,
                type: e.target.value
              })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Primary Logo"
            />
            
            {/* Quick type suggestions */}
            <div className="mt-2">
              <p className="text-xs text-gray-500 mb-1">Quick select:</p>
              <div className="flex flex-wrap gap-1">
                {commonLogoTypes
                  .filter(type => type !== editingLogoData.type)
                  .slice(0, 4)
                  .map((type) => (
                    <button
                      key={type}
                      onClick={() => setEditingLogoData({
                        ...editingLogoData,
                        type: type
                      })}
                      className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                    >
                      {type}
                    </button>
                  ))}
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={editingLogoData.description}
              onChange={(e) => setEditingLogoData({
                ...editingLogoData,
                description: e.target.value
              })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe when and how this logo should be used..."
              rows={3}
            />
          </div>

          {/* Logo File Status */}
          {editingLogoData.storage_path && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Logo File</label>
              <div className="flex items-center gap-2">
                <span className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                  ✓ {editingLogoData.storage_path}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Logo file uploaded successfully. Use the upload button in the main view to change it.
              </p>
              
              {/* Logo Preview Area */}
              {editingLogoData.upload_url && (
                <div className="mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="h-24 bg-white rounded border border-gray-200 flex items-center justify-center overflow-hidden">
                    <img 
                      src={editingLogoData.upload_url} 
                      alt={editingLogoData.type}
                      className="max-h-full max-w-full object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLDivElement;
                        if (fallback) fallback.style.display = 'block';
                      }}
                    />
                    <div className="text-center hidden">
                      <Image className="w-8 h-8 text-gray-400 mx-auto mb-1" />
                      <span className="text-xs text-gray-500">Failed to load</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Usage Guidelines */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Usage Guidelines</label>
            
            {/* Add Guideline Input */}
            <div className="flex gap-2 mb-2">
              <input
                type="text"
                value={newGuideline}
                onChange={(e) => setNewGuideline(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddGuideline();
                  }
                }}
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Add a usage guideline"
              />
              <button
                onClick={handleAddGuideline}
                disabled={!newGuideline.trim()}
                className="px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus size={16} />
              </button>
            </div>

            {/* Common Guidelines Quick Add */}
            <div className="mb-3">
              <p className="text-xs text-gray-500 mb-1">Quick add:</p>
              <div className="flex flex-wrap gap-1">
                {commonGuidelines
                  .filter(guideline => !(editingLogoData.usage_guidelines || []).includes(guideline))
                  .slice(0, 4)
                  .map((guideline) => (
                    <button
                      key={guideline}
                      onClick={() => {
                        const currentGuidelines = editingLogoData.usage_guidelines || [];
                        setEditingLogoData({
                          ...editingLogoData,
                          usage_guidelines: [...currentGuidelines, guideline]
                        });
                      }}
                      className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                    >
                      + {guideline}
                    </button>
                  ))}
              </div>
            </div>

            {/* Current Guidelines */}
            {(editingLogoData.usage_guidelines || []).length > 0 && (
              <div className="space-y-1">
                {(editingLogoData.usage_guidelines || []).map((guideline, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                    <span className="text-sm text-blue-800 flex items-start">
                      <span className="mr-2">•</span>
                      <span>{guideline}</span>
                    </span>
                    <button
                      onClick={() => handleRemoveGuideline(index)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium flex items-center gap-2"
          >
            <Save size={16} />
            Save Logo
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}