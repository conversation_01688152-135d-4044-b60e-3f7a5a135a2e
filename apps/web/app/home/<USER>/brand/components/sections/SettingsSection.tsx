'use client';

import React, { useState } from 'react';
import { Trash2, AlertTriangle } from 'lucide-react';
import { useZero } from '~/hooks/use-zero';
import { useRouter } from 'next/navigation';

interface SettingsSectionProps {
  brandId: string;
  brandName: string;
}
const DELETE_CONFIRMATION_TEXT = "DELETE";
export function SettingsSection({ brandId, brandName }: SettingsSectionProps) {
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deleteConfirmationText, setDeleteConfirmationText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const zero = useZero();
  const router = useRouter();

  const handleDeleteBrand = async () => {
    if (deleteConfirmationText !== DELETE_CONFIRMATION_TEXT) {
      return;
    }

    setIsDeleting(true);
    
    try {
      console.log(`Deleting brand: ${brandId}`);
      
      await zero.mutate.company_brand.delete({
        id: brandId
      });
      
      
    } catch (error) {
      console.error('Error deleting brand:', error);
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirmation(false);
    setDeleteConfirmationText('');
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600">Manage your brand settings and configuration</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        
        {/* Danger Zone */}
        <div className="border border-red-200 rounded-lg p-6 bg-red-50">
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle size={20} className="text-red-600" />
            <h2 className="text-lg font-semibold text-red-900">Danger Zone</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-red-900 mb-2">Delete Brand</h3>
              <p className="text-sm text-red-700 mb-4">
                Permanently delete this brand and all associated data. This action cannot be undone.
              </p>
              
              {!showDeleteConfirmation ? (
                <button
                  onClick={() => setShowDeleteConfirmation(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  <Trash2 size={16} />
                  Delete Brand
                </button>
              ) : (
                <div className="space-y-4 p-4 bg-white border border-red-300 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertTriangle size={16} className="text-red-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-red-900 mb-1">Confirm Brand Deletion</h4>
                      <p className="text-sm text-red-700 mb-3">
                        This will permanently delete the brand &quot;{brandName}&quot; and all its data including:
                      </p>
                      <ul className="text-sm text-red-700 list-disc list-inside space-y-1 mb-3">
                        <li>Brand profile information</li>
                        <li>Messaging strategy</li>
                        <li>Visual identity assets</li>
                        <li>Product catalog</li>
                        <li>Prompt library</li>
                        <li>All custom fields</li>
                      </ul>
                      <p className="text-sm text-red-700 mb-3">
                        <strong>This action cannot be undone.</strong>
                      </p>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-red-900 mb-2">
                      Type &quot;DELETE&quot; to confirm deletion:
                    </label>
                    <input
                      type="text"
                      value={deleteConfirmationText}
                      onChange={(e) => setDeleteConfirmationText(e.target.value)}
                      className="w-full p-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder={brandName}
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={handleDeleteBrand}
                      disabled={deleteConfirmationText !== DELETE_CONFIRMATION_TEXT || isDeleting}
                      className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <Trash2 size={16} />
                      {isDeleting ? 'Deleting...' : 'Delete Brand Forever'}
                    </button>
                    <button
                      onClick={handleCancelDelete}
                      disabled={isDeleting}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}