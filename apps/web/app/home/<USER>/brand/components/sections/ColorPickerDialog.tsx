'use client';

import React, { useState, useRef } from 'react';
import { Save } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@kit/ui/dialog';
import { Color } from '../../types/brand';

interface ColorPickerDialogProps {
  isOpen: boolean;
  color: Color | null;
  onClose: () => void;
  onSave: (updatedColor: Color) => void;
}

export function ColorPickerDialog({ isOpen, color, onClose, onSave }: ColorPickerDialogProps) {
  const [editingColorData, setEditingColorData] = useState<Color | null>(color);
  const [hue, setHue] = useState(0);
  const [saturation, setSaturation] = useState(100);
  const [brightness, setBrightness] = useState(100);
  const [hexInput, setHexInput] = useState('');
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Preset colors matching the design
  const presetColors = [
    ['#E53E3E', '#FF8C00', '#FFD700', '#8B4513', '#32CD32', '#228B22', '#9932CC', '#8A2BE2'],
    ['#4169E1', '#00CED1', '#90EE90', '#000000', '#2F4F4F', '#696969', '#D3D3D3', '#FFFFFF']
  ];

  // Color conversion utilities
  const hslToHex = (h: number, s: number, l: number) => {
    l /= 100;
    const a = s * Math.min(l, 1 - l) / 100;
    const f = (n: number) => {
      const k = (n + h / 30) % 12;
      const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
      return Math.round(255 * color).toString(16).padStart(2, '0');
    };
    return `#${f(0)}${f(8)}${f(4)}`;
  };

  const hexToHsl = (hex: string) => {
    const r = parseInt(hex.slice(1, 3), 16) / 255;
    const g = parseInt(hex.slice(3, 5), 16) / 255;
    const b = parseInt(hex.slice(5, 7), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  };

  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1] || '0', 16),
      g: parseInt(result[2] || '0', 16),
      b: parseInt(result[3] || '0', 16)
    } : null;
  };

  const rgbToHex = (r: number, g: number, b: number) => {
    return "#" + [r, g, b].map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    }).join("");
  };

  const getCurrentColor = () => {
    // If hex input is a complete valid hex, use it, otherwise use HSL
    if (hexInput.length === 7 && /^#[0-9A-Fa-f]{6}$/.test(hexInput)) {
      return hexInput;
    }
    return hslToHex(hue, saturation, brightness);
  };

  // Update hex input when HSL changes (from sliders/canvas)
  React.useEffect(() => {
    const newHex = hslToHex(hue, saturation, brightness);
    // Only update hex input if user isn't currently typing
    if (document.activeElement?.tagName !== 'INPUT' || document.activeElement?.getAttribute('type') !== 'text') {
      setHexInput(newHex);
    }
  }, [hue, saturation, brightness]);

  // Initialize HSL values when color changes
  React.useEffect(() => {
    if (color) {
      setEditingColorData({ ...color });
      const hsl = hexToHsl(color.values.hex);
      setHue(hsl.h);
      setSaturation(hsl.s);
      setBrightness(hsl.l);
      setHexInput(color.values.hex);
    }
  }, [color]);

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const newSaturation = (x / canvas.width) * 100;
    const newBrightness = 100 - (y / canvas.height) * 100;
    
    setSaturation(Math.max(0, Math.min(100, newSaturation)));
    setBrightness(Math.max(0, Math.min(100, newBrightness)));
  };

  const handleSave = () => {
    if (!editingColorData) return;
    
    const newHex = getCurrentColor();
    const rgb = hexToRgb(newHex);
    
    const updatedColor = {
      ...editingColorData,
      values: {
        ...editingColorData.values,
        hex: newHex,
        rgb: rgb ? `R${rgb.r} G${rgb.g} B${rgb.b}` : editingColorData.values.rgb
      }
    };

    onSave(updatedColor);
  };

  if (!editingColorData) return null;

  const currentColor = getCurrentColor();
  const rgb = hexToRgb(currentColor);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Color</DialogTitle>
        </DialogHeader>
        
        {/* Color Picker Area */}
        <div className="space-y-4">
          {/* Main Color Gradient */}
          <div className="relative">
            <canvas
              ref={canvasRef}
              width={320}
              height={200}
              className="w-full h-48 rounded-lg border border-gray-300 cursor-crosshair"
              onClick={handleCanvasClick}
              style={{
                background: `linear-gradient(to top, #000, transparent), linear-gradient(to right, #fff, hsl(${hue}, 100%, 50%))`
              }}
            />
            {/* Color Picker Circle */}
            <div
              className="absolute w-4 h-4 border-2 border-white rounded-full shadow-lg pointer-events-none"
              style={{
                left: `${(saturation / 100) * 100}%`,
                top: `${100 - (brightness / 100) * 100}%`,
                transform: 'translate(-50%, -50%)'
              }}
            />
          </div>
          
          {/* Hue Strip */}
          <div className="relative h-4 rounded-lg overflow-hidden">
            <input
              type="range"
              min="0"
              max="360"
              value={hue}
              onChange={(e) => setHue(parseInt(e.target.value))}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <div
              className="h-full rounded-lg"
              style={{
                background: 'linear-gradient(to right, #ff0000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff, #ff0000)'
              }}
            />
            <div
              className="absolute top-0 w-1 h-full bg-white border border-gray-300 rounded-sm"
              style={{ left: `${(hue / 360) * 100}%`, transform: 'translateX(-50%)' }}
            />
          </div>
          
          {/* Color Preview */}
          <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
            <div
              className="w-12 h-12 rounded-lg border border-gray-300"
              style={{ backgroundColor: currentColor }}
            />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">Preview</div>
              <div className="text-xs text-gray-600">{currentColor.toUpperCase()}</div>
            </div>
          </div>
          
          {/* Input Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hex</label>
              <input
                type="text"
                value={hexInput}
                onChange={(e) => {
                  const value = e.target.value;
                  // Allow typing partial hex values
                  if (/^#[0-9A-Fa-f]*$/.test(value) && value.length <= 7) {
                    setHexInput(value);
                    // Only update HSL if we have a complete hex color
                    if (value.length === 7) {
                      const hsl = hexToHsl(value);
                      setHue(hsl.h);
                      setSaturation(hsl.s);
                      setBrightness(hsl.l);
                    }
                  }
                }}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                placeholder="#000000"
              />
            </div>
            
            {rgb && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">RGB</label>
                <div className="grid grid-cols-3 gap-1">
                  <input
                    type="number"
                    min="0"
                    max="255"
                    value={rgb.r}
                    onChange={(e) => {
                      const newValue = parseInt(e.target.value) || 0;
                      const clampedValue = Math.max(0, Math.min(255, newValue));
                      const newColor = rgbToHex(clampedValue, rgb.g, rgb.b);
                      const hsl = hexToHsl(newColor);
                      setHue(hsl.h);
                      setSaturation(hsl.s);
                      setBrightness(hsl.l);
                    }}
                    className="w-full p-1 border border-gray-300 rounded text-xs"
                    placeholder="R"
                  />
                  <input
                    type="number"
                    min="0"
                    max="255"
                    value={rgb.g}
                    onChange={(e) => {
                      const newValue = parseInt(e.target.value) || 0;
                      const clampedValue = Math.max(0, Math.min(255, newValue));
                      const newColor = rgbToHex(rgb.r, clampedValue, rgb.b);
                      const hsl = hexToHsl(newColor);
                      setHue(hsl.h);
                      setSaturation(hsl.s);
                      setBrightness(hsl.l);
                    }}
                    className="w-full p-1 border border-gray-300 rounded text-xs"
                    placeholder="G"
                  />
                  <input
                    type="number"
                    min="0"
                    max="255"
                    value={rgb.b}
                    onChange={(e) => {
                      const newValue = parseInt(e.target.value) || 0;
                      const clampedValue = Math.max(0, Math.min(255, newValue));
                      const newColor = rgbToHex(rgb.r, rgb.g, clampedValue);
                      const hsl = hexToHsl(newColor);
                      setHue(hsl.h);
                      setSaturation(hsl.s);
                      setBrightness(hsl.l);
                    }}
                    className="w-full p-1 border border-gray-300 rounded text-xs"
                    placeholder="B"
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* Preset Colors */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Preset Colors</label>
            <div className="space-y-2">
              {presetColors.map((row, rowIndex) => (
                <div key={rowIndex} className="flex gap-2">
                  {row.map((color, colorIndex) => (
                    <button
                      key={colorIndex}
                      onClick={() => {
                        const hsl = hexToHsl(color);
                        setHue(hsl.h);
                        setSaturation(hsl.s);
                        setBrightness(hsl.l);
                      }}
                      className="w-8 h-8 rounded border border-gray-300 hover:scale-110 transition-transform"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              ))}
            </div>
          </div>
          
          {/* Color Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Color Name</label>
            <input
              type="text"
              value={editingColorData.name}
              onChange={(e) => {
                setEditingColorData({
                  ...editingColorData,
                  name: e.target.value
                });
              }}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter color name"
            />
          </div>
          
          {/* Usage Guidelines */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Usage Guidelines</label>
            <textarea
              value={editingColorData.usage_guidelines || ''}
              onChange={(e) => {
                setEditingColorData({
                  ...editingColorData,
                  usage_guidelines: e.target.value
                });
              }}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe when and how this color should be used..."
              rows={3}
            />
          </div>
        </div>
        
        <DialogFooter>
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium flex items-center gap-2"
          >
            <Save size={16} />
            Save Color
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}