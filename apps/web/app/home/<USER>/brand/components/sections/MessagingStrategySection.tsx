'use client';

import React, { useState } from 'react';
import { Edit2, Save, X, Plus, Trash2 } from 'lucide-react';
import { MessagingStrategy } from '../../types/brand';
import { useZero } from '~/hooks/use-zero';
import { EditableField } from '../shared/EditableField';
import { CustomFieldManager } from '../shared/CustomFieldManager';

interface MessagingStrategySectionProps {
  messagingStrategy: MessagingStrategy;
  brandId: string;
}

export function MessagingStrategySection({ messagingStrategy, brandId }: MessagingStrategySectionProps) {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<string, any>>({});
  const zero = useZero();

  const standardFields = ['voice', 'differentiators', 'style_guide_reference'];

  const handleEditStart = (field: string, value: any) => {
    setEditingField(field);
    setEditValues({ ...editValues, [field]: value });
  };

  const handleEditSave = (field: string) => {
    console.log(`Saving ${field}:`, editValues[field]);
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        messaging_strategy: {
          ...messagingStrategy,
          [field]: editValues[field]
        }
      }
    });
    setEditingField(null);
  };

  const handleEditCancel = () => {
    setEditingField(null);
    setEditValues({});
  };

  const handleValueChange = (field: string, value: any) => {
    setEditValues({ ...editValues, [field]: value });
  };

  const handleDeleteField = (field: string) => {
    console.log(`Deleting field: ${field}`);
    const updatedStrategy = { ...messagingStrategy };
    delete updatedStrategy[field];
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        messaging_strategy: updatedStrategy
      }
    });
  };

  const handleAddCustomField = (fieldName: string) => {
    console.log('Added custom field:', fieldName);
    setEditingField(fieldName);
    setEditValues({ ...editValues, [fieldName]: '' });
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        messaging_strategy: {
          ...messagingStrategy,
          [fieldName]: ''
        }
      }
    });
  };

  const renderVoiceSection = () => {
    const isEditing = editingField === 'voice';
    const voices = messagingStrategy.voice || [];
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">Brand Voice</label>
          {!isEditing && (
            <button
              onClick={() => handleEditStart('voice', voices)}
              className="text-gray-400 hover:text-gray-600 p-1"
            >
              <Edit2 size={14} />
            </button>
          )}
        </div>
        
        <p className="text-xs text-gray-500 mb-2">The personality and character of your brand&apos;s communication style</p>
        
        {isEditing ? (
          <div className="space-y-2">
            <div className="space-y-3">
              {(editValues.voice || []).map((voice: any, index: number) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={voice.name || ''}
                    onChange={(e) => {
                      const newVoices = [...(editValues.voice || [])];
                      newVoices[index] = { ...voice, name: e.target.value };
                      setEditValues({ ...editValues, voice: newVoices });
                    }}
                    placeholder="Voice name"
                    className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="text"
                    value={voice.description || ''}
                    onChange={(e) => {
                      const newVoices = [...(editValues.voice || [])];
                      newVoices[index] = { ...voice, description: e.target.value };
                      setEditValues({ ...editValues, voice: newVoices });
                    }}
                    placeholder="Description"
                    className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={() => {
                      const newVoices = (editValues.voice || []).filter((_: any, i: number) => i !== index);
                      setEditValues({ ...editValues, voice: newVoices });
                    }}
                    className="p-2 text-red-500 hover:text-red-700"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              ))}
            </div>
            <button
              onClick={() => {
                const newVoices = [...(editValues.voice || []), { name: '', description: '' }];
                setEditValues({ ...editValues, voice: newVoices });
              }}
              className="flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm"
            >
              <Plus size={14} />
              Add Voice
            </button>
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave('voice')}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={() => handleEditStart('voice', voices)}
          >
            {voices.length ? (
              <div className="space-y-2">
                {voices.map((voice, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <span className="font-medium text-gray-900 min-w-0 flex-shrink-0">
                      {voice.name}:
                    </span>
                    <span className="text-gray-700">{voice.description}</span>
                  </div>
                ))}
              </div>
            ) : (
              <span className="text-gray-400 italic">No voice attributes set</span>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderDifferentiatorsSection = () => {
    const isEditing = editingField === 'differentiators';
    const differentiators = messagingStrategy.differentiators || [];
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">Differentiators</label>
          {!isEditing && (
            <button
              onClick={() => handleEditStart('differentiators', differentiators)}
              className="text-gray-400 hover:text-gray-600 p-1"
            >
              <Edit2 size={14} />
            </button>
          )}
        </div>
        
        <p className="text-xs text-gray-500 mb-2">What makes your brand unique and sets you apart from competitors</p>
        
        {isEditing ? (
          <div className="space-y-2">
            <div className="space-y-4">
              {(editValues.differentiators || []).map((diff: any, index: number) => (
                <div key={index} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <input
                      type="text"
                      value={diff.audience || ''}
                      onChange={(e) => {
                        const newDiffs = [...(editValues.differentiators || [])];
                        newDiffs[index] = { ...diff, audience: e.target.value };
                        setEditValues({ ...editValues, differentiators: newDiffs });
                      }}
                      placeholder="Target audience"
                      className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                      onClick={() => {
                        const newDiffs = (editValues.differentiators || []).filter((_: any, i: number) => i !== index);
                        setEditValues({ ...editValues, differentiators: newDiffs });
                      }}
                      className="ml-2 p-2 text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                  <textarea
                    value={diff.points?.join('\n') || ''}
                    onChange={(e) => {
                      const newDiffs = [...(editValues.differentiators || [])];
                      newDiffs[index] = { ...diff, points: e.target.value.split('\n').filter(p => p.trim()) };
                      setEditValues({ ...editValues, differentiators: newDiffs });
                    }}
                    placeholder="Enter differentiating points, one per line"
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                  />
                </div>
              ))}
            </div>
            <button
              onClick={() => {
                const newDiffs = [...(editValues.differentiators || []), { audience: '', points: [] }];
                setEditValues({ ...editValues, differentiators: newDiffs });
              }}
              className="flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm"
            >
              <Plus size={14} />
              Add Differentiator
            </button>
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave('differentiators')}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={() => handleEditStart('differentiators', differentiators)}
          >
            {differentiators.length ? (
              <div className="space-y-4">
                {differentiators.map((diff, index) => (
                  <div key={index} className="p-3 bg-white rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-2">{diff.audience}</h4>
                    <ul className="space-y-1">
                      {diff.points.map((point, pointIndex) => (
                        <li key={pointIndex} className="text-gray-700 text-sm flex items-start">
                          <span className="mr-2">•</span>
                          <span>{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            ) : (
              <span className="text-gray-400 italic">No differentiators set</span>
            )}
          </div>
        )}
      </div>
    );
  };


  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Messaging Strategy</h1>
        <p className="text-gray-600">Define how your brand communicates with different audiences</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {renderVoiceSection()}
        {renderDifferentiatorsSection()}
        
        {/* Style Guide Reference - using reusable component */}
        <EditableField
          field="style_guide_reference"
          label="Style Guide Reference"
          value={messagingStrategy.style_guide_reference}
          helpText="Additional style guidelines, references, or notes for content creation"
          isTextarea={true}
          canDelete={true}
          editingField={editingField}
          editValues={editValues}
          onEditStart={handleEditStart}
          onEditSave={handleEditSave}
          onEditCancel={handleEditCancel}
          onValueChange={handleValueChange}
          onDelete={handleDeleteField}
        />

        {/* Custom Fields Manager */}
        <CustomFieldManager
          data={messagingStrategy}
          standardFields={standardFields}
          editingField={editingField}
          editValues={editValues}
          onEditStart={handleEditStart}
          onEditSave={handleEditSave}
          onEditCancel={handleEditCancel}
          onValueChange={handleValueChange}
          onDelete={handleDeleteField}
          onAddCustomField={handleAddCustomField}
        />
      </div>
    </div>
  );
}