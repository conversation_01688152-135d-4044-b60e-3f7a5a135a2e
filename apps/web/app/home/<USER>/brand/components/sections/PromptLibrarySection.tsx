'use client';

import React, { useState } from 'react';
import { Edit2, Save, X, Plus, Trash2, BookO<PERSON>, Copy, Filter } from 'lucide-react';
import { Prompt } from '../../types/brand';
import { useZero } from '~/hooks/use-zero';
import { CustomFieldManager } from '../shared/CustomFieldManager';

interface PromptLibrarySectionProps {
  prompts: Prompt[];
  brandId: string;
}

export function PromptLibrarySection({ prompts, brandId }: PromptLibrarySectionProps) {
  const [editingPrompt, setEditingPrompt] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<string, any>>({});
  const [isAddingPrompt, setIsAddingPrompt] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [copiedPrompt, setCopiedPrompt] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const zero = useZero();

  // Create a mock object structure for CustomFieldManager
  const libraryData = { prompts };
  const standardFields = ['prompts'];

  const categories = ['all', ...new Set(prompts.map(p => p.category))];
  const filteredPrompts = selectedCategory === 'all' 
    ? prompts 
    : prompts.filter(p => p.category === selectedCategory);

  const handleEditStart = (promptId: string, prompt: Prompt) => {
    setEditingPrompt(promptId);
    setEditValues({ ...prompt });
  };

  const handleEditSave = (promptId: string) => {
    console.log(`Saving prompt ${promptId}:`, editValues);
    
    const updatedPrompts = prompts.map(p => 
      p.id === promptId ? editValues : p
    );
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        prompt_library: updatedPrompts
      }
    });
    
    setEditingPrompt(null);
    setEditValues({});
  };

  const handleEditCancel = () => {
    setEditingPrompt(null);
    setEditValues({});
  };

  const handleAddPrompt = () => {
    setIsAddingPrompt(true);
    setEditValues({
      id: `prompt-${Date.now()}`,
      name: '',
      category: '',
      prompt: ''
    });
  };

  const handleAddPromptSave = () => {
    console.log('Adding new prompt:', editValues);
    
    const updatedPrompts = [...prompts, editValues];
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        prompt_library: updatedPrompts
      }
    });
    
    setIsAddingPrompt(false);
    setEditValues({});
  };

  const handleAddPromptCancel = () => {
    setIsAddingPrompt(false);
    setEditValues({});
  };

  const handleDeletePrompt = (promptId: string) => {
    console.log('Deleting prompt:', promptId);
    const updatedPrompts = prompts.filter(p => p.id !== promptId);
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        prompt_library: updatedPrompts
      }
    });
  };

  // Custom field handlers for library-level fields
  const handleCustomFieldEditStart = (field: string, value: any) => {
    setEditingField(field);
    setEditValues({ ...editValues, [field]: value });
  };

  const handleCustomFieldEditSave = (field: string) => {
    console.log(`Saving library field ${field}:`, editValues[field]);
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        prompt_library: {
          ...libraryData,
          [field]: editValues[field]
        }
      }
    });
    setEditingField(null);
  };

  const handleCustomFieldEditCancel = () => {
    setEditingField(null);
    setEditValues({});
  };

  const handleCustomFieldValueChange = (field: string, value: any) => {
    setEditValues({ ...editValues, [field]: value });
  };

  const handleCustomFieldDelete = (field: string) => {
    console.log(`Deleting library field: ${field}`);
    const updatedLibrary = { ...libraryData };
    delete updatedLibrary[field];
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        prompt_library: updatedLibrary
      }
    });
  };

  const handleAddCustomField = (fieldName: string) => {
    console.log('Added library custom field:', fieldName);
    setEditingField(fieldName);
    setEditValues({ ...editValues, [fieldName]: '' });
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        prompt_library: {
          ...libraryData,
          [fieldName]: ''
        }
      }
    });
  };

  const handleCopyPrompt = async (promptId: string, promptText: string) => {
    try {
      await navigator.clipboard.writeText(promptText);
      setCopiedPrompt(promptId);
      setTimeout(() => setCopiedPrompt(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'Photography': 'bg-purple-100 text-purple-800',
      'Social Media Copy': 'bg-blue-100 text-blue-800',
      'Blog Content': 'bg-green-100 text-green-800',
      'Email Marketing': 'bg-orange-100 text-orange-800',
      'Product Description': 'bg-red-100 text-red-800',
      'Press Release': 'bg-yellow-100 text-yellow-800',
      'Video Script': 'bg-indigo-100 text-indigo-800',
      'Ad Copy': 'bg-pink-100 text-pink-800',
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const renderPromptCard = (prompt: Prompt) => {
    const isEditing = editingPrompt === prompt.id;
    const isCopied = copiedPrompt === prompt.id;

    return (
      <div key={prompt.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Prompt Name</label>
              <input
                type="text"
                value={editValues.name || ''}
                onChange={(e) => setEditValues({ ...editValues, name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter prompt name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <input
                type="text"
                value={editValues.category || ''}
                onChange={(e) => setEditValues({ ...editValues, category: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter category (e.g., Photography, Social Media Copy)"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Prompt</label>
              <textarea
                value={editValues.prompt || ''}
                onChange={(e) => setEditValues({ ...editValues, prompt: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={6}
                placeholder="Enter the complete prompt text"
              />
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave(prompt.id)}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div>
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-start gap-2">
                <BookOpen size={18} className="text-blue-600 mt-0.5" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{prompt.name}</h3>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(prompt.category)}`}>
                    {prompt.category}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => handleCopyPrompt(prompt.id, prompt.prompt)}
                  className={`p-1 rounded transition-colors ${
                    isCopied 
                      ? 'text-green-600 hover:text-green-700' 
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                  title="Copy prompt"
                >
                  <Copy size={14} />
                </button>
                <button
                  onClick={() => handleEditStart(prompt.id, prompt)}
                  className="text-gray-400 hover:text-gray-600 p-1"
                  title="Edit prompt"
                >
                  <Edit2 size={14} />
                </button>
                <button
                  onClick={() => handleDeletePrompt(prompt.id)}
                  className="text-gray-400 hover:text-red-600 p-1"
                  title="Delete prompt"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            </div>
            
            <div className="bg-white p-3 rounded-lg border border-gray-200">
              <p className="text-sm text-gray-700 whitespace-pre-wrap">{prompt.prompt}</p>
            </div>
            
            {isCopied && (
              <div className="mt-2 text-sm text-green-600 font-medium">
                Prompt copied to clipboard!
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderAddPromptForm = () => {
    return (
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Prompt Name</label>
            <input
              type="text"
              value={editValues.name || ''}
              onChange={(e) => setEditValues({ ...editValues, name: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter prompt name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <input
              type="text"
              value={editValues.category || ''}
              onChange={(e) => setEditValues({ ...editValues, category: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter category (e.g., Photography, Social Media Copy)"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Prompt</label>
            <textarea
              value={editValues.prompt || ''}
              onChange={(e) => setEditValues({ ...editValues, prompt: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={6}
              placeholder="Enter the complete prompt text"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleAddPromptSave}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              <Save size={14} />
              Add Prompt
            </button>
            <button
              onClick={handleAddPromptCancel}
              className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Prompt Library</h1>
        <p className="text-gray-600">Your collection of content generation prompts</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-900">Prompts</h2>
            
            {categories.length > 1 && (
              <div className="flex items-center gap-2">
                <Filter size={16} className="text-gray-500" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {categories.filter(c => c !== 'all').map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
          
          {!isAddingPrompt && (
            <button
              onClick={handleAddPrompt}
              className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              <Plus size={14} />
              Add Prompt
            </button>
          )}
        </div>

        <div className="space-y-4">
          {isAddingPrompt && renderAddPromptForm()}
          
          {filteredPrompts.map(renderPromptCard)}
          
          {!isAddingPrompt && filteredPrompts.length === 0 && prompts.length > 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No prompts found in the &quot;{selectedCategory}&quot; category.</p>
            </div>
          )}
          
          {!isAddingPrompt && prompts.length === 0 && (
            <div className="text-center py-12">
              <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No prompts yet</h3>
              <p className="text-gray-600 mb-4">Create your first content generation prompt to get started</p>
              <button
                onClick={handleAddPrompt}
                className="flex items-center gap-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto"
              >
                <Plus size={16} />
                Add Prompt
              </button>
            </div>
          )}
        </div>

        {/* Custom Fields Manager for library-level fields */}
        <CustomFieldManager
          data={libraryData}
          standardFields={standardFields}
          editingField={editingField}
          editValues={editValues}
          onEditStart={handleCustomFieldEditStart}
          onEditSave={handleCustomFieldEditSave}
          onEditCancel={handleCustomFieldEditCancel}
          onValueChange={handleCustomFieldValueChange}
          onDelete={handleCustomFieldDelete}
          onAddCustomField={handleAddCustomField}
        />
      </div>
    </div>
  );
}