'use client';

import React, { useState } from 'react';
import { Edit2, Save, X, Plus, Trash2, Upload, Camera } from 'lucide-react';
import { VisualIdentity, ColorCategory, Font, Logo, PhotographyGuidelines } from '../../types/brand';
import { useZero } from '~/hooks/use-zero';
import { EditableField } from '../shared/EditableField';
import { CustomFieldManager } from '../shared/CustomFieldManager';
import { ColorPaletteSection } from './ColorPaletteSection';
import { FontDialog } from './FontDialog';
import { LogoDialog } from './LogoDialog';
import { PhotographyDialog } from './PhotographyDialog';
import { uploadFontAction, deleteFontAction, uploadLogoAction, deleteLogoAction } from '../../_lib/server/server-actions';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useTransition } from 'react';
import { toast } from '@kit/ui/sonner';

interface VisualIdentitySectionProps {
  visualIdentity: VisualIdentity;
  brandId: string;
}

export function VisualIdentitySection({ visualIdentity, brandId }: VisualIdentitySectionProps) {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<string, any>>({});
  const [editingFont, setEditingFont] = useState<Font | null>(null);
  const [editingLogo, setEditingLogo] = useState<Logo | null>(null);
  const [editingPhotography, setEditingPhotography] = useState<PhotographyGuidelines | null>(null);
  const [uploadingFonts, setUploadingFonts] = useState<Set<string>>(new Set());
  const [uploadingLogos, setUploadingLogos] = useState<Set<string>>(new Set());
  const [isPending, startTransition] = useTransition();
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();

  const standardFields = ['color_palette', 'fonts', 'logos', 'photography'];

  const handleEditStart = (field: string, value: any) => {
    setEditingField(field);
    setEditValues({ ...editValues, [field]: value });
  };

  const handleEditSave = (field: string) => {
    console.log(`Saving ${field}:`, editValues[field]);
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        visual_identity: {
          ...visualIdentity,
          [field]: editValues[field]
        }
      }
    });
    setEditingField(null);
  };

  const handleEditCancel = () => {
    setEditingField(null);
    setEditValues({});
  };

  const handleValueChange = (field: string, value: any) => {
    setEditValues({ ...editValues, [field]: value });
  };

  const handleDeleteField = (field: string) => {
    console.log(`Deleting field: ${field}`);
    const updatedIdentity = { ...visualIdentity };
    delete updatedIdentity[field];
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        visual_identity: updatedIdentity
      }
    });
  };

  const handleAddCustomField = (fieldName: string) => {
    console.log('Added custom field:', fieldName);
    setEditingField(fieldName);
    setEditValues({ ...editValues, [fieldName]: '' });
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        visual_identity: {
          ...visualIdentity,
          [fieldName]: ''
        }
      }
    });
  };

  const createDefaultFont = (): Font => {
    return {
      id: `font-${Date.now()}`,
      name: 'New Font',
      family: 'Arial, sans-serif',
      storage_path: null,
      upload_url: undefined,
      font_family: undefined,
      font_format: undefined,
      roles: [],
      usage_notes: ''
    };
  };

  const handleAddFirstFont = () => {
    const newFont = createDefaultFont();
    setEditingFont(newFont);
  };

  const handleEditFont = (font: Font) => {
    setEditingFont(font);
  };

  const handleSaveFont = async (updatedFont: Font) => {
    const currentFonts = visualIdentity.fonts || [];
    const isNewFont = !currentFonts.some(f => f.id === updatedFont.id);

    let updatedFonts;
    if (isNewFont) {
      updatedFonts = [...currentFonts, updatedFont];
    } else {
      updatedFonts = currentFonts.map(f => f.id === updatedFont.id ? updatedFont : f);
    }

    try {
      await zero.mutate.company_brand.update({
        id: brandId,
        values: {
          visual_identity: {
            ...visualIdentity,
            fonts: updatedFonts
          }
        }
      });
      
      setEditingFont(null);
    } catch (error) {
      console.error('Error saving font:', error);
    }
  };

  const handleCancelFontEdit = () => {
    setEditingFont(null);
  };

  const handleDeleteFont = async (fontId: string) => {
    const currentFonts = visualIdentity.fonts || [];
    const fontToDelete = currentFonts.find(f => f.id === fontId);
    
    startTransition(async () => {
      try {
        // If font has a storage path, delete the file from storage
        if (fontToDelete?.storage_path) {
          const result = await deleteFontAction(workspace.account.id, fontToDelete.storage_path);
          if (!result.success) {
            toast.error(result.error || 'Failed to delete font file');
            return;
          }
        }
        
        // Remove font from the database
        const updatedFonts = currentFonts.filter(f => f.id !== fontId);

        await zero.mutate.company_brand.update({
          id: brandId,
          values: {
            visual_identity: {
              ...visualIdentity,
              fonts: updatedFonts
            }
          }
        });
        
        toast.success('Font deleted successfully!');
      } catch (error) {
        console.error('Error deleting font:', error);
        toast.error('Failed to delete font');
      }
    });
  };

  const handleFontUpload = async (fontId: string, file: File) => {
    setUploadingFonts(prev => new Set(prev).add(fontId));
    
    startTransition(async () => {
      try {
        const result = await uploadFontAction(workspace.account.id, file);
        
        if (result.success && result.data) {
          // Update the font with the uploaded file information
          const currentFonts = visualIdentity.fonts || [];
          const updatedFonts = currentFonts.map(f => 
            f.id === fontId 
              ? { 
                  ...f, 
                  storage_path: result.data.name,
                  upload_url: result.data.url,
                  font_family: result.data.fontFamily,
                  font_format: result.data.format
                }
              : f
          );

          await zero.mutate.company_brand.update({
            id: brandId,
            values: {
              visual_identity: {
                ...visualIdentity,
                fonts: updatedFonts
              }
            }
          });
          
          toast.success('Font uploaded successfully!');
        } else {
          toast.error(result.error || 'Failed to upload font');
        }
      } catch (error) {
        console.error('Error uploading font:', error);
        toast.error('Failed to upload font');
      } finally {
        setUploadingFonts(prev => {
          const newSet = new Set(prev);
          newSet.delete(fontId);
          return newSet;
        });
      }
    });
  };

  // Logo Management Functions
  const createDefaultLogo = (): Logo => {
    return {
      id: `logo-${Date.now()}`,
      type: 'Primary Logo',
      description: 'Main brand logo for general use',
      storage_path: null,
      upload_url: undefined,
      usage_guidelines: []
    };
  };

  const handleAddFirstLogo = () => {
    const newLogo = createDefaultLogo();
    setEditingLogo(newLogo);
  };

  const handleEditLogo = (logo: Logo) => {
    setEditingLogo(logo);
  };

  const handleSaveLogo = async (updatedLogo: Logo) => {
    const currentLogos = visualIdentity.logos || [];
    const isNewLogo = !currentLogos.some(l => l.id === updatedLogo.id);

    let updatedLogos;
    if (isNewLogo) {
      updatedLogos = [...currentLogos, updatedLogo];
    } else {
      updatedLogos = currentLogos.map(l => l.id === updatedLogo.id ? updatedLogo : l);
    }

    try {
      await zero.mutate.company_brand.update({
        id: brandId,
        values: {
          visual_identity: {
            ...visualIdentity,
            logos: updatedLogos
          }
        }
      });
      
      setEditingLogo(null);
    } catch (error) {
      console.error('Error saving logo:', error);
    }
  };

  const handleCancelLogoEdit = () => {
    setEditingLogo(null);
  };

  const handleDeleteLogo = async (logoId: string) => {
    const currentLogos = visualIdentity.logos || [];
    const logoToDelete = currentLogos.find(l => l.id === logoId);
    
    startTransition(async () => {
      try {
        // If logo has a storage path, delete the file from storage
        if (logoToDelete?.storage_path) {
          const result = await deleteLogoAction(workspace.account.id, logoToDelete.storage_path);
          if (!result.success) {
            toast.error(result.error || 'Failed to delete logo file');
            return;
          }
        }
        
        // Remove logo from the database
        const updatedLogos = currentLogos.filter(l => l.id !== logoId);

        await zero.mutate.company_brand.update({
          id: brandId,
          values: {
            visual_identity: {
              ...visualIdentity,
              logos: updatedLogos
            }
          }
        });
        
        toast.success('Logo deleted successfully!');
      } catch (error) {
        console.error('Error deleting logo:', error);
        toast.error('Failed to delete logo');
      }
    });
  };

  const handleLogoUpload = async (logoId: string, file: File) => {
    setUploadingLogos(prev => new Set(prev).add(logoId));
    
    startTransition(async () => {
      try {
        const result = await uploadLogoAction(workspace.account.id, file);
        
        if (result.success && result.data) {
          // Update the logo with the uploaded file information
          const currentLogos = visualIdentity.logos || [];
          const updatedLogos = currentLogos.map(l => 
            l.id === logoId 
              ? { 
                  ...l, 
                  storage_path: result.data.name,
                  upload_url: result.data.url
                }
              : l
          );

          await zero.mutate.company_brand.update({
            id: brandId,
            values: {
              visual_identity: {
                ...visualIdentity,
                logos: updatedLogos
              }
            }
          });
          
          toast.success('Logo uploaded successfully!');
        } else {
          toast.error(result.error || 'Failed to upload logo');
        }
      } catch (error) {
        console.error('Error uploading logo:', error);
        toast.error('Failed to upload logo');
      } finally {
        setUploadingLogos(prev => {
          const newSet = new Set(prev);
          newSet.delete(logoId);
          return newSet;
        });
      }
    });
  };

  // Photography Management Functions
  const createDefaultPhotography = (): PhotographyGuidelines => {
    return {
      style: 'Professional and authentic',
      artDirection: [],
      rules: {
        do: [],
        dont: []
      }
    };
  };

  const handleAddFirstPhotography = () => {
    const newPhotography = createDefaultPhotography();
    setEditingPhotography(newPhotography);
  };

  const handleEditPhotography = () => {
    if (visualIdentity.photography) {
      setEditingPhotography(visualIdentity.photography);
    } else {
      handleAddFirstPhotography();
    }
  };

  const handleSavePhotography = async (updatedPhotography: PhotographyGuidelines) => {
    try {
      await zero.mutate.company_brand.update({
        id: brandId,
        values: {
          visual_identity: {
            ...visualIdentity,
            photography: updatedPhotography
          }
        }
      });
      
      setEditingPhotography(null);
    } catch (error) {
      console.error('Error saving photography guidelines:', error);
    }
  };

  const handleCancelPhotographyEdit = () => {
    setEditingPhotography(null);
  };

  const handleDeletePhotography = async () => {
    try {
      const updatedIdentity = { ...visualIdentity };
      delete updatedIdentity.photography;
      
      await zero.mutate.company_brand.update({
        id: brandId,
        values: {
          visual_identity: updatedIdentity
        }
      });
    } catch (error) {
      console.error('Error deleting photography guidelines:', error);
    }
  };


  const renderFonts = () => {
    const fonts = visualIdentity.fonts || [];
    
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Typography</h3>
          {fonts.length > 0 && (
            <button
              onClick={handleAddFirstFont}
              className="flex items-center gap-2 px-3 py-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors text-sm"
            >
              <Plus size={16} />
              Add Font
            </button>
          )}
        </div>
        
        <div className="space-y-4">
          {fonts.map((font: Font) => (
            <div key={font.id} className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{font.name}</h4>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEditFont(font)}
                    className="text-gray-400 hover:text-gray-600 p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Edit2 size={14} />
                  </button>
                  {uploadingFonts.has(font.id) ? (
                    <div className="text-gray-600 text-sm flex items-center gap-1">
                      <div className="animate-spin w-3 h-3 border border-gray-400 border-t-transparent rounded-full"></div>
                      Uploading...
                    </div>
                  ) : (
                    <div className="relative">
                      <input
                        type="file"
                        accept=".woff,.woff2,.ttf,.otf"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleFontUpload(font.id, file);
                          }
                        }}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        disabled={isPending}
                      />
                      <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1 disabled:opacity-50" disabled={isPending}>
                        <Upload size={14} />
                        {font.storage_path ? 'Replace' : 'Upload'}
                      </button>
                    </div>
                  )}
                  <button
                    onClick={() => handleDeleteFont(font.id)}
                    className="text-gray-400 hover:text-red-600 p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              </div>
              
              <div className="text-sm text-gray-600 mb-2">
                <span className="font-medium">Family:</span> {font.family}
              </div>

              {/* Font Preview */}
              {font.storage_path && font.upload_url && (
                <div className="mb-3">
                  <style jsx>{`
                    @font-face {
                      font-family: '${font.font_family || font.name}';
                      src: url('${font.upload_url}') format('${font.font_format || 'woff2'}');
                      font-display: swap;
                    }
                  `}</style>
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <div className="text-sm text-gray-600 mb-1">Preview:</div>
                    <div 
                      className="text-lg"
                      style={{ 
                        fontFamily: `'${font.font_family || font.name}', ${font.family}`,
                        lineHeight: '1.2'
                      }}
                    >
                      The quick brown fox jumps over the lazy dog
                    </div>
                    <div 
                      className="text-sm text-gray-600 mt-1"
                      style={{ 
                        fontFamily: `'${font.font_family || font.name}', ${font.family}` 
                      }}
                    >
                      AaBbCc 123
                    </div>
                  </div>
                </div>
              )}
              
              {font.roles && font.roles.length > 0 && (
                <div className="mb-2">
                  <span className="text-sm font-medium text-gray-700">Roles:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {font.roles.map((role, index) => (
                      <span key={index} className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                        {role}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              {font.usage_notes && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Notes:</span> {font.usage_notes}
                </div>
              )}
            </div>
          ))}
          
          {fonts.length === 0 && (
            <div 
              className="p-8 bg-gray-50 rounded-lg text-center border-2 border-dashed border-gray-300 cursor-pointer hover:bg-gray-100 hover:border-gray-400 transition-colors"
              onClick={handleAddFirstFont}
            >
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <h4 className="text-lg font-medium text-gray-600 mb-2">No Typography Defined</h4>
              <p className="text-sm text-gray-500 mb-3">
                Click here to add your first brand font.
              </p>
              <div className="text-xs text-gray-400">
                💡 Tip: Start with your primary heading font
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderLogos = () => {
    const logos = visualIdentity.logos || [];
    
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Logos</h3>
          {logos.length > 0 && (
            <button
              onClick={handleAddFirstLogo}
              className="flex items-center gap-2 px-3 py-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors text-sm"
            >
              <Plus size={16} />
              Add Logo
            </button>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {logos.map((logo: Logo) => (
            <div key={logo.id} className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{logo.type}</h4>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEditLogo(logo)}
                    className="text-gray-400 hover:text-gray-600 p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Edit2 size={14} />
                  </button>
                  {uploadingLogos.has(logo.id) ? (
                    <div className="text-gray-600 text-sm flex items-center gap-1">
                      <div className="animate-spin w-3 h-3 border border-gray-400 border-t-transparent rounded-full"></div>
                      Uploading...
                    </div>
                  ) : (
                    <div className="relative">
                      <input
                        type="file"
                        accept=".png,.jpg,.jpeg,.svg,.pdf,.ai,.eps"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleLogoUpload(logo.id, file);
                          }
                        }}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        disabled={isPending}
                      />
                      <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1 disabled:opacity-50" disabled={isPending}>
                        <Upload size={14} />
                        {logo.storage_path ? 'Replace' : 'Upload'}
                      </button>
                    </div>
                  )}
                  <button
                    onClick={() => handleDeleteLogo(logo.id)}
                    className="text-gray-400 hover:text-red-600 p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              </div>
              
              {logo.storage_path && logo.upload_url ? (
                <div className="bg-white p-4 rounded-lg border border-gray-200 mb-3">
                  <div className="h-24 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                    <img 
                      src={logo.upload_url} 
                      alt={logo.type}
                      className="max-h-full max-w-full object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        if (target.nextElementSibling) {
                          (target.nextElementSibling as HTMLElement).textContent = 'Failed to load image';
                        }
                      }}
                    />
                    <span className="text-gray-500 text-sm hidden">Failed to load image</span>
                  </div>
                </div>
              ) : uploadingLogos.has(logo.id) ? (
                <div className="bg-white p-4 rounded-lg border border-gray-200 mb-3">
                  <div className="h-24 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                    <div className="flex flex-col items-center gap-2">
                      <div className="animate-spin w-6 h-6 border border-gray-400 border-t-transparent rounded-full"></div>
                      <span className="text-gray-500 text-sm">Uploading...</span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white p-4 rounded-lg border border-gray-200 mb-3">
                  <div className="h-24 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                    <span className="text-gray-400 text-sm">No logo uploaded</span>
                  </div>
                </div>
              )}
              
              <p className="text-sm text-gray-600 mb-2">{logo.description}</p>
              
              {logo.usage_guidelines && logo.usage_guidelines.length > 0 && (
                <div>
                  <span className="text-sm font-medium text-gray-700">Usage Guidelines:</span>
                  <ul className="text-sm text-gray-600 mt-1">
                    {logo.usage_guidelines.map((guideline, index) => (
                      <li key={index} className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>{guideline}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
          
          {logos.length === 0 && (
            <div 
              className="p-8 bg-gray-50 rounded-lg text-center col-span-full border-2 border-dashed border-gray-300 cursor-pointer hover:bg-gray-100 hover:border-gray-400 transition-colors"
              onClick={handleAddFirstLogo}
            >
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <h4 className="text-lg font-medium text-gray-600 mb-2">No Logos Defined</h4>
              <p className="text-sm text-gray-500 mb-3">
                Click here to add your first brand logo.
              </p>
              <div className="text-xs text-gray-400">
                💡 Tip: Start with your primary logo
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderPhotography = () => {
    const photography = visualIdentity.photography;
    
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Photography Guidelines</h3>
          {photography && (
            <div className="flex items-center gap-2">
              <button
                onClick={handleEditPhotography}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <Edit2 size={14} />
              </button>
              <button
                onClick={handleDeletePhotography}
                className="text-gray-400 hover:text-red-600 p-1"
              >
                <Trash2 size={14} />
              </button>
            </div>
          )}
        </div>
        
        {photography ? (
          <div className="space-y-4 hover:bg-gray-50 p-4 rounded-lg transition-colors cursor-pointer" onClick={handleEditPhotography}>
            <div className="p-4 bg-white rounded-lg border border-gray-200">
              <h4 className="font-medium text-gray-900 mb-2">Style</h4>
              <p className="text-gray-700">{photography.style}</p>
            </div>
            
            {photography.artDirection && photography.artDirection.length > 0 && (
              <div className="p-4 bg-white rounded-lg border border-gray-200">
                <h4 className="font-medium text-gray-900 mb-2">Art Direction</h4>
                <ul className="space-y-1">
                  {photography.artDirection.map((direction, index) => (
                    <li key={index} className="text-gray-700 flex items-start">
                      <span className="mr-2">•</span>
                      <span>{direction}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {photography.rules && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {photography.rules.do && photography.rules.do.length > 0 && (
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <h4 className="font-medium text-green-900 mb-2">Do&apos;s</h4>
                    <ul className="space-y-1">
                      {photography.rules.do.map((rule, index) => (
                        <li key={index} className="text-green-800 flex items-start text-sm">
                          <span className="mr-2">✓</span>
                          <span>{rule}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {photography.rules.dont && photography.rules.dont.length > 0 && (
                  <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                    <h4 className="font-medium text-red-900 mb-2">Don&apos;ts</h4>
                    <ul className="space-y-1">
                      {photography.rules.dont.map((rule, index) => (
                        <li key={index} className="text-red-800 flex items-start text-sm">
                          <span className="mr-2">✗</span>
                          <span>{rule}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <div 
            className="p-8 bg-gray-50 rounded-lg text-center border-2 border-dashed border-gray-300 cursor-pointer hover:bg-gray-100 hover:border-gray-400 transition-colors"
            onClick={handleAddFirstPhotography}
          >
            <Camera className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">No Photography Guidelines Defined</h4>
            <p className="text-sm text-gray-500 mb-3">
              Click here to add photography style guidelines.
            </p>
            <div className="text-xs text-gray-400">
              💡 Tip: Define your visual style and do&apos;s & don&apos;ts
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Visual Identity</h1>
        <p className="text-gray-600">Your brand&apos;s visual elements and style guidelines</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="mb-8">
          <ColorPaletteSection visualIdentity={visualIdentity} brandId={brandId} />
        </div>
        
        {renderFonts()}
        {renderLogos()}
        {renderPhotography()}

        {/* Custom Fields Manager */}
        <CustomFieldManager
          data={visualIdentity}
          standardFields={standardFields}
          editingField={editingField}
          editValues={editValues}
          onEditStart={handleEditStart}
          onEditSave={handleEditSave}
          onEditCancel={handleEditCancel}
          onValueChange={handleValueChange}
          onDelete={handleDeleteField}
          onAddCustomField={handleAddCustomField}
        />
      </div>
      
      {/* Font Dialog */}
      <FontDialog
        isOpen={!!editingFont}
        font={editingFont}
        onClose={handleCancelFontEdit}
        onSave={handleSaveFont}
      />
      
      {/* Logo Dialog */}
      <LogoDialog
        isOpen={!!editingLogo}
        logo={editingLogo}
        onClose={handleCancelLogoEdit}
        onSave={handleSaveLogo}
      />
      
      {/* Photography Dialog */}
      <PhotographyDialog
        isOpen={!!editingPhotography}
        photography={editingPhotography}
        onClose={handleCancelPhotographyEdit}
        onSave={handleSavePhotography}
      />
    </div>
  );
}