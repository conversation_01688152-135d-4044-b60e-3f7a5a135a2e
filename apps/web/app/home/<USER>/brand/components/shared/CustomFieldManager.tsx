'use client';

import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import { EditableField } from './EditableField';

interface CustomFieldManagerProps {
  data: Record<string, any>;
  standardFields: string[];
  editingField: string | null;
  editValues: Record<string, any>;
  onEditStart: (field: string, value: any) => void;
  onEditSave: (field: string) => void;
  onEditCancel: () => void;
  onValueChange: (field: string, value: any) => void;
  onDelete: (field: string) => void;
  onAddCustomField: (fieldName: string) => void;
}

export function CustomFieldManager({
  data,
  standardFields,
  editingField,
  editValues,
  onEditStart,
  onEditSave,
  onEditCancel,
  onValueChange,
  onDelete,
  onAddCustomField
}: CustomFieldManagerProps) {
  const [isAddingCustomField, setIsAddingCustomField] = useState(false);
  const [customFieldName, setCustomFieldName] = useState('');

  const getCustomFields = () => {
    return Object.keys(data).filter(key => {
      // Only include fields that are not standard fields and are simple values (string, number, boolean)
      if (standardFields.includes(key)) return false;
      
      const value = data[key];
      // Only include simple values that can be edited as text
      return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' || value === null || value === undefined;
    });
  };

  const isStandardField = (fieldName: string) => {
    return standardFields.includes(fieldName);
  };

  const handleAddCustomField = () => {
    if (customFieldName.trim() && !isStandardField(customFieldName)) {
      onAddCustomField(customFieldName);
      setIsAddingCustomField(false);
      setCustomFieldName('');
    }
  };

  const handleCustomFieldCancel = () => {
    setIsAddingCustomField(false);
    setCustomFieldName('');
  };

  const renderAddCustomFieldForm = () => {
    return (
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Custom Field Name</label>
            <input
              type="text"
              value={customFieldName}
              onChange={(e) => setCustomFieldName(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddCustomField();
                }
              }}
              placeholder="Enter field name (e.g., company_history, core_values)"
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleAddCustomField}
              disabled={!customFieldName.trim() || isStandardField(customFieldName)}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <Plus size={14} />
              Add Field
            </button>
            <button
              onClick={handleCustomFieldCancel}
              className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
          
          {customFieldName.trim() && isStandardField(customFieldName) && (
            <p className="text-sm text-red-600">This field name is already used. Please choose a different name.</p>
          )}
        </div>
      </div>
    );
  };

  const customFields = getCustomFields();

  return (
    <>
      {/* Render existing custom fields */}
      {customFields.map(field => (
        <EditableField
          key={field}
          field={field}
          label={field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          value={data[field]}
          canDelete={true}
          editingField={editingField}
          editValues={editValues}
          onEditStart={onEditStart}
          onEditSave={onEditSave}
          onEditCancel={onEditCancel}
          onValueChange={onValueChange}
          onDelete={onDelete}
        />
      ))}
      
      {/* Add Custom Field Form */}
      {isAddingCustomField && renderAddCustomFieldForm()}
      
      {/* Add Custom Field Button */}
      {!isAddingCustomField && !editingField && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <button
            onClick={() => setIsAddingCustomField(true)}
            className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
          >
            <Plus size={16} />
            Add Custom Field
          </button>
        </div>
      )}
    </>
  );
}