'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Edit2, Save, X, Trash2 } from 'lucide-react';

interface EditableFieldProps {
  field: string;
  label: string;
  value: any;
  helpText?: string;
  isTextarea?: boolean;
  canDelete?: boolean;
  editingField: string | null;
  editValues: Record<string, any>;
  onEditStart: (field: string, value: any) => void;
  onEditSave: (field: string) => void;
  onEditCancel: () => void;
  onDelete?: (field: string) => void;
  onValueChange: (field: string, value: any) => void;
}

export function EditableField({
  field,
  label,
  value,
  helpText,
  isTextarea = false,
  canDelete = false,
  editingField,
  editValues,
  onEditStart,
  onEditSave,
  onEditCancel,
  onDelete,
  onValueChange
}: EditableFieldProps) {
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const isEditing = editingField === field;

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-1">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        {!isEditing && (
          <div className="flex items-center gap-1">
            <button
              onClick={() => onEditStart(field, value)}
              className="text-gray-400 hover:text-gray-600 p-1"
            >
              <Edit2 size={14} />
            </button>
            {canDelete && onDelete && (
              <button
                onClick={() => onDelete(field)}
                className="text-gray-400 hover:text-red-600 p-1"
                title={`Delete ${label}`}
              >
                <Trash2 size={14} />
              </button>
            )}
          </div>
        )}
      </div>
      
      {helpText && (
        <p className="text-xs text-gray-500 mb-2">{helpText}</p>
      )}
      
      {isEditing ? (
        <div className="space-y-2">
          {isTextarea ? (
            <textarea
              ref={inputRef as React.RefObject<HTMLTextAreaElement>}
              value={editValues[field] || ''}
              onChange={(e) => onValueChange(field, e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={4}
            />
          ) : (
            <input
              ref={inputRef as React.RefObject<HTMLInputElement>}
              type="text"
              value={editValues[field] || ''}
              onChange={(e) => onValueChange(field, e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          )}
          <div className="flex gap-2">
            <button
              onClick={() => onEditSave(field)}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              <Save size={14} />
              Save
            </button>
            <button
              onClick={onEditCancel}
              className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div 
          className="p-3 bg-gray-50 rounded-lg min-h-[44px] flex items-center cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => onEditStart(field, value)}
        >
          <span className="text-gray-900">
            {value ? (
              typeof value === 'object' ? (
                Array.isArray(value) ? (
                  <div className="space-y-2">
                    {value.map((item, index) => (
                      <div key={index} className="flex items-start gap-3">
                        {typeof item === 'object' && item.name ? (
                          <>
                            <span className="font-medium text-gray-900 min-w-0 flex-shrink-0">
                              {item.name}:
                            </span>
                            <span className="text-gray-700">{item.description || item.value || 'No description'}</span>
                          </>
                        ) : (
                          <span className="text-gray-700">{typeof item === 'object' ? JSON.stringify(item) : item}</span>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-1">
                    {Object.entries(value).map(([key, val]) => (
                      <div key={key} className="flex items-start gap-3">
                        <span className="font-medium text-gray-900 min-w-0 flex-shrink-0">
                          {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                        </span>
                        <span className="text-gray-700">
                          {typeof val === 'object' ? JSON.stringify(val) : String(val)}
                        </span>
                      </div>
                    ))}
                  </div>
                )
              ) : (
                value
              )
            ) : (
              <span className="text-gray-400 italic">Not set</span>
            )}
          </span>
        </div>
      )}
    </div>
  );
}