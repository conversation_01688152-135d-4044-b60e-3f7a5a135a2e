import React from 'react';
import { ALargeSmall, Image, Info, Palette, Settings } from 'lucide-react';

const menuItems = [
  { label: 'Details', targetId: 'details-section', icon: <Info size={18} /> },
  { label: 'Colors', targetId: 'colors-section', icon: <Palette size={18} /> },
  {
    label: 'Fonts',
    targetId: 'fonts-section',
    icon: <ALargeSmall size={18} />,
  },
  { label: 'Logos', targetId: 'logos-section', icon: <Image size={18} /> },
  {
    label: 'Advanced',
    targetId: 'advanced-section',
    icon: <Settings size={18} />,
  },
];

export default function BrandSidebar() {
  // const { setIsDeleteDialogOpen, isNewBrand } = useBrand();
  
  const handleMenuClick = (targetId: string) => {
    const element = document.getElementById(targetId);
    if (!element) {
      return;
    }

    if (element.getAttribute('data-state') === 'closed') {
      // click on the trigger
      (element.querySelector('h3>button') as HTMLButtonElement).click();
    }
    element.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="brand-sidebar-wrapper">
      <div className="brand-sidebar">
        <ul className="m-0 list-none p-0 space-y-1">
          {menuItems.map((item) => (
            <li
              key={item.targetId}
              className="w-full cursor-pointer rounded-md px-2 py-2 text-sm hover:bg-gray-100"
            >
              <button
                onClick={() => handleMenuClick(item.targetId)}
                className="flex h-full w-full cursor-pointer items-center text-left"
              >
                <span className="mr-2">{item.icon}</span>
                {item.label}
              </button>
            </li>
          ))}
        </ul>
        
        {/* {!isNewBrand && (
          <div className="mt-10 pt-4 border-t border-gray-200">
            <button
              onClick={() => setIsDeleteDialogOpen(true)}
              className="flex items-center w-full px-2 py-2 text-sm text-red-600 rounded-md hover:bg-red-50"
            >
              <Trash2 size={18} className="mr-2" />
              Delete Brand
            </button>
            <p className="mt-2 text-xs text-gray-500 px-2">
              Start over by deleting all brand assets and information
            </p>
          </div>
        )} */}
      </div>
    </div>
  );
}
