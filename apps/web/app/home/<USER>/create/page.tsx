'use client';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
// import { withI18n } from '~/lib/i18n/with-i18n';
import { TeamAccountLayoutPageHeader } from '../_components/layout';
import { Dashboard } from '../_components/dashboard/components/dashboard';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';


function TeamAccountHomePage() {
  const workspace = useTeamAccountWorkspace();

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={workspace.account.id}
        title={<Trans i18nKey={'common:routes.create'} />}
        description={'Create'}
      />

      <PageBody>
        <Dashboard />
      </PageBody>
    </>
  );
}

export default (TeamAccountHomePage);
