'use server'
import { InitialCampaignResponse } from "~/types/Campaign";

export async function getContentProgress(user) {
    console.log('user', user)
    const response = 
    await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/get-campaign-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        campaign_id: user?.user_metadata?.initialCampaignId,
        user_id: user?.id,
        company_id: user?.user_metadata?.company_id,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch campaign status');
    }

    const data = await response.json();
    return data as InitialCampaignResponse;
}