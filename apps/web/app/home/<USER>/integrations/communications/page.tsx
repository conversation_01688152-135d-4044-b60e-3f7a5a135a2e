import { PageBody } from '@kit/ui/page';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { Trans } from '@kit/ui/trans';
import { TeamAccountLayoutPageHeader } from '../../_components/layout';
// local imports


export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('teams:integrations:communications:pageTitle');

  return {
    title,
  };
};

interface TeamAccountCommunicationsIntegrationsPageProps {
  params: Promise<{ account: string }>;
}

async function TeamAccountCommunicationsIntegrationsPage(props: TeamAccountCommunicationsIntegrationsPageProps) {
  const slug = (await props.params).account;

  return (
    <>
    <TeamAccountLayoutPageHeader
        account={slug}
        title={'Communication Integrations'}
        description={'Connect communication tools to streamline team collaboration'}
      />

      <PageBody>
        <div className={'flex max-w-2xl flex-1 flex-col space-y-6'}>
          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey="integrations:communications.comingSoon.title" defaults="Coming Soon" />
              </CardTitle>
              <CardDescription>
                <Trans 
                  i18nKey="integrations:communications.comingSoon.description" 
                  defaults="Communication integrations like Slack, Discord, and Microsoft Teams will be available soon." 
                />
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                <Trans 
                  i18nKey="integrations:communications.comingSoon.features" 
                  defaults="Features will include automated notifications, team updates, and seamless communication workflows." 
                />
              </p>
            </CardContent>
          </Card>
        </div>
      </PageBody>
    </>
  );
}

export default TeamAccountCommunicationsIntegrationsPage; 