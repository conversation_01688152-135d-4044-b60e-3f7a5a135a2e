import { PageBody } from '@kit/ui/page';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { Trans } from '@kit/ui/trans';
import { TeamAccountLayoutPageHeader } from '../../_components/layout';


export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('teams:integrations:productivity:pageTitle');

  return {
    title,
  };
};

interface TeamAccountProductivityIntegrationsPageProps {
  params: Promise<{ account: string }>;
}

async function TeamAccountProductivityIntegrationsPage(props: TeamAccountProductivityIntegrationsPageProps) {
  const slug = (await props.params).account;

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={slug}
        title={'Productivity Integrations'}
        description={'Connect productivity tools to enhance your workflow and scheduling'}
      />

      <PageBody>
        <div className={'flex max-w-2xl flex-1 flex-col space-y-6'}>
          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey="integrations:productivity.comingSoon.title" defaults="Coming Soon" />
              </CardTitle>
              <CardDescription>
                <Trans 
                  i18nKey="integrations:productivity.comingSoon.description" 
                  defaults="Productivity integrations like Google Calendar, Notion, and Trello will be available soon." 
                />
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                <Trans 
                  i18nKey="integrations:productivity.comingSoon.features" 
                  defaults="Features will include calendar synchronization, task management, and automated workflow scheduling." 
                />
              </p>
            </CardContent>
          </Card>
        </div>
      </PageBody>
    </>
  );
}

export default TeamAccountProductivityIntegrationsPage; 