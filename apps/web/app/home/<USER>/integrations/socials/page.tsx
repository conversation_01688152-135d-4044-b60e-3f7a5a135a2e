import { PageBody } from '@kit/ui/page';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { AddSocialProfileDialog } from '../_components/add-social-profile-dialog';
import { SocialProfilesList } from '../_components/social-profiles-list';
import { TeamAccountLayoutPageHeader } from '../../_components/layout';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('teams:integrations:socials:pageTitle');

  return {
    title,
  };
};

interface TeamAccountSocialsIntegrationsPageProps {
  params: Promise<{ account: string }>;
}

async function TeamAccountSocialsIntegrationsPage(props: TeamAccountSocialsIntegrationsPageProps) {
  const slug = (await props.params).account;

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={slug}
        title={'Social Media Integrations'}
        description={'Connect and manage your social media accounts with multiple profiles'}
      />

      <PageBody>
        <div className={'flex max-w-4xl flex-1 flex-col space-y-6'}>
          {/* Add New Profile Section */}
          <div className="flex flex-col space-y-4">
            <div>
              <h2 className="text-lg font-semibold">Social Profiles</h2>
              <p className="text-sm text-muted-foreground">
                Create multiple social profiles to organize different sets of social accounts (e.g., Personal, Company, Brand).
              </p>
            </div>
            <div className="max-w-md">
              <AddSocialProfileDialog />
            </div>
          </div>

          {/* Existing Profiles List */}
          <SocialProfilesList />
        </div>
      </PageBody>
    </>
  );
}

export default TeamAccountSocialsIntegrationsPage; 