'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { getSocialProfiles, getProfilesDetails, disconnectSocialProfile } from '../_lib/server/server-actions';

interface ProfileDetails {
  activeSocialAccounts: string[];
  displayNames: Array<{
    created: string;
    displayName: string;
    id: string;
    platform: string;
    profileUrl: string;
    userImage: string;
    username: string;
    headline?: string;
    subscriptionType?: string;
    verifiedType?: string;
    refreshDaysRemaining?: number;
    refreshRequired?: string;
    type?: string;
  }>;
  email: string | null;
  monthlyApiCalls: number;
  monthlyPostCount: number;
  refId: string;
  title: string;
  lastUpdated: string;
  nextUpdate: string;
}

const platformLogos = {
  linkedin: '/images/LI-Logo.png',
  twitter: '/images/logo-black.png', // Using the existing X/Twitter logo
  facebook: '/images/logo-black.png', // Fallback to default logo for now
  instagram: '/images/logo-black.png', // Fallback to default logo for now
  // Add more platforms as needed
};

const platformNames = {
  linkedin: 'LinkedIn',
  twitter: 'Twitter/X',
  facebook: 'Facebook',
  instagram: 'Instagram',
};

export function SocialsIntegrationCard() {
  const workspace = useTeamAccountWorkspace();
  const [profileDetails, setProfileDetails] = useState<ProfileDetails | null>(null);
  const [profile, setProfile] = useState<any>();
  const [loading, setLoading] = useState(true);
  const [disconnecting, setDisconnecting] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfileDetails = async () => {
      try {
        const profiles = await getSocialProfiles(workspace.user.id, workspace.account.id);
        console.log("profiles", profiles);
        if (!(profiles.length > 0)) {
          setLoading(false);
          return;
        }
        console.log("ENTER", profiles[0].profileKey);
        setProfile(profiles[0]);
        const profileDetails = await getProfilesDetails(profiles[0].profileKey);
        console.log("profileDetails", profileDetails);
        setProfileDetails(profileDetails);
      } catch (error) {
        console.error('Error fetching profile details:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchProfileDetails();
  }, [workspace.user.id, workspace.account.id]);

  const handleDisconnect = async (platform: string) => {
    setDisconnecting(profile.id);
    try {
      console.log(`Disconnecting ${platform} profile ${profile.title}`);
      
      if (!profileDetails?.refId) {
        throw new Error('Profile key not found');
      }
      
      // Call the disconnect API with platform and profileKey
      await disconnectSocialProfile(platform, profile.profileKey);
      
      // Refresh the profile details after successful disconnect
      const profiles = await getSocialProfiles(workspace.user.id, workspace.account.id);
      if (profiles.length > 0) {
        const updatedProfileDetails = await getProfilesDetails(profiles[0].profileKey);
        setProfileDetails(updatedProfileDetails);
      } else {
        setProfileDetails(null);
      }
    } catch (error) {
      console.error(`Error disconnecting ${platform}:`, error);
      // You might want to show a toast notification here for user feedback
    } finally {
      setDisconnecting(null);
    }
  };

  const renderProfileCard = (profile: ProfileDetails['displayNames'][0]) => {
    const logoSrc = platformLogos[profile.platform as keyof typeof platformLogos] || '/images/default-social-logo.png';
    const platformName = platformNames[profile.platform as keyof typeof platformNames] || profile.platform;

    return (
      <Card key={profile.id} className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Platform Logo */}
              <div className="flex-shrink-0">
                <img 
                  src={logoSrc}
                  alt={`${platformName} Logo`} 
                  width={profile.platform === 'linkedin' ? 60 : 32}
                  height={profile.platform === 'linkedin' ? 60 : 32}
                  className="object-contain" 
                />
              </div>
              
              {/* Profile Image */}
              {profile.userImage ? (
                <div className="h-12 w-12 rounded-full overflow-hidden relative flex-shrink-0">
                  <img 
                    src={profile.userImage} 
                    alt={`${profile.displayName}'s profile`} 
                    className="object-cover w-full h-full" 
                  />
                </div>
              ) : (
                <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-sm flex-shrink-0">
                  {profile.displayName?.[0] || '?'}
                </div>
              )}
              
              {/* Profile Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h3 className="font-medium text-lg truncate">{profile.displayName}</h3>
                  <Badge variant="secondary" className="text-green-600 bg-green-50 text-xs">
                    Connected
                  </Badge>
                  {profile.verifiedType && profile.verifiedType !== 'none' && (
                    <Badge variant="outline" className="text-xs">
                      {profile.verifiedType} verified
                    </Badge>
                  )}
                </div>
                {profile.username && (
                  <p className="text-sm text-muted-foreground">@{profile.username}</p>
                )}
                {profile.headline && (
                  <p className="text-sm text-muted-foreground mt-1 truncate">{profile.headline}</p>
                )}
                
                {/* Connection Details */}
                <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                  <span>Connected: {new Date(profile.created).toLocaleDateString()}</span>
                  {profile.refreshDaysRemaining && (
                    <span>Refresh in: {profile.refreshDaysRemaining} days</span>
                  )}
                </div>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center space-x-2 flex-shrink-0 ml-4">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => window.open(profile.profileUrl, '_blank')}
              >
                View Profile
              </Button>
              <Button 
                variant="outline" 
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={() => handleDisconnect(profile.platform)}
                disabled={disconnecting === profile.id}
                size="sm"
              >
                {disconnecting === profile.id ? 'Disconnecting...' : 'Disconnect'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!profileDetails || !profileDetails.displayNames || profileDetails.displayNames.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Connected Social Accounts</h2>
        <Badge variant="outline">
          {profileDetails.displayNames.length} connected
        </Badge>
      </div>
      
      {/* Connected Profiles */}
      <div className="space-y-4">
        {profileDetails.displayNames.map(renderProfileCard)}
      </div>
    </div>
  );
} 