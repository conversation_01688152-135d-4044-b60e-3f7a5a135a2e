'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { cn } from '@kit/ui/utils';

interface IntegrationCategoryCardProps {
  title: string;
  description: string;
  href: string;
  icons: Array<{
    src: string;
    alt: string;
    darkSrc?: string;
  }>;
  className?: string;
}

export function IntegrationCategoryCard({
  title,
  description,
  href,
  icons,
  className
}: IntegrationCategoryCardProps) {
  return (
    <Card className={cn("hover:shadow-md transition-shadow h-full", className)}>
      <CardHeader>
        <CardTitle className="text-lg text-left">
          <Trans i18nKey={`integrations:categories.${title.toLowerCase()}.title`} defaults={title} />
        </CardTitle>
        
        {icons.length > 0 && (
          <div className="flex items-center space-x-4 my-6">
            {icons.map((icon, index) => (
              <div key={index} className="relative w-16 h-16">
                <Image
                  src={icon.src}
                  alt={icon.alt}
                  width={64}
                  height={64}
                  className="object-contain dark:hidden"
                />
                {icon.darkSrc && (
                  <Image
                    src={icon.darkSrc}
                    alt={icon.alt}
                    width={64}
                    height={64}
                    className="object-contain hidden dark:block"
                  />
                )}
              </div>
            ))}
          </div>
        )}
        
        <CardDescription className="text-left">
          <Trans 
            i18nKey={`integrations:categories.${title.toLowerCase()}.description`} 
            defaults={description} 
          />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button asChild variant="outline" className="w-full hover:bg-primary hover:text-primary-foreground transition-colors">
          <Link href={href} className="flex items-center justify-center">
            <Trans i18nKey="integrations:configure" defaults="Configure" />
            <ChevronRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
} 