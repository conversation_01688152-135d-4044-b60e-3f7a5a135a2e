'use client';

import { useEffect, useState } from 'react';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';
import { toast } from '@kit/ui/sonner';
import { createProfile, generateJWT, getSocialProfiles } from '../_lib/server/server-actions';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

export function ConnectToAyrshare() {
  const [isConnecting, setIsConnecting] = useState(false);
  const [profiles, setProfiles] = useState<any>([]);
  const workspace = useTeamAccountWorkspace();

  useEffect(() => {
    const fetchProfiles = async () => {
        const profiles = await getSocialProfiles(workspace.user.id, workspace.account.id);
        if(profiles.status == 'false') return;
        setProfiles(profiles || []);
    }
    fetchProfiles();
  }, [])

  const handleConnect = async () => {
    setIsConnecting(true);

    try {
      // TODO: Implement Ayrshare connection logic
      if(profiles && profiles.length > 0) {
        const profile = profiles[0];

        const jwtData = await generateJWT(profile.profileKey);

        if(jwtData.status == 'success') {
            await window.open(jwtData.url, '_blank', );
        }
      } else {
        const data = await createProfile(workspace.user.id, workspace.account.id);

        if(data && data.status == 'success') {
          const jwtData = await generateJWT(data.profileKey);

          if(jwtData.status == 'success') {
              await window.open(jwtData.url, '_blank', );
          }
        } else {
          toast.error('Failed to connect. Please try again.');
        }
      }
      toast.success('Connected to successfully!');
      window.location.reload();
    } catch (error) {
      console.error('Error connecting to Ayrshare:', error);
      toast.error('Failed to connect. Please try again.');
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>
          <Trans i18nKey="integrations:ayrshare.title" defaults="Connect Socials" />
        </CardTitle>
        <CardDescription>
          <Trans 
            i18nKey="integrations:ayrshare.description" 
            defaults="Connect multiple social media accounts and publish right from the content studio."
          />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button 
          onClick={handleConnect}
          disabled={isConnecting}
          className="w-full"
          size="lg"
        >
          {isConnecting ? (
            <Trans i18nKey="integrations:ayrshare.connecting" defaults="Connecting..." />
          ) : (
            <Trans i18nKey="integrations:ayrshare.connect" defaults="Choose Accounts" />
          )}
        </Button>
      </CardContent>
    </Card>
  );
}  
