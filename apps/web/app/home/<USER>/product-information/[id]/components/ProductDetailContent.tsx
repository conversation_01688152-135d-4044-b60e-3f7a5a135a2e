'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Edit2, Save, X, Plus, Trash2, Package, FileText, Check, ChevronsUpDown, MoreVertical } from 'lucide-react';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Separator } from '@kit/ui/separator';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@kit/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Badge } from '@kit/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { toast } from '@kit/ui/sonner';

import { useQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import type { Product } from '~/types/product';
import { ProductDocumentsTable } from '../../components/ProductDocumentsTable';

interface ProductDetailContentProps {
  productId: string;
}

export function ProductDetailContent({ productId }: ProductDetailContentProps) {
  const { account } = useTeamAccountWorkspace();
  const zero = useZero();
  const [isEditing, setIsEditing] = useState(false);
  const [editValues, setEditValues] = useState<Partial<Product>>({});
  const [keyFeatures, setKeyFeatures] = useState<Array<{name: string; value_prop: string; differentiation: string}>>([]);
  const [icpPopoverOpen, setIcpPopoverOpen] = useState(false);

  const [products] = useQuery(zero.query.products.where('id', productId), {
    ttl: "10m"
  });

  const [productDocuments] = useQuery(
    zero.query.product_documents.where('product_id', productId), 
    { ttl: "10m" }
  );

  // Fetch ICPs to display names and for editing
  const [icps] = useQuery(zero.query.icps.where('company_id', account?.id), {
    ttl: "10m"
  });

  const product = products?.[0];

  React.useEffect(() => {
    if (product && !isEditing) {
      setEditValues(product);
      setKeyFeatures(product.key_features || [{name: '', value_prop: '', differentiation: ''}]);
    }
  }, [product, isEditing]);

  if (!product) {
    return (
      <div className="container mx-auto p-8">
        <div className="text-center">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">Product not found</h3>
          <p className="mt-1 text-sm text-gray-500">
            The product you are looking for does not exist.
          </p>
          <div className="mt-6">
            <Link href={`/home/<USER>/product-information`}>
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const handleEditStart = () => {
    setIsEditing(true);
    setEditValues(product);
    setKeyFeatures(product.key_features || ['']);
  };

  const handleEditSave = async () => {
    try {
      const filteredFeatures = keyFeatures.filter(feature => feature.trim() !== '');
      
      await zero.mutate.products.update({
        id: productId,
        values: {
          ...editValues,
          key_features: filteredFeatures,
          updated_at: Date.now(),
        }
      });
      
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating product:', error);
    }
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditValues(product);
    setKeyFeatures(product.key_features || [{name: '', value_prop: '', differentiation: ''}]);
  };

  const addKeyFeature = () => {
    setKeyFeatures([...keyFeatures, {name: '', value_prop: '', differentiation: ''}]);
  };

  const removeKeyFeature = (index: number) => {
    const newFeatures = keyFeatures.filter((_, i) => i !== index);
    setKeyFeatures(newFeatures);
  };

  const updateKeyFeature = (index: number, field: 'name' | 'value_prop' | 'differentiation', value: string) => {
    const newFeatures = [...keyFeatures];
    newFeatures[index] = { ...newFeatures[index], [field]: value };
    setKeyFeatures(newFeatures);
  };

  const handleDeleteProduct = async () => {
    if (!window.confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      return;
    }

    try {
      await zero.mutate.products.delete({ id: productId });
      toast.success('Product deleted successfully');
      // Navigate back to products list
      window.location.href = `/home/<USER>/product-information`;
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product. Please try again.');
    }
  };

  return (
    <div className="container mx-auto p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Link href={`/home/<USER>/product-information`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
        </div>
        
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Package className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold">{product.name}</h1>
              <p className="text-muted-foreground">Product Details</p>
            </div>
          </div>
          
          {!isEditing ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEditStart}>
                  <Edit2 className="h-4 w-4 mr-2" />
                  Edit Product
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={handleDeleteProduct}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Product
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex gap-2">
              <Button onClick={handleEditSave}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
              <Button onClick={handleEditCancel} variant="outline">
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          )}
        </div>
      </div>

      <Tabs defaultValue="details" className="space-y-6">
        <TabsList>
          <TabsTrigger value="details">Product Details</TabsTrigger>
          <TabsTrigger value="documents">
            Documentation ({productDocuments?.length || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Product Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {isEditing ? (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Product Name</label>
                    <Input
                      value={editValues.name || ''}
                      onChange={(e) => setEditValues({ ...editValues, name: e.target.value })}
                      placeholder="Enter product name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <Textarea
                      value={editValues.description || ''}
                      onChange={(e) => setEditValues({ ...editValues, description: e.target.value })}
                      placeholder="Describe your product"
                      rows={4}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Target Audience (ICPs)</label>
                    <Popover open={icpPopoverOpen} onOpenChange={setIcpPopoverOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={icpPopoverOpen}
                          className="w-full justify-between"
                        >
                          {editValues.target_audience && Array.isArray(editValues.target_audience) && editValues.target_audience.length > 0
                            ? `${editValues.target_audience.length} ICP${editValues.target_audience.length > 1 ? 's' : ''} selected`
                            : "Select target ICPs..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search ICPs..." />
                          <CommandEmpty>
                            {icps?.length === 0 ? (
                              <div className="p-4 text-center text-sm text-muted-foreground">
                                No ICPs found. Create ICPs first to assign them to products.
                              </div>
                            ) : (
                              "No ICPs found."
                            )}
                          </CommandEmpty>
                          <CommandGroup>
                            {icps?.map((icp) => (
                              <CommandItem
                                key={icp.id}
                                value={icp.id}
                                onSelect={(currentValue) => {
                                  const currentTargetAudience = Array.isArray(editValues.target_audience) ? editValues.target_audience : [];
                                  const updatedValue = currentTargetAudience.includes(currentValue)
                                    ? currentTargetAudience.filter((value) => value !== currentValue)
                                    : [...currentTargetAudience, currentValue];
                                  setEditValues({ ...editValues, target_audience: updatedValue });
                                }}
                              >
                                <Check
                                  className={`mr-2 h-4 w-4 ${
                                    Array.isArray(editValues.target_audience) && editValues.target_audience.includes(icp.id) ? "opacity-100" : "opacity-0"
                                  }`}
                                />
                                {icp.name || `ICP ${icp.id.slice(0, 8)}`}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    
                    {/* Display selected ICPs as badges */}
                    {editValues.target_audience && Array.isArray(editValues.target_audience) && editValues.target_audience.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {editValues.target_audience.map((icpId) => {
                          const icp = icps?.find(i => i.id === icpId);
                          return (
                            <Badge key={icpId} variant="secondary" className="flex items-center gap-1">
                              {icp?.name || `ICP ${icpId.slice(0, 8)}`}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => {
                                  const currentTargetAudience = Array.isArray(editValues.target_audience) ? editValues.target_audience : [];
                                  const newValue = currentTargetAudience.filter(id => id !== icpId);
                                  setEditValues({ ...editValues, target_audience: newValue });
                                }}
                              />
                            </Badge>
                          );
                        })}
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Key Features</label>
                    <p className="text-xs text-gray-500 mb-2">Your main product features with their value propositions and differentiators</p>
                    
                    <div className="space-y-4">
                      {keyFeatures.map((feature, index) => (
                        <div key={index} className="p-3 border border-gray-200 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <Input
                              value={feature.name}
                              onChange={(e) => updateKeyFeature(index, 'name', e.target.value)}
                              placeholder="Feature name"
                              className="flex-1 font-medium"
                            />
                            {keyFeatures.length > 1 && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeKeyFeature(index)}
                                className="ml-2"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                          <div className="space-y-2">
                            <Textarea
                              value={feature.value_prop}
                              onChange={(e) => updateKeyFeature(index, 'value_prop', e.target.value)}
                              placeholder="Value proposition - What benefit does this feature provide?"
                              className="w-full"
                              rows={2}
                            />
                            <Textarea
                              value={feature.differentiation}
                              onChange={(e) => updateKeyFeature(index, 'differentiation', e.target.value)}
                              placeholder="Key differentiation - How does this feature set you apart from competitors?"
                              className="w-full"
                              rows={2}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addKeyFeature}
                      className="mt-2"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Feature
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  {product.description && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
                      <p className="text-gray-900">{product.description}</p>
                    </div>
                  )}

                  {product.target_audience && Array.isArray(product.target_audience) && product.target_audience.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Target ICPs</h3>
                      <div className="flex flex-wrap gap-2">
                        {product.target_audience.map((icpId) => {
                          const icp = icps?.find(i => i.id === icpId);
                          return (
                            <Badge key={icpId} variant="secondary">
                              {icp?.name || `ICP ${icpId.slice(0, 8)}`}
                            </Badge>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {product.key_features && product.key_features.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Key Features</h3>
                      <div className="space-y-4">
                        {product.key_features.map((feature, index) => (
                          <div key={index} className="p-3 bg-gray-50 rounded-lg border">
                            <div className="flex items-start">
                              <span className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-blue-600 flex-shrink-0" />
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900 mb-2">{feature.name}</h4>
                                {feature.value_prop && (
                                  <div className="mb-2">
                                    <span className="text-xs font-medium text-gray-600">Value Proposition:</span>
                                    <p className="text-sm text-gray-700 mt-1">{feature.value_prop}</p>
                                  </div>
                                )}
                                {feature.differentiation && (
                                  <div>
                                    <span className="text-xs font-medium text-gray-600">Key Differentiation:</span>
                                    <p className="text-sm text-gray-700 mt-1">{feature.differentiation}</p>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Product Documentation
                </CardTitle>
                <Link href={`/home/<USER>/product-information/${productId}/documents/new`}>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Document
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {productDocuments && productDocuments.length > 0 ? (
                <ProductDocumentsTable
                  data={productDocuments as any}
                  page={1}
                  pageCount={1}
                  pageSize={20}
                />
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-semibold text-gray-900">No documents yet</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Add documentation, guides, or other materials for this product.
                  </p>
                  <div className="mt-6">
                    <Link href={`/home/<USER>/product-information/${productId}/documents/new`}>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add First Document
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}