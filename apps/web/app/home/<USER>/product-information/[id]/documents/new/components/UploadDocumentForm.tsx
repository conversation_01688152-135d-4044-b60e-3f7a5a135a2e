'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation } from '@tanstack/react-query';
import { Cloud, Link, ArrowLeft } from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';

import { uploadProductDocument } from '~/services/product-document';
import { getLocalApi } from '~/utils/api.util';
import { getFormattedUrl } from '~/utils/url.util';
import { upload } from '@vercel/blob/client';
import UrlDialog from '~/home/<USER>/product-information/new/components/UrlDialog';

interface UploadFormInputs {
  title: string;
  document: File | null;
}

interface UploadDocumentFormProps {
  productId: string;
}

export function UploadDocumentForm({ productId }: UploadDocumentFormProps) {
  const { account } = useTeamAccountWorkspace();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [files, setFiles] = useState<File[]>([]);
  const [isOpen, onOpenChange] = useState(false);

  const { handleSubmit, setValue, watch } = useForm<UploadFormInputs>({
    defaultValues: {
      title: '',
      document: null,
    },
  });

  const title = watch('title');

  const { getRootProps, getInputProps, isDragAccept, isDragReject } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    maxFiles: 1,
    onDropAccepted: (acceptedFiles) => {
      if (acceptedFiles.length < 0) {
        console.log('No files selected');
        return;
      }

      setFiles(acceptedFiles);
      setValue('document', acceptedFiles[0] as File);

      const fileName = (acceptedFiles[0] as File).name;
      const fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
      setValue('title', fileNameWithoutExt);
    },
  });

  const uploadMutation = useMutation({
    mutationFn: async (data: { title: string; file: File }) => {
      try {
        const blob = await upload(data.file.name, data.file, {
          access: 'public',
          handleUploadUrl: '/api/product-documents/upload',
        });

        // Upload product document with product_id
        return uploadProductDocument(data.title, null, account?.id, undefined, blob.url, productId);
      } catch (error) {
        console.error('Error uploading to blob:', error);
        throw new Error('Failed to upload document');
      }
    },
    onSuccess: () => {
      toast.success('Document uploaded successfully');
      router.push(`/home/<USER>/product-information/${productId}`);
      router.refresh();
    },
    onError: (error: any) => {
      console.error('Upload error:', error);
      toast.error(error.message || 'Failed to upload document');
      setError(error.message);
    },
  });

  const urlMutation = useMutation({
    mutationFn: async (url: string) => {
      url = getFormattedUrl(url);
      const response = await getLocalApi().post('/scrape-website', { url });

      if (!response.data.text) {
        throw new Error('Failed to scrape website');
      }
      
      const content = response.data.text as string;
      return uploadProductDocument(url, null, account?.id, content, undefined, productId);
    },
    onSuccess: () => {
      toast.success('Content extracted and uploaded successfully');
      router.push(`/home/<USER>/product-information/${productId}`);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to extract content from URL');
      setError(error.message);
    },
  });

  const onSubmit = async (data: UploadFormInputs) => {
    if (data.document) {
      uploadMutation.mutate({
        title: data.title,
        file: data.document,
      });
    }
  };

  const handleUrlSubmit = async (url: string) => {
    await urlMutation.mutateAsync(url);
  };

  return (
    <>
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => router.push(`/home/<USER>/product-information/${productId}`)}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Product
        </Button>
      </div>

      <Card className="mx-auto max-w-2xl">
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="mb-4 flex justify-end">
              <Button
                variant="default"
                onClick={() => onOpenChange(true)}
                type="button"
              >
                <Link className="mr-2 h-4 w-4" />
                Add URL
              </Button>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Document Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setValue('title', e.target.value)}
                placeholder="Enter document title"
                required
              />
            </div>

            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragAccept ? 'border-green-400 bg-green-50' : 
                isDragReject ? 'border-red-400 bg-red-50' : 
                'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Cloud className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-lg font-medium">
                {files.length > 0 ? 'File Selected!' : 'Drop files here or click to browse'}
              </p>
              <p className="text-sm text-gray-500">
                Supports PDF and DOCX files
              </p>
              {files.length > 0 && (
                <p className="mt-2 text-sm font-medium text-blue-600">
                  {files[0].name}
                </p>
              )}
            </div>

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={!title || !files.length || uploadMutation.isPending}
                className="flex-1"
              >
                {uploadMutation.isPending ? 'Uploading...' : 'Upload Document'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/home/<USER>/product-information/${productId}`)}
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <UrlDialog
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        onSubmit={handleUrlSubmit}
        isLoading={urlMutation.isPending}
      />
    </>
  );
}