import { UploadDocumentForm } from './components/UploadDocumentForm';

interface UploadDocumentPageProps {
  params: {
    account: string;
    id: string;
  };
}

export default function UploadDocumentPage({ params }: UploadDocumentPageProps) {
  return (
    <div className="container mx-auto p-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Upload Product Document</h1>
        <p className="text-muted-foreground">
          Add documentation, guides, or other materials for this product
        </p>
      </div>
      <UploadDocumentForm productId={params.id} />
    </div>
  );
}