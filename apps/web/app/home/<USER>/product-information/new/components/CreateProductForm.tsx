'use client';

import React, { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, X, Check, ChevronsUpDown } from 'lucide-react';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@kit/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';

import { useZero } from '~/hooks/use-zero';
import { useQuery } from '@rocicorp/zero/react';

const CreateProductSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  target_audience: z.array(z.string()).optional(),
  key_features: z.array(z.object({
    name: z.string(),
    value_prop: z.string(),
    differentiation: z.string(),
  })).optional(),
});

type CreateProductFormData = z.infer<typeof CreateProductSchema>;

export function CreateProductForm() {
  const router = useRouter();
  const { account } = useTeamAccountWorkspace();
  const zero = useZero();
  const [isPending, startTransition] = useTransition();
  const [keyFeatures, setKeyFeatures] = useState<Array<{name: string; value_prop: string; differentiation: string}>>([{name: '', value_prop: '', differentiation: ''}]);
  const [icpPopoverOpen, setIcpPopoverOpen] = useState(false);

  // Fetch ICPs for the company
  const [icps] = useQuery(zero.query.icps.where('company_id', account?.id), {
    ttl: "10m"
  });

  const form = useForm<CreateProductFormData>({
    resolver: zodResolver(CreateProductSchema),
    defaultValues: {
      name: '',
      description: '',
      target_audience: [],
      key_features: [{name: '', value_prop: '', differentiation: ''}],
    },
  });

  const onSubmit = (data: CreateProductFormData) => {
    if (!account?.id) return;

    startTransition(async () => {
            try {
        const filteredFeatures = keyFeatures.filter(feature => 
          feature.name.trim() !== '' || feature.value_prop.trim() !== '' || feature.differentiation.trim() !== ''
        );
        
        const productId = crypto.randomUUID();
         
        await zero.mutate.products.insert({
          id: productId,
          name: data.name,
          description: data.description,
          target_audience: data.target_audience,
          key_features: filteredFeatures,
          company_id: account.id,
          custom_fields: {},
        });

        toast.success('Product created successfully');
        router.push(`/home/<USER>/product-information/${productId}`);
      } catch (error) {
        console.error('Error creating product:', error);
        toast.error('Failed to create product. Please try again.');
      }
    });
  };

  const addKeyFeature = () => {
    setKeyFeatures([...keyFeatures, {name: '', value_prop: '', differentiation: ''}]);
  };

  const removeKeyFeature = (index: number) => {
    const newFeatures = keyFeatures.filter((_, i) => i !== index);
    setKeyFeatures(newFeatures);
  };

  const updateKeyFeature = (index: number, field: 'name' | 'value_prop' | 'differentiation', value: string) => {
    const newFeatures = [...keyFeatures];
    newFeatures[index] = { ...newFeatures[index], [field]: value };
    setKeyFeatures(newFeatures);
  };

  return (
    <Card className="max-w-2xl">
      <CardHeader>
        <CardTitle>Product Information</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product Name *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter product name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your product"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="target_audience"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target Audience (ICPs)</FormLabel>
                  <FormControl>
                    <Popover open={icpPopoverOpen} onOpenChange={setIcpPopoverOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={icpPopoverOpen}
                          className="w-full justify-between"
                        >
                          {field.value && field.value.length > 0
                            ? `${field.value.length} ICP${field.value.length > 1 ? 's' : ''} selected`
                            : "Select target ICPs..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search ICPs..." />
                          <CommandEmpty>
                            {icps?.length === 0 ? (
                              <div className="p-4 text-center text-sm text-muted-foreground">
                                No ICPs found. Create ICPs first to assign them to products.
                              </div>
                            ) : (
                              "No ICPs found."
                            )}
                          </CommandEmpty>
                          <CommandGroup>
                            {icps?.map((icp) => (
                              <CommandItem
                                key={icp.id}
                                value={icp.id}
                                onSelect={(currentValue) => {
                                  const newValue = field.value || [];
                                  const updatedValue = newValue.includes(currentValue)
                                    ? newValue.filter((value) => value !== currentValue)
                                    : [...newValue, currentValue];
                                  field.onChange(updatedValue);
                                }}
                              >
                                <Check
                                  className={`mr-2 h-4 w-4 ${
                                    field.value?.includes(icp.id) ? "opacity-100" : "opacity-0"
                                  }`}
                                />
                                {icp.name || `ICP ${icp.id.slice(0, 8)}`}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  
                  {/* Display selected ICPs as badges */}
                  {field.value && field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value.map((icpId) => {
                        const icp = icps?.find(i => i.id === icpId);
                        return (
                          <Badge key={icpId} variant="secondary" className="flex items-center gap-1">
                            {icp?.name || `ICP ${icpId.slice(0, 8)}`}
                            <X
                              className="h-3 w-3 cursor-pointer"
                              onClick={() => {
                                const newValue = field.value?.filter(id => id !== icpId) || [];
                                field.onChange(newValue);
                              }}
                            />
                          </Badge>
                        );
                      })}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <label className="text-sm font-medium">Key Features</label>
              <p className="text-xs text-gray-500 mb-2">Your main product features with their value propositions and differentiators</p>
              
              <div className="space-y-4">
                {keyFeatures.map((feature, index) => (
                  <div key={index} className="p-3 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <Input
                        value={feature.name}
                        onChange={(e) => updateKeyFeature(index, 'name', e.target.value)}
                        placeholder="Feature name"
                        className="flex-1 font-medium"
                      />
                      {keyFeatures.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeKeyFeature(index)}
                          className="ml-2"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Textarea
                        value={feature.value_prop}
                        onChange={(e) => updateKeyFeature(index, 'value_prop', e.target.value)}
                        placeholder="Value proposition - What benefit does this feature provide?"
                        className="w-full"
                        rows={2}
                      />
                      <Textarea
                        value={feature.differentiation}
                        onChange={(e) => updateKeyFeature(index, 'differentiation', e.target.value)}
                        placeholder="Key differentiation - How does this feature set you apart from competitors?"
                        className="w-full"
                        rows={2}
                      />
                    </div>
                  </div>
                ))}
              </div>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addKeyFeature}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Feature
              </Button>
            </div>

            <div className="flex gap-4 pt-4">
              <Button type="submit" disabled={isPending}>
                {isPending ? 'Creating...' : 'Create Product'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}