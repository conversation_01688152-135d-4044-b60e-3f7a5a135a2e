'use client';

import { useState, useMemo } from 'react';
import { Avatar, AvatarFallback } from '@kit/ui/avatar';
import { Button } from '@kit/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@kit/ui/command';
import { Check, User } from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface AssigneeAvatarProps {
  taskId: string;
  assignedTo?: string;
  onAssigneeChange?: (userId: string | null) => void;
}

export function AssigneeAvatar({ taskId, assignedTo, onAssigneeChange }: AssigneeAvatarProps) {
  const [open, setOpen] = useState(false);
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();

  // Get all memberships for this company
  const [memberships] = useZeroQuery(
    zero.query.accounts_memberships.where('account_id', '=', workspace.account.id),
    {
      ttl: '1d'
    }
  );

  // Get all accounts (users) from memberships
  const [accounts] = useZeroQuery(zero.query.accounts, {
    ttl: '1d'
  });

  // Get company members with their account details
  const companyMembers = useMemo(() => {
    if (!memberships || !accounts) return [];
    
    return memberships.map(membership => {
      const account = accounts.find(acc => acc.id === membership.user_id);
      return {
        id: membership.user_id,
        name: account?.name || 'Unknown User',
        email: account?.email || '',
        role: membership.account_role
      };
    }).filter(member => member.name !== 'Unknown User');
  }, [memberships, accounts]);

  // Find the assigned user
  const assignedUser = useMemo(() => {
    if (!assignedTo) return null;
    return companyMembers.find(member => member.id === assignedTo);
  }, [assignedTo, companyMembers]);

  const handleAssigneeSelect = async (userId: string | null) => {
    try {
      // Update the company_content record
      await zero.mutate.company_content.update({
        id: taskId,
        values: {
          assigned_to: userId || undefined
        }
      });
      
      // Call the optional callback
      onAssigneeChange?.(userId);
      setOpen(false);
    } catch (error) {
      console.error('Failed to update assignee:', error);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 rounded-full p-0 hover:bg-muted"
        >
          {assignedUser ? (
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs font-medium bg-primary/10 text-primary">
                {getInitials(assignedUser.name)}
              </AvatarFallback>
            </Avatar>
          ) : (
            <div className="h-8 w-8 rounded-full border-2 border-dashed border-muted-foreground/30 flex items-center justify-center hover:border-muted-foreground/50 transition-colors">
              <User className="h-4 w-4 text-muted-foreground/50" />
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-0" align="start">
        <Command>
          <CommandInput placeholder="Search team members..." />
          <CommandList>
            <CommandEmpty>No team members found.</CommandEmpty>
            <CommandGroup>
              {/* Unassign option */}
              <CommandItem
                value="unassign"
                onSelect={() => handleAssigneeSelect(null)}
                className="flex items-center gap-2"
              >
                <div className="h-6 w-6 rounded-full border border-dashed border-muted-foreground/30 flex items-center justify-center">
                  <User className="h-3 w-3 text-muted-foreground/50" />
                </div>
                <span className="text-muted-foreground">Unassigned</span>
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    !assignedTo ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
              
              {/* Team members */}
              {companyMembers.map((member) => (
                <CommandItem
                  key={member.id}
                  value={member.name}
                  onSelect={() => handleAssigneeSelect(member.id)}
                  className="flex items-center gap-2"
                >
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className="text-xs font-medium bg-primary/10 text-primary">
                      {getInitials(member.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{member.name}</span>
                    {member.email && (
                      <span className="text-xs text-muted-foreground">{member.email}</span>
                    )}
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      assignedTo === member.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
} 