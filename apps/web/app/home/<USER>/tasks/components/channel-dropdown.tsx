"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { Button } from "@kit/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@kit/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@kit/ui/popover"
import { cn } from "@kit/ui/utils"

interface ChannelSwitcherProps {
  channels: string[]
  className?: string
  selectedChannel: string | null
  setSelectedChannel: (channel: string | null) => void
}

export default function ChannelSwitcher({ channels, className, selectedChannel, setSelectedChannel }: ChannelSwitcherProps) {
  const [open, setOpen] = React.useState(false)

  const handleChannelSelect = (channel: string) => {
    setSelectedChannel(channel);
    setOpen(false);
  };

  const handleAllSelect = () => {
    setSelectedChannel(null);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select a channel"
          className={cn("w-[200px] justify-between", className)}
        >
          <span className="truncate flex-1 min-w-0 text-left"> 
            {selectedChannel ?? "All Channels"}
          </span>
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search channels..." />
          <CommandList>
            <CommandEmpty>No channel found.</CommandEmpty>
            <CommandGroup>
              <CommandItem
                value="all"
                onSelect={handleAllSelect}
                className="text-sm"
              >
                All Channels
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    selectedChannel === null
                      ? "opacity-100"
                      : "opacity-0"
                  )}
                />
              </CommandItem>
            </CommandGroup>
            {channels.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  {channels.map((channel) => (
                    <CommandItem
                      key={channel}
                      value={channel}
                      onSelect={() => handleChannelSelect(channel)}
                      className="text-sm"
                    >
                      {channel}
                      <Check
                        className={cn(
                          "ml-auto h-4 w-4",
                          selectedChannel === channel
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
} 