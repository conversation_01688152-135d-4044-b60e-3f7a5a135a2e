import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

interface AyrsharePost {
  commentCount: number;
  comments?: string[];
  commentsState: string;
  created: string;
  id: string;
  lastModified: string;
  likeBy?: string[];
  likeCount: number;
  likedByCurrentUser: boolean;
  mediaUrls?: Array<{
    altText?: string;
    id: string;
    url: string;
  }>;
  post: string;
  postUrl: string;
  publishedAt: string;
  reactions: Record<string, number>;
  share: string;
  status: string;
  target: string;
  totalFirstLevelComments: number;
  visibility: string;
  organizationalEntity?: string;
  uniqueImpressionsCount?: number;
}

interface AyrshareResponse {
  status: string;
  posts: AyrsharePost[];
  lastUpdated: string;
  nextUpdate: string;
  postsCount: number;
}

export async function fetchLinkedInAnalytics(profileKey: string): Promise<AyrshareResponse | null> {
  const logger = await getLogger();
  console.log({profileKey})
  try {
    logger.info('Fetching LinkedIn analytics from Ayrshare API for profile key: ' + profileKey);
    
    const response = await fetch('https://api.ayrshare.com/api/history/linkedin?limit=100&skipAnalytics=false', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer 30BFF2EA-975E4A1B-BC5EC31D-E50A6C71',
        'Content-Type': 'application/json',
        'Profile-Key': profileKey,
      },
    });

    if (!response.ok) {
      logger.error('Failed to fetch LinkedIn analytics', {
        status: response.status,
        statusText: response.statusText,
      });
      return null;
    }

    const data = await response.json() as AyrshareResponse;
    
    // const likedBy = data.posts.map((post) => post.likeBy);
    // console.log({likedBy})

    // const likeResponse = await fetch(`https://api.ayrshare.com/api/brand/byUser?platforms[0]=linkedin&linkedinUser=${likedBy[3]}`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': 'Bearer 30BFF2EA-975E4A1B-BC5EC31D-E50A6C71',
    //     'Content-Type': 'application/json',
    //     'Profile-Key': profileKey,
    //   },
    // });

    // const likeData = await likeResponse.json();
    // console.log(JSON.stringify(likeData, null, 2))

    logger.info('Successfully fetched LinkedIn analytics', {
      postsCount: data.postsCount,
    });


    
    return data;
  } catch (error) {
    logger.error(`Error fetching LinkedIn analytics:  ${error}`);
    return null;
  }
}



//upsert the linkedin analytics data into the database
// export async function upsertLinkedInAnalytics(id: string, data: AyrshareResponse) {
//   const logger = await getLogger();
//   const supabase = getSupabaseServerClient();
//   const { data, error } = await supabase
//     .from('ayrshare_user_profile')
//     .upsert({
//       id: id,
//       linkedin_analytics: data,
//     }, {
//       onConflict: 'id',
//     })

//   if (error) {
//     logger.error('Error upserting LinkedIn analytics data into the database', {
//       error: error,
//     });
//   }

//   logger.info('Upserted LinkedIn analytics data into the database', {
//     data: data,
//   });

//   return data;
// }

export type { AyrsharePost, AyrshareResponse };