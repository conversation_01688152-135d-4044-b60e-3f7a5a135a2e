import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@kit/ui/card';
// import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import type { AyrshareResponse } from '../_lib/server/analytics.service';

interface AnalyticsOverviewProps {
  data: AyrshareResponse;
}

export function AnalyticsOverview({ data }: AnalyticsOverviewProps) {
  console.log("data", data);
  
  // Guard against undefined or null data
  if (!data || !data.posts || !Array.isArray(data.posts)) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              <Trans i18nKey="analytics:totalPosts" defaults="Total Posts" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              <Trans i18nKey="analytics:postsInHistory" defaults="Posts in history" />
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalLikes = data.posts.reduce((sum, post) => sum + (post.likeCount || 0), 0);
  const totalComments = data.posts.reduce((sum, post) => sum + (post.commentCount || 0), 0);
  const avgEngagement = data.posts.length > 0 ? (totalLikes + totalComments) / data.posts.length : 0;
  
  // Filter out values that are NaN
  const totalImpressions = data.posts.reduce((sum, post) => {
    console.log({post})
    if (post.uniqueImpressionsCount && !isNaN(post.uniqueImpressionsCount)) {
      return sum + post.uniqueImpressionsCount;
    }
    return sum;
  }, 0);
  
  // Check if any post has organizational entity data
  const isOrganization = data.posts.some((post) => post.organizationalEntity);
  const avgImpressions = data.posts.length > 0 ? totalImpressions / data.posts.length : 0;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="analytics:totalPosts" defaults="Total Posts" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{data.postsCount || 0}</div>
          <p className="text-xs text-muted-foreground">
            <Trans i18nKey="analytics:postsInHistory" defaults="Posts in history" />
          </p>
        </CardContent>
      </Card>

      {/* <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="analytics:totalLikes" defaults="Total Impressions" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalImpressions}</div>
          <p className="text-xs text-muted-foreground">
            <Trans i18nKey="analytics:acrossAllPosts" defaults="Across all posts" />
          </p>
        </CardContent>
      </Card> */}

      
      {/* <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="analytics:totalComments" defaults="Total Comments" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalComments}</div>
          <p className="text-xs text-muted-foreground">
            <Trans i18nKey="analytics:totalEngagement" defaults="Total engagement" />
          </p>
        </CardContent>
      </Card> */}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="analytics:avgEngagement" defaults="Avg Engagement" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{avgEngagement.toFixed(1)}</div>
          <p className="text-xs text-muted-foreground">
            <Trans i18nKey="analytics:likesCommentsPerPost" defaults="Likes + comments per post" />
          </p>
        </CardContent>
      </Card>

      {/* Average impressions per post - only show if organization data is available */}
      {isOrganization && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              <Trans i18nKey="analytics:avgImpressionsPerPost" defaults="Avg Impressions per Post" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgImpressions.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              <Trans i18nKey="analytics:impressionsPerPost" defaults="Impressions per post" />
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 