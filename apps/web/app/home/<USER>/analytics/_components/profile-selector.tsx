'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';
import { SocialProfile } from './analytics-client-wrapper';

interface ProfileSelectorProps {
  profiles: SocialProfile[];
  selectedProfileKey?: string;
  onProfileChange: (profileKey: string) => void;
}

export function ProfileSelector({ profiles, selectedProfileKey, onProfileChange }: ProfileSelectorProps) {
  console.log('ProfileSelector Debug:', {
    profilesLength: profiles.length,
    profiles,
    selectedProfileKey,
  });

  if (!profiles || profiles.length === 0) {
    return null;
  }

  // Single profile case
  if (profiles.length === 1) {
    const firstProfile = profiles[0];
    if (!firstProfile) return null;
    
    const displayName = firstProfile.profile_name || firstProfile.title || `Profile ${firstProfile.id}`;
    
    return (
      <div className="text-sm text-muted-foreground">
        <p>{displayName}</p>
      </div>
    );
  }

  // Multiple profiles case - should show dropdown
  console.log('Rendering dropdown for multiple profiles:', profiles.length);
  
  // Find the selected profile for display
  const selectedProfile = profiles.find(profile => profile.profileKey === selectedProfileKey);
  const selectedDisplayName = selectedProfile ? 
    (selectedProfile.profile_name || selectedProfile.title || `Profile ${selectedProfile.id}`) : 
    undefined;
  
  console.log('Selected profile info:', {
    selectedProfileKey,
    selectedProfile,
    selectedDisplayName
  });
  
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium">
        <Trans i18nKey="analytics:selectProfile" defaults="Profile:" />
      </span>
      <div className="flex items-center gap-2">
        <Select value={selectedProfileKey} onValueChange={onProfileChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select a profile" />
          </SelectTrigger>
          <SelectContent>
            {profiles.map((profile) => {
              const displayName = profile.profile_name || profile.title || `Profile ${profile.id}`;
              console.log('Profile item:', { id: profile.id, profileKey: profile.profileKey, displayName });
              
              return (
                <SelectItem key={profile.id} value={profile.profileKey}>
                  {displayName}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
        {/* Temporary debug indicator - remove after testing */}
        <span className="text-xs text-muted-foreground bg-blue-100 px-2 py-1 rounded">
          {profiles.length} profiles
        </span>
      </div>
    </div>
  );
} 