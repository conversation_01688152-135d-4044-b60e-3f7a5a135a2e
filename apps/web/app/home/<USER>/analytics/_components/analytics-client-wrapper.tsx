'use client';

import { useState, useEffect, useMemo } from 'react';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AlertCircle } from 'lucide-react';

import { AnalyticsHeader } from './analytics-header';
import { AnalyticsOverview } from './analytics-overview';
import { PostAnalyticsList } from './post-analytics-list';
import { ProfileSelector } from './profile-selector';

export interface SocialProfile {
  id: string;
  title: string;
  profileKey: string;
  user_id: string;
  company_id: string;
  created_at: string;
  profile_name: string;
  userProfileId: string;
}

interface AnalyticsClientWrapperProps {
  profiles: SocialProfile[];
  initialAnalyticsData: any;
  initialProfileKey: string;
}

export function AnalyticsClientWrapper({ 
  profiles, 
  initialAnalyticsData, 
  initialProfileKey 
}: AnalyticsClientWrapperProps) {
  console.log('AnalyticsClientWrapper Debug:', {
    profilesLength: profiles.length,
    profiles,
    initialProfileKey,
    initialAnalyticsData
  });

  const [selectedProfileKey, setSelectedProfileKey] = useState(initialProfileKey);
  const [analyticsData, setAnalyticsData] = useState(initialAnalyticsData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize the selected profile to prevent unnecessary re-renders
  const selectedProfile = useMemo(() => {
    return profiles.find(profile => profile.profileKey === selectedProfileKey);
  }, [profiles, selectedProfileKey]);

  // Sync selectedProfile when profiles change
  useEffect(() => {
    if (!selectedProfile && profiles.length > 0) {
      const firstProfile = profiles[0];
      if (firstProfile) {
        setSelectedProfileKey(firstProfile.profileKey);
      }
    }
  }, [profiles, selectedProfile]);

  console.log('AnalyticsClientWrapper State:', {
    selectedProfileKey,
    selectedProfile,
    profilesPassedToSelector: profiles.length
  });

  const handleProfileChange = async (profileKey: string) => {
    console.log('handleProfileChange called with:', profileKey);
    console.log('Current selectedProfileKey:', selectedProfileKey);
    
    if (profileKey === selectedProfileKey) {
      console.log('Profile key unchanged, returning early');
      return;
    }
    
    console.log('Changing profile from', selectedProfileKey, 'to', profileKey);
    
    setSelectedProfileKey(profileKey);
    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching analytics for profile key:', profileKey);
      const response = await fetch(`/api/analytics/linkedin?profileKey=${encodeURIComponent(profileKey)}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch analytics data');
      }
      
      const newAnalyticsData = await response.json();
      console.log('Successfully fetched new analytics data:', newAnalyticsData);
      setAnalyticsData(newAnalyticsData);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <AnalyticsHeader
        title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
        description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
      >
        <ProfileSelector
          profiles={profiles}
          selectedProfileKey={selectedProfileKey}
          onProfileChange={handleProfileChange}
        />
      </AnalyticsHeader>

      <PageBody>
        <div className="flex flex-col gap-8">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                <Trans i18nKey="analytics:errorTitle" defaults="Error loading analytics" />
              </AlertTitle>
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          ) : analyticsData ? (
            <>
              <AnalyticsOverview data={analyticsData} />
              <PostAnalyticsList profileId={selectedProfile?.userProfileId} posts={analyticsData.posts} />
            </>
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                <Trans i18nKey="analytics:errorTitle" defaults="Error loading analytics" />
              </AlertTitle>
              <AlertDescription>
                <Trans i18nKey="analytics:errorDescription" defaults="Unable to fetch LinkedIn analytics data. Please try again later." />
              </AlertDescription>
            </Alert>
          )}
        </div>
      </PageBody>
    </>
  );
} 