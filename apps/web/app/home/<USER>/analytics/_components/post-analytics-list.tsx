'use client';
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';
import { ExternalLink, FileText, Heart, Loader2, MessageCircle, Share, User, TrendingUp, BarChart3 } from 'lucide-react';
import { useState } from 'react';
import type { AyrsharePost } from '../_lib/server/analytics.service';
import {  useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@kit/ui/chart';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
} from 'recharts';

interface PostAnalyticsListProps {
  posts: AyrsharePost[];
  profileId: string | undefined;
}

interface EngagementProfile {
  icp_score: number;
  rationale: string;
  profile_id: string;
  full_name?: string;
  profile_image?: string;
  country_full_name?: string;

}

function EngagementReportModal({ engagementData }: { engagementData: EngagementProfile[] }) {
  const getScoreColor = (score: number) => {
    if (score >= 8) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 6) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    if (score >= 4) return 'bg-orange-100 text-orange-800 border-orange-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 8) return 'High Match';
    if (score >= 6) return 'Good Match';
    if (score >= 4) return 'Moderate Match';
    return 'Low Match';
  };

  const averageScore = engagementData.length > 0 
    ? (engagementData.reduce((sum, profile) => sum + profile.icp_score, 0) / engagementData.length).toFixed(1)
    : '0';

  const highValueProfiles = engagementData.filter(profile => profile.icp_score >= 7).length;
  const totalProfiles = engagementData.length;

  return (
    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          <Trans i18nKey="analytics:engagementReportTitle" defaults="Post Engagement Analysis" />
        </DialogTitle>
        <DialogDescription>
          <Trans i18nKey="analytics:engagementReportDescription" defaults="Detailed analysis of user engagement and ICP alignment" />
        </DialogDescription>
      </DialogHeader>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{totalProfiles}</div>
            <div className="text-sm text-muted-foreground">Total Profiles</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{averageScore}</div>
            <div className="text-sm text-muted-foreground">Average ICP Score</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{highValueProfiles}</div>
            <div className="text-sm text-muted-foreground">High-Value Matches</div>
          </CardContent>
        </Card>
      </div>

      {/* Profile List */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <User className="h-4 w-4" />
          Profile Analysis
        </h3>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {engagementData
            .sort((a, b) => b.icp_score - a.icp_score)
            .map((profile, index) => (
            <Card key={profile.profile_id} className="p-4">
              <div className="flex items-start justify-between gap-4">
                {/* if no profile image, display a default image */}
                {profile.profile_image ? (
                  <img src={profile.profile_image} alt={profile.full_name || profile.profile_id} className="h-12 w-12 rounded-full object-cover" />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center">
                    <User className="h-6 w-6 text-muted-foreground" />
                  </div>
                )}
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={`${getScoreColor(profile.icp_score)} font-medium`}>
                      Score: {profile.icp_score}/10
                    </Badge>
                    <Badge variant="outline">
                      {getScoreLabel(profile.icp_score)}
                    </Badge>
                  </div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">
                    Name: {profile.full_name || profile.profile_id}
                  </div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">
                    Country: {profile.country_full_name || 'N/A'}
                  </div>
                  <p className="text-sm leading-relaxed">
                    {profile.rationale}
                  </p>
                </div>
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-muted">
                  <TrendingUp className={`h-5 w-5 ${profile.icp_score >= 7 ? 'text-green-600' : profile.icp_score >= 4 ? 'text-yellow-600' : 'text-red-600'}`} />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </DialogContent>
  );
}

export function PostAnalyticsList({ posts, profileId }: PostAnalyticsListProps) {
  const [openModal, setOpenModal] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const calculateEngagementStats = (engagementData: EngagementProfile[]) => {
    if (!engagementData || engagementData.length === 0) {
      return { averageScore: 0, highValueProfiles: 0, totalProfiles: 0 };
    }

    const averageScore = engagementData.reduce((sum, profile) => sum + profile.icp_score, 0) / engagementData.length;
    const highValueProfiles = engagementData.filter(profile => profile.icp_score >= 7).length;
    const totalProfiles = engagementData.length;

    return { averageScore, highValueProfiles, totalProfiles };
  };

  const calculateOverallStats = (posts: any[]) => {
    const allEngagementProfiles: EngagementProfile[] = [];
    
    posts.forEach(post => {
      if (post.engagement_details && 
          !post.engagement_details.is_generating && 
          !post.engagement_details.error_generating && 
          post.engagement_details.engagement_info && 
          Array.isArray(post.engagement_details.engagement_info)) {
        allEngagementProfiles.push(...(post.engagement_details.engagement_info as EngagementProfile[]));
      }
    });

    if (allEngagementProfiles.length === 0) {
      return { 
        totalProfiles: 0, 
        icpProfiles: 0, 
        nonIcpProfiles: 0, 
        icpRatio: 0, 
        overallAverageScore: 0 
      };
    }

    const icpProfiles = allEngagementProfiles.filter(profile => profile.icp_score >= 7).length;
    const nonIcpProfiles = allEngagementProfiles.length - icpProfiles;
    const icpRatio = allEngagementProfiles.length > 0 ? (icpProfiles / allEngagementProfiles.length) * 100 : 0;
    const overallAverageScore = allEngagementProfiles.reduce((sum, profile) => sum + profile.icp_score, 0) / allEngagementProfiles.length;

    return {
      totalProfiles: allEngagementProfiles.length,
      icpProfiles,
      nonIcpProfiles,
      icpRatio,
      overallAverageScore
    };
  };

  const prepareChartData = (posts: any[]) => {
    return posts
      .filter(post => 
        post.engagement_details && 
        !post.engagement_details.is_generating && 
        !post.engagement_details.error_generating && 
        post.engagement_details.engagement_info && 
        Array.isArray(post.engagement_details.engagement_info) &&
        (post.engagement_details.engagement_info as EngagementProfile[]).length > 0
      )
      .map((post, index) => {
        const engagementProfiles = post.engagement_details.engagement_info as EngagementProfile[];
        const icpProfiles = engagementProfiles.filter(profile => profile.icp_score >= 7).length;
        const icpRatio = (icpProfiles / engagementProfiles.length) * 100;
        const avgScore = engagementProfiles.reduce((sum, profile) => sum + profile.icp_score, 0) / engagementProfiles.length;
        
        return {
          postIndex: index + 1,
          postDate: formatDate(post.publishedAt),
          icpRatio: parseFloat(icpRatio.toFixed(1)),
          avgIcpScore: parseFloat(avgScore.toFixed(1)),
          postId: post.id,
        };
      })
      .reverse(); // Show oldest to newest for better timeline visualization
  };

  const prepareEngagementChartData = (posts: any[]) => {
    return posts
      .filter(post => 
        typeof post.likeCount === 'number' && 
        typeof post.commentCount === 'number'
      )
      .map((post, index) => {
        const totalEngagement = post.likeCount + post.commentCount;
        
        return {
          postIndex: index + 1,
          postDate: formatDate(post.publishedAt),
          likes: post.likeCount,
          comments: post.commentCount,
          totalEngagement,
          postId: post.id,
        };
      })
      .reverse(); // Show oldest to newest for better timeline visualization
  };

  const zero = useZero();
  const workspace = useTeamAccountWorkspace();

  const [postEngagementDetails] = useZeroQuery(
    zero.query.post_engagement_details
    .where('company_id', '=', workspace.account.id)
    .where('profile_id', '=', profileId || ''),
    {
      ttl: '10m'
    }
  );

  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const generateEngagementReport = (platform_post_id: string, isError: boolean = false, id: string = '') => {
    console.log('generateEngagementReport', {
      isError,
      id,
      platform_post_id
    })
    if(isError) {
      // delete the post_engagement_details for the postId if it exists
      zero.mutate.post_engagement_details.delete({
        id,
      });
    }

    // insert a new post_engagement_details for the postId
    zero.mutate.post_engagement_details.insert({
      id: crypto.randomUUID(),
      values: {
        platform_post_id: platform_post_id, 
        profile_id: profileId || '',
        is_generating: true,
        error_generating: false,
        engagement_info: {},
        company_id: workspace.account.id,
        // only the likeBy field for the postId in this function
        engaged_users: posts.find((post) => post.id === platform_post_id)?.likeBy || [],
      },
    });
  };

  if (posts.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-medium text-muted-foreground">
              <Trans i18nKey="analytics:noPosts" defaults="No posts found" />
            </h3>
            <p className="text-sm text-muted-foreground">
              <Trans i18nKey="analytics:noPostsDescription" defaults="No LinkedIn posts available to display." />
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // if post enegements has an ide which is also in the posts array, add that array to the post
  const postsWithEngagementDetails = posts.map((post) => {
    const engagementDetail = postEngagementDetails.find((detail) => detail.platform_post_id === post.id);
    console.log('engagementDetail', engagementDetail);
    return {
      ...post,
      engagement_details: engagementDetail,
    };
  });

  console.log('postsWithEngagementDetails', postsWithEngagementDetails);
  const overallStats = calculateOverallStats(postsWithEngagementDetails);
  const chartData = prepareChartData(postsWithEngagementDetails);
  const engagementChartData = prepareEngagementChartData(postsWithEngagementDetails);
  console.log('chartData', chartData);
  console.log('engagementChartData', engagementChartData);
  
  const chartConfig = {
    icpRatio: {
      label: 'ICP Ratio (%)',
      color: '#2563eb', // blue-600 to match summary card
    },
    avgIcpScore: {
      label: 'Avg ICP Score',
      color: '#16a34a', // green-600 to match summary card
    },
  } satisfies ChartConfig;

  const engagementChartConfig = {
    likes: {
      label: 'Likes',
      color: '#f59e0b', // amber-500
    },
    comments: {
      label: 'Comments', 
      color: '#8b5cf6', // violet-500
    },
    totalEngagement: {
      label: 'Total Engagement',
      color: '#ef4444', // red-500
    },
  } satisfies ChartConfig;

  return (
    <div className="space-y-6">
      {/* Overall ICP Summary Stats */}
      {overallStats.totalProfiles > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <Trans i18nKey="analytics:overallIcpSummary" defaults="Overall ICP Engagement" />
            </CardTitle>
            <CardDescription>
              <Trans i18nKey="analytics:overallIcpSummaryDescription" defaults="Summary of ICP engagement across all analyzed posts" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {overallStats.icpRatio.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  <Trans i18nKey="analytics:icpToNonIcpRatio" defaults="ICP to Non-ICP Ratio" />
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {overallStats.icpProfiles} ICP / {overallStats.nonIcpProfiles} Non-ICP
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {overallStats.overallAverageScore.toFixed(1)}/10
                </div>
                <div className="text-sm text-muted-foreground">
                  <Trans i18nKey="analytics:overallAvgIcpScore" defaults="Overall Avg ICP Score" />
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Across {overallStats.totalProfiles} profiles
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* ICP Performance Timeline Chart */}
      {chartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              <Trans i18nKey="analytics:icpPerformanceTimeline" defaults="ICP Performance Timeline" />
            </CardTitle>
            <CardDescription>
              <Trans i18nKey="analytics:icpPerformanceTimelineDescription" defaults="ICP ratio and average score trends across posts" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="postIndex" 
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => `Post ${value}`}
                />
                <YAxis 
                  yAxisId="ratio"
                  orientation="left"
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value}%`}
                  domain={[0, 100]}
                />
                <YAxis 
                  yAxisId="score"
                  orientation="right"
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value}/10`}
                  domain={[0, 10]}
                />
                <ChartTooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0]?.payload;
                      return (
                        <div className="bg-background border rounded-lg p-3 shadow-md">
                          <p className="font-medium">Post {label}</p>
                          <p className="text-sm text-muted-foreground">{data?.postDate}</p>
                          <div className="mt-2 space-y-1">
                            <p className="flex items-center gap-2 text-sm">
                              <span className="w-3 h-3 rounded-full" style={{ backgroundColor: '#2563eb' }}></span>
                              ICP Ratio: {data?.icpRatio}%
                            </p>
                            <p className="flex items-center gap-2 text-sm">
                              <span className="w-3 h-3 rounded-full" style={{ backgroundColor: '#16a34a' }}></span>
                              Avg ICP Score: {data?.avgIcpScore}/10
                            </p>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Line
                  yAxisId="ratio"
                  type="monotone"
                  dataKey="icpRatio"
                  stroke="var(--color-icpRatio)"
                  strokeWidth={2}
                  dot={{ fill: "var(--color-icpRatio)", strokeWidth: 2, r: 4 }}
                />
                <Line
                  yAxisId="score"
                  type="monotone"
                  dataKey="avgIcpScore"
                  stroke="var(--color-avgIcpScore)"
                  strokeWidth={2}
                  dot={{ fill: "var(--color-avgIcpScore)", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      )}

      {/* Engagement Timeline Chart */}
      {engagementChartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-primary" />
              <Trans i18nKey="analytics:engagementTimeline" defaults="Engagement Timeline" />
            </CardTitle>
            <CardDescription>
              <Trans i18nKey="analytics:engagementTimelineDescription" defaults="Likes and comments trends across posts" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={engagementChartConfig} className="h-64 w-full">
              <LineChart data={engagementChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="postIndex" 
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => `Post ${value}`}
                />
                <YAxis 
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value}`}
                />
                <ChartTooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0]?.payload;
                      return (
                        <div className="bg-background border rounded-lg p-3 shadow-md">
                          <p className="font-medium">Post {label}</p>
                          <p className="text-sm text-muted-foreground">{data?.postDate}</p>
                          <div className="mt-2 space-y-1">
                            <p className="flex items-center gap-2 text-sm">
                              <span className="w-3 h-3 rounded-full" style={{ backgroundColor: '#f59e0b' }}></span>
                              Likes: {data?.likes}
                            </p>
                            <p className="flex items-center gap-2 text-sm">
                              <span className="w-3 h-3 rounded-full" style={{ backgroundColor: '#8b5cf6' }}></span>
                              Comments: {data?.comments}
                            </p>
                            <p className="flex items-center gap-2 text-sm">
                              <span className="w-3 h-3 rounded-full" style={{ backgroundColor: '#ef4444' }}></span>
                              Total: {data?.totalEngagement}
                            </p>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="likes"
                  stroke="var(--color-likes)"
                  strokeWidth={2}
                  dot={{ fill: "var(--color-likes)", strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="comments"
                  stroke="var(--color-comments)"
                  strokeWidth={2}
                  dot={{ fill: "var(--color-comments)", strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="totalEngagement"
                  stroke="var(--color-totalEngagement)"
                  strokeWidth={2}
                  dot={{ fill: "var(--color-totalEngagement)", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      )}

      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">
          <Trans i18nKey="analytics:recentPosts" defaults="Recent Posts" />
        </h2>
        <Badge variant="secondary">{posts.length} posts</Badge>
      </div>
      
      <div className="grid gap-4">
        {postsWithEngagementDetails.map((post) => {
          console.log('post', post);
        return (
          <Card key={post.id} className="transition-shadow hover:shadow-md">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-base">
                  <Button variant="outline" size="lg" asChild>
                  <a 
                    href={post.postUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center gap-1"
                  >
                    <ExternalLink className="h-3 w-3" />
                    <Trans i18nKey="analytics:viewPost" defaults="View" />
                  </a>
                </Button>
                  </CardTitle>
                  <CardDescription>
                    {formatDate(post.publishedAt)} • {post.status}
                  </CardDescription>
                </div>
              {
              post.engagement_details ?
              
              (
                // also impelment a try again button if post.engagement_details.error_generating is true
                post.engagement_details.error_generating ?
                <div>
                  <Button variant="outline" size="sm" className="border-red-500" onClick={() => generateEngagementReport(post.id, true, post?.engagement_details?.id)}>
                    Error - click to try again
                  </Button>
                </div>
                :
                post.engagement_details.is_generating ?
              <div>
                {/* Add a spinner here and indicate that its generating */}
                <div className="flex items-center gap-1">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  <Trans i18nKey="analytics:generatingEngagementReport" defaults="Generating Engagement Report" />
                </div>
              </div>
              :
              <Dialog open={openModal === post.id} onOpenChange={(open) => setOpenModal(open ? post.id : null)}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <div className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      <Trans i18nKey="analytics:viewEngagementReport" defaults="View Engagement Report" />
                    </div>
                  </Button>
                </DialogTrigger>
                <EngagementReportModal 
                  engagementData={(post.engagement_details.engagement_info as unknown as EngagementProfile[]) || []} 
                />
              </Dialog>
              )
              :
              <div>
              <Button variant="outline" onClick={() => generateEngagementReport(post.id)} size="sm" >
                <div className="flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    <Trans i18nKey="analytics:generateEngagementReport" defaults="Generate Engagement Report" />
                  </div>
                </Button>
              </div>
              }
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {post.post && (
                <div className="prose prose-sm max-w-none">
                  <p className="text-sm leading-relaxed">
                    {truncateText(post.post)}
                  </p>
                </div>
              )}
              
              {post.mediaUrls && post.mediaUrls.length > 0 && (
                <div className="flex gap-2">
                  {post.mediaUrls.slice(0, 3).map((media, index) => (
                    <div key={media.id} className="relative">
                      <img 
                        src={media.url} 
                        alt={media.altText || 'Post image'} 
                        className="h-16 w-16 rounded-md object-cover"
                      />
                      {post.mediaUrls!.length > 3 && index === 2 && (
                        <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black bg-opacity-50 text-xs text-white">
                          +{post.mediaUrls!.length - 3}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
              
              <div className="flex items-center justify-between border-t pt-4">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Heart className="h-4 w-4" />
                    <span>{post.likeCount}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageCircle className="h-4 w-4" />
                    <span>{post.commentCount}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Share className="h-4 w-4" />
                    <span className="capitalize">{post.visibility.toLowerCase()}</span>
                  </div>
                  
                  {/* ICP Analytics */}
                  {post.engagement_details && 
                   !post.engagement_details.is_generating && 
                   !post.engagement_details.error_generating && 
                   post.engagement_details.engagement_info && 
                   Array.isArray(post.engagement_details.engagement_info) && 
                   (post.engagement_details.engagement_info as EngagementProfile[]).length > 0 && (
                    <>
                      <div className="flex items-center gap-1 text-blue-600">
                        <BarChart3 className="h-4 w-4" />
                        <span>
                          Avg ICP: {`${calculateEngagementStats(post.engagement_details.engagement_info as EngagementProfile[]).averageScore.toFixed(1)} / 10`} 
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-green-600">
                        <TrendingUp className="h-4 w-4" />
                        <span>
                          {/* ICPs: {`${calculateEngagementStats(post.engagement_details.engagement_info as EngagementProfile[]).highValueProfiles} / ${ (post.engagement_details.engagement_info as EngagementProfile[]).length}`}  */}
                          ICPs: {`${calculateEngagementStats(post.engagement_details.engagement_info as EngagementProfile[]).highValueProfiles} `} 
                        </span>
                      </div>
                    </>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  {Object.entries(post.reactions).map(([reaction, count]) => (
                    <Badge key={reaction} variant="outline" className="text-xs">
                      {reaction}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )
        }
       
        )}
      </div>
    </div>
  );
} 