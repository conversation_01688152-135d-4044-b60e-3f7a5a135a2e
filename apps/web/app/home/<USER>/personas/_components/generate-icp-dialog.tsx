'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Sparkles, Check, ChevronsUpDown } from 'lucide-react';

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@kit/ui/form';
import { Textarea } from '@kit/ui/textarea';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@kit/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

interface Product {
  id: string;
  name: string;
  description?: string;
  key_features?: Array<{name: string; value_prop: string; differentiation: string}>;
}

interface GenerateICPDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (data: { description: string; selectedProducts: string[] }) => void;
  products: Product[];
  isGenerating?: boolean;
}

const GenerateICPSchema = z.object({
  description: z.string().min(1, 'Description is required').max(500, 'Description is too long'),
  selectedProducts: z.array(z.string()).min(1, 'Please select at least one product'),
});

type GenerateICPFormData = z.infer<typeof GenerateICPSchema>;

export function GenerateICPDialog({
  isOpen,
  onClose,
  onGenerate,
  products,
  isGenerating = false
}: GenerateICPDialogProps) {
  const [isProductSelectorOpen, setIsProductSelectorOpen] = useState(false);

  const form = useForm<GenerateICPFormData>({
    resolver: zodResolver(GenerateICPSchema),
    defaultValues: {
      description: '',
      selectedProducts: [],
    },
  });

  const handleSubmit = (data: GenerateICPFormData) => {
    onGenerate(data);
    form.reset();
    onClose();
  };

  const handleClose = () => {
    if (!isGenerating) {
      form.reset();
      onClose();
    }
  };

  const selectedProducts = form.watch('selectedProducts');
  const selectedProductNames = products
    .filter(product => selectedProducts.includes(product.id))
    .map(product => product.name);

  const toggleProduct = (productId: string) => {
    const current = form.getValues('selectedProducts');
    const updated = current.includes(productId)
      ? current.filter(id => id !== productId)
      : [...current, productId];
    form.setValue('selectedProducts', updated);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            <Trans i18nKey="personas:generateICPWithAI" defaults="Generate ICP with AI" />
          </DialogTitle>
          <DialogDescription>
            <Trans 
              i18nKey="personas:generateICPDescription" 
              defaults="Provide a brief description of your ideal customer and select relevant products to help AI generate a comprehensive ICP."
            />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="personas:icpDescription" defaults="ICP Description" />
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your ideal customer in a few words..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans 
                      i18nKey="personas:icpDescriptionHelp" 
                      defaults="Describe in a few words if you have an idea about the ICP (e.g., 'Mid-size SaaS companies looking for customer analytics tools')"
                    />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="selectedProducts"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="personas:referenceProducts" defaults="Reference Products" />
                  </FormLabel>
                  <Popover open={isProductSelectorOpen} onOpenChange={setIsProductSelectorOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          type="button"
                          className={cn(
                            "w-full justify-between text-left font-normal",
                            !selectedProducts.length && "text-muted-foreground"
                          )}
                        >
                          {selectedProducts.length > 0
                            ? `${selectedProducts.length} product(s) selected`
                            : "Select products..."
                          }
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search products..." />
                        <CommandList>
                          <CommandEmpty>No products found.</CommandEmpty>
                          <CommandGroup>
                            {products.map((product) => (
                              <CommandItem
                                key={product.id}
                                value={product.name}
                                onSelect={() => toggleProduct(product.id)}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedProducts.includes(product.id)
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {product.name}
                                {product.description && (
                                  <span className="ml-2 text-xs text-muted-foreground truncate">
                                    {product.description.slice(0, 50)}...
                                  </span>
                                )}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  {selectedProductNames.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedProductNames.map((name) => (
                        <Badge key={name} variant="secondary" className="text-xs">
                          {name}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <FormDescription>
                    <Trans 
                      i18nKey="personas:referenceProductsHelp" 
                      defaults="Select the relevant products to use as reference material to generate this ICP"
                    />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isGenerating}
              >
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </Button>
              <Button type="submit" disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    <Trans i18nKey="personas:generating" defaults="Generating..." />
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    <Trans i18nKey="personas:generateICP" defaults="Generate ICP" />
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 