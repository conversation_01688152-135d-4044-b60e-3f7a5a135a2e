import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@kit/ui/card";
import { But<PERSON> } from "@kit/ui/button";
import { Edit, Eye, Users, Loader2, AlertCircle, MoreVertical, Trash } from "lucide-react";
import { Trans } from "@kit/ui/trans";
import { Badge } from "@kit/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@kit/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@kit/ui/alert-dialog";
import { ICP } from "~/types/icp";
import { useState } from "react";
import EditICPDialog from "./edit-icp-dialog";
import { useZero } from "~/hooks/use-zero";
import { toast } from "sonner";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";

export function ICPCard({ icp, accountSlug }: { icp: ICP, accountSlug: string }) {
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const zero = useZero();
    const workspace = useTeamAccountWorkspace();
    const isGenerating = icp.is_generating;
    const errorGenerating = icp.error_generating;
    const handleDelete = async () => {
        setIsDeleting(true);
        try {
            zero.mutate.icps.delete({
                id: icp.id,
            });
            console.log('Deleting ICP:', icp.id);
            // toast.success('ICP deleted successfully');
        } catch (error) {
            console.error('Error deleting ICP:', error);
        } finally {
            setIsDeleting(false);
            setIsDeleteDialogOpen(false);
        }
    };
    
    const handleRetry = async () => {
      const reference_material = icp.reference_material || [];
      const reference_description = icp.reference_description || '';
      await handleDelete();
    
      zero.mutate.icps.insert({
        id: crypto.randomUUID(),
        values: {
          company_id: workspace.account.id,
          withAi: icp.withAi,
          withLinkedIn: icp.withLinkedIn,
          name: null,
          data: {},
          error_generating: false,
          reference_material: reference_material,
          reference_description: reference_description,
          linkedInUrls: icp.linkedInUrls
        }
      });
    };

    if(errorGenerating) {
        return (
            <Card className="hover:shadow-md transition-shadow">
                <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                    <AlertCircle className="h-8 w-8 text-red-500 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">
                        <Trans i18nKey="personas:errorGenerating" defaults="Error Generating ICP" />
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                        <Trans i18nKey="personas:errorGeneratingDescription" defaults="Something went wrong during generation" />
                    </p>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRetry}
                    >
                        <Trans i18nKey="personas:tryAgain" defaults="Try Again" />
                    </Button>
                </CardContent>
            </Card>
        );
    }
    if (isGenerating) {
        return (
            <Card className="hover:shadow-md transition-shadow">
                <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                    <h3 className="text-lg font-semibold mb-2">
                        <Trans i18nKey="personas:generating" defaults="Generating ICP..." />
                    </h3>
                    <p className="text-sm text-muted-foreground">
                        <Trans i18nKey="personas:generatingDescription" defaults="Creating your ideal customer profile" />
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader>
          <CardTitle className="flex items-start justify-between">
            <span className="text-lg font-semibold">{icp.name}</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  <Trans i18nKey="common:edit" defaults="Edit" />
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  <Trans i18nKey="common:delete" defaults="Delete" />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Personas count */}
          {/* <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              <Trans 
                i18nKey="personas:personasCount" 
                values={{ count: icp.personas_count || 0 }}
                defaults="{count, plural, =0 {No personas} one {# persona} other {# personas}}"
              />
            </span>
          </div> */}
  
          {/* Key attributes preview */}
          <div className="space-y-2">
            {icp.data?.target_industries && Array.isArray(icp.data.target_industries) && icp.data.target_industries.length > 0 && (
              <div>
                <span className="text-xs text-muted-foreground">Industries:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {(icp.data.target_industries as string[]).map((industry) => (
                    <Badge key={industry} variant="outline" className="text-xs">
                      {industry}
                    </Badge>
                  ))}
                  {/* {(icp.data.target_industries as string[]).length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{(icp.data.target_industries as string[]).length - 2} more
                    </Badge>
                  )} */}
                </div>
              </div>
            )}
  
            {icp.data?.geography_markets && Array.isArray(icp.data.geography_markets) && icp.data.geography_markets.length > 0 && (
              <div>
                <span className="text-xs text-muted-foreground">Markets:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {(icp.data.geography_markets as string[]).map((market) => (
                    <Badge key={market} variant="outline" className="text-xs">
                      {market}
                    </Badge>
                  ))}
                  {/* {(icp.data.geography_markets as string[]).length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{(icp.data.geography_markets as string[]).length - 2} more
                    </Badge>
                  )} */}
                </div>
              </div>
            )}
  
            {icp.data?.company_revenue_range_usd && icp.data.company_revenue_range_usd.min && icp.data.company_revenue_range_usd.max && (
              <div>
                <span className="text-xs text-muted-foreground">Revenue Range:</span>
                <Badge variant="secondary" className="ml-2 text-xs">
                  ${icp.data.company_revenue_range_usd.min.toLocaleString()} - ${icp.data.company_revenue_range_usd.max.toLocaleString()}
                </Badge>
              </div>
            )}

            {icp.data?.decision_making_departments && Array.isArray(icp.data.decision_making_departments) && icp.data.decision_making_departments.length > 0 && (
              <div>
                <span className="text-xs text-muted-foreground">Decision Makers:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {(icp.data.decision_making_departments as string[]).map((dept) => (
                    <Badge key={dept} variant="outline" className="text-xs">
                      {dept}
                    </Badge>
                  ))}
                  {/* {(icp.data.decision_making_departments as string[]).length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{(icp.data.decision_making_departments as string[]).length - 2} more
                    </Badge>
                  )} */}
                </div>
              </div>
            )}
  
            {icp.data?.buying_triggers && Array.isArray(icp.data.buying_triggers) && icp.data.buying_triggers.length > 0 && (
              <div>
                <span className="text-xs text-muted-foreground">Buying Triggers:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {(icp.data.buying_triggers as string[]).map((trigger) => (
                    <Badge key={trigger} variant="outline" className="text-xs">
                      {trigger}
                    </Badge>
                  ))}
                  {/* {(icp.data.buying_triggers as string[]).length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{(icp.data.buying_triggers as string[]).length - 2} more
                    </Badge>
                  )} */}
                </div>
              </div>
            )}
  
            {icp.data?.use_cases_problems && Array.isArray(icp.data.use_cases_problems) && icp.data.use_cases_problems.length > 0 && (
              <div>
                <span className="text-xs text-muted-foreground">Use Cases:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {(icp.data.use_cases_problems as string[]).map((useCase) => (
                    <Badge key={useCase} variant="outline" className="text-xs">
                      {useCase}
                    </Badge>
                  ))}
                  {/* {(icp.data.use_cases_problems as string[]).length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{(icp.data.use_cases_problems as string[]).length - 2} more
                    </Badge>
                  )} */}
                </div>
              </div>
            )}
          </div>
  
          {/* Action buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="default"
              size="sm"
              className="flex-1"
              asChild
            >
              <a href={`/home/<USER>/personas/icp/${icp.id}`}>
                <Eye className="h-4 w-4 mr-2" />
                <Trans i18nKey="personas:viewPersonas" defaults="View Personas" />
              </a>
            </Button>
          </div>
        </CardContent>
        
        <EditICPDialog
          icp={icp}
          isOpen={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
        />

        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                <Trans i18nKey="personas:deleteICPTitle" defaults="Delete ICP" />
              </AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete {icp.name}? This action cannot be undone and will also delete all associated personas.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    <Trans i18nKey="common:deleting" defaults="Deleting..." />
                  </>
                ) : (
                  <>
                    <Trash className="h-4 w-4 mr-2" />
                    <Trans i18nKey="common:delete" defaults="Delete" />
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Card>
    );
  } 