'use client';

import { useState, KeyboardEvent } from 'react';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { X, Plus } from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { Button } from '@kit/ui/button';

interface HybridSelectFieldProps {
  value: string[];
  onChange: (value: string[]) => void;
  options: readonly string[];
  placeholder?: string;
  className?: string;
  label?: string;
}

export default function HybridSelectField({
  value = [],
  onChange,
  options,
  className
}: HybridSelectFieldProps) {
  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<string>('');

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const trimmedValue = inputValue.trim();
      
      if (trimmedValue && !value.includes(trimmedValue)) {
        onChange([...value, trimmedValue]);
        setInputValue('');
      }
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      // Remove last tag if input is empty and backspace is pressed
      onChange(value.slice(0, -1));
    }
  };

  const handleSelectOption = (option: string) => {
    if (option && !value.includes(option)) {
      onChange([...value, option]);
    }
    setSelectedOption('');
  };

  const removeTag = (indexToRemove: number) => {
    onChange(value.filter((_, index) => index !== indexToRemove));
  };

  // Filter out already selected options
  const availableOptions = options.filter(option => !value.includes(option));

  return (
    <div className={cn("space-y-3", className)}>
      {/* Dropdown Selection */}
      <div className="flex gap-2">
        <Select value={selectedOption} onValueChange={handleSelectOption}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select from predefined options..." />
          </SelectTrigger>
          <SelectContent>
            {availableOptions.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Manual Input */}
      <div className="flex gap-2">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Or type a custom option..."
          className="flex-1"
        />
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => {
            const trimmedValue = inputValue.trim();
            if (trimmedValue && !value.includes(trimmedValue)) {
              onChange([...value, trimmedValue]);
              setInputValue('');
            }
          }}
          disabled={!inputValue.trim() || value.includes(inputValue.trim())}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Selected Tags */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {value.map((tag, index) => (
            <Badge
              key={`${tag}-${index}`}
              variant="secondary"
              className="flex items-center gap-1 px-2 py-1"
            >
              <span>{tag}</span>
              <button
                type="button"
                onClick={() => removeTag(index)}
                className="ml-1 h-3 w-3 flex items-center justify-center rounded-full hover:bg-muted-foreground/20 transition-colors"
                aria-label={`Remove ${tag}`}
              >
                <X className="h-2 w-2" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
} 