'use client';

import { Input } from "@kit/ui/input";
import { NumberRange } from "~/types/icp";

interface NumberRangeFieldProps {
  value: NumberRange;
  onChange: (value: NumberRange) => void;
  placeholder?: {
    min: string;
    max: string;
  };
  className?: string;
}

export default function NumberRangeField({
  value,
  onChange,
  placeholder = { min: "Min", max: "Max" },
  className
}: NumberRangeFieldProps) {
  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value === '' ? null : Number(e.target.value);
    onChange({ ...value, min: newValue });
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value === '' ? null : Number(e.target.value);
    onChange({ ...value, max: newValue });
  };

  return (
    <div className={`flex gap-2 ${className}`}>
      <div className="flex-1">
        <Input
          type="number"
          placeholder={placeholder.min}
          value={value.min ?? ''}
          onChange={handleMinChange}
          min={0}
        />
      </div>
      <div className="flex items-center px-2 text-muted-foreground">
        to
      </div>
      <div className="flex-1">
        <Input
          type="number"
          placeholder={placeholder.max}
          value={value.max ?? ''}
          onChange={handleMaxChange}
          min={value.min ?? 0}
        />
      </div>
    </div>
  );
} 