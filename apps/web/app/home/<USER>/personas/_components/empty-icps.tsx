import { Users, Plus } from "lucide-react";
import { Button } from "@kit/ui/button";
import { Trans } from "@kit/ui/trans";

interface EmptyICPsProps {
  accountSlug: string;
}

export default function EmptyICPs({ accountSlug }: EmptyICPsProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center space-y-6">
      <div className="rounded-full bg-muted p-6">
        <Users className="h-12 w-12 text-muted-foreground" />
      </div>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">
          <Trans i18nKey="personas:noICPsTitle" defaults="No Ideal Customer Profiles" />
        </h3>
        <p className="text-muted-foreground max-w-md">
          <Trans 
            i18nKey="personas:noICPsDescription" 
            defaults="Start by creating your first Ideal Customer Profile to organize your personas by customer segments."
          />
        </p>
      </div>
      
      {/* <Button asChild>
        <a href={`/home/<USER>/personas?type=new`}>
          <Plus className="h-4 w-4 mr-2" />
          <Trans i18nKey="personas:createFirstICP" defaults="Create Your First ICP" />
        </a>
      </Button> */}
    </div>
  );
} 