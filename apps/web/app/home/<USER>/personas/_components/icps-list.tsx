import { ICP } from "~/types/icp";
import { ICPCard } from "./icp-card";

interface ICPsListProps {
  icps: ICP[];
  accountSlug: string;
}

export default function ICPsList({ icps, accountSlug }: ICPsListProps) {
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {icps.map((icp) => (
          <ICPCard
            key={icp.id}
            icp={icp}
            accountSlug={accountSlug}
          />
        ))}
      </div>
    </div>
  );
}

interface ICPCardProps {
  icp: ICP;
  accountSlug: string;
}
