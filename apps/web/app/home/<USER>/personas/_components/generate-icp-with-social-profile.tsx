'use client';

import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
// TO DO: remove deprecated lucide icons for linkedin
import { Linkedin, Plus, X, Check, ChevronsUpDown } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@kit/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from '~/hooks/use-zero';

interface Product {
  id: string;
  name: string;
  description?: string;
  key_features?: Array<{name: string; value_prop: string; differentiation: string}>;
}

interface GenerateICPWithSocialProfileDialogProps {
  isOpen: boolean;
  onClose: () => void;
  products: Product[];
}

const LinkedInUrlSchema = z.string().url('Please enter a valid URL').refine(
  (url) => url.includes('linkedin.com'),
  { message: 'Please enter a valid LinkedIn URL' }
);

const GenerateICPWithSocialProfileSchema = z.object({
  linkedinUrls: z.array(
    z.object({
      url: LinkedInUrlSchema,
    })
  ).min(1, 'Please enter at least one LinkedIn URL').max(5, 'Maximum 5 LinkedIn URLs allowed'),
  selectedProducts: z.array(z.string()).min(1, 'Please select at least one product'),
});

type GenerateICPWithSocialProfileFormData = z.infer<typeof GenerateICPWithSocialProfileSchema>;

export function GenerateICPWithSocialProfileDialog({
  isOpen,
  onClose,
  products
}: GenerateICPWithSocialProfileDialogProps) {
  const [isProductSelectorOpen, setIsProductSelectorOpen] = useState(false);
  const form = useForm<GenerateICPWithSocialProfileFormData>({
    resolver: zodResolver(GenerateICPWithSocialProfileSchema),
    defaultValues: {
      linkedinUrls: [{ url: '' }],
      selectedProducts: [],
    },
  });
  const { account } = useTeamAccountWorkspace();
  const zero = useZero();

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'linkedinUrls',
  });

  const handleSubmit = (data: GenerateICPWithSocialProfileFormData) => {
    const urls = data.linkedinUrls.map(item => item.url);
    // TODO: Implement actual LinkedIn profile processing
    zero.mutate.icps.insert({
        id: crypto.randomUUID(),
        values : {
            company_id: account.id,
            withAi: true,
            withLinkedIn: true,
            name: null,
            data: {},
            is_generating: true,
            error_generating: false,
            reference_products: data.selectedProducts,
            reference_description: '',
            linkedInUrls: urls
        }
    });
    form.reset();
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  const addUrlField = () => {
    if (fields.length < 5) {
      append({ url: '' });
    }
  };

  const removeUrlField = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const canAddMore = fields.length < 5;
  const canRemove = fields.length > 1;

  const selectedProducts = form.watch('selectedProducts');
  const selectedProductNames = products
    .filter(product => selectedProducts.includes(product.id))
    .map(product => product.name);

  const toggleProduct = (productId: string) => {
    const current = form.getValues('selectedProducts');
    const updated = current.includes(productId)
      ? current.filter(id => id !== productId)
      : [...current, productId];
    form.setValue('selectedProducts', updated);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Linkedin className="h-5 w-5" />
            <Trans i18nKey="personas:generateICPWithLinkedIn" defaults="Generate ICP with LinkedIn Profiles" />
          </DialogTitle>
          <DialogDescription>
            <Trans 
              i18nKey="personas:generateICPWithLinkedInDescription" 
              defaults="Enter LinkedIn profile URLs of your ideal customers to generate an ICP based on their professional information and company details."
            />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <FormLabel className="text-sm font-medium">
                  <Trans i18nKey="personas:linkedinUrls" defaults="LinkedIn Profile URLs" />
                </FormLabel>
                {canAddMore && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addUrlField}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    <Trans i18nKey="personas:addUrl" defaults="Add" />
                  </Button>
                )}
              </div>

              {fields.map((field, index) => (
                <FormField
                  key={field.id}
                  control={form.control}
                  name={`linkedinUrls.${index}.url`}
                  render={({ field: fieldProps }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormControl>
                          <Input
                            placeholder="https://linkedin.com/in/profile-name"
                            {...fieldProps}
                          />
                        </FormControl>
                        {canRemove && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeUrlField(index)}
                            className="px-2"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}

              <FormDescription>
                <Trans 
                  i18nKey="personas:linkedinUrlsHelp" 
                  defaults="Enter LinkedIn profile URLs of your ideal customers. You can add up to 5 profiles to help us generate a comprehensive ICP."
                />
              </FormDescription>
            </div>

            <FormField
              control={form.control}
              name="selectedProducts"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="personas:referenceProducts" defaults="Reference Products" />
                  </FormLabel>
                  <Popover open={isProductSelectorOpen} onOpenChange={setIsProductSelectorOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          type="button"
                          className={cn(
                            "w-full justify-between text-left font-normal",
                            !selectedProducts.length && "text-muted-foreground"
                          )}
                        >
                          {selectedProducts.length > 0
                            ? `${selectedProducts.length} product(s) selected`
                            : "Select products..."
                          }
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search products..." />
                        <CommandList>
                          <CommandEmpty>No products found.</CommandEmpty>
                          <CommandGroup>
                            {products.map((product) => (
                              <CommandItem
                                key={product.id}
                                value={product.name}
                                onSelect={() => toggleProduct(product.id)}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedProducts.includes(product.id)
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {product.name}
                                {product.description && (
                                  <span className="ml-2 text-xs text-muted-foreground truncate">
                                    {product.description.slice(0, 50)}...
                                  </span>
                                )}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  {selectedProductNames.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedProductNames.map((name) => (
                        <Badge key={name} variant="secondary" className="text-xs">
                          {name}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <FormDescription>
                    <Trans 
                      i18nKey="personas:referenceProductsHelp" 
                      defaults="Select the relevant products to use as reference material to generate this ICP"
                    />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
              >
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </Button>
              <Button
                type="submit"
                className="flex items-center gap-2"
              >
                <Linkedin className="h-4 w-4" />
                  <Trans i18nKey="personas:generateICP" defaults="Generate ICP" />
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
