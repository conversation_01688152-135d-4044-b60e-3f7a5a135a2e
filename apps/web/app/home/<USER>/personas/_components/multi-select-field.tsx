'use client';

import { useState } from "react";
import { Badge } from "@kit/ui/badge";
import { Button } from "@kit/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@kit/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@kit/ui/popover";
import { X, Plus, Check } from "lucide-react";
import { cn } from "@kit/ui/utils";

interface MultiSelectFieldProps {
  value: string[];
  onChange: (value: string[]) => void;
  options: readonly string[];
  placeholder?: string;
  className?: string;
}

export default function MultiSelectField({
  value = [],
  onChange,
  options,
  placeholder = "Select options...",
  className
}: MultiSelectFieldProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = (option: string) => {
    if (value.includes(option)) {
      onChange(value.filter((item) => item !== option));
    } else {
      onChange([...value, option]);
    }
  };

  const handleRemove = (option: string) => {
    onChange(value.filter((item) => item !== option));
  };

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-start h-auto min-h-[2.5rem] p-2"
          >
            {value.length === 0 ? (
              <span className="text-muted-foreground">{placeholder}</span>
            ) : (
              <div className="flex flex-wrap gap-1">
                {value.slice(0, 3).map((item) => (
                  <Badge
                    key={item}
                    variant="secondary"
                    className="text-xs"
                  >
                    {item}
                    <button
                      type="button"
                      className="ml-1 hover:text-destructive"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemove(item);
                      }}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
                {value.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{value.length - 3} more
                  </Badge>
                )}
              </div>
            )}
            <Plus className="ml-auto h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Search options..." />
            <CommandEmpty>No options found.</CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {options.map((option) => (
                <CommandItem
                  key={option}
                  value={option}
                  onSelect={() => handleSelect(option)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value.includes(option) ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected items display for better UX */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {value.map((item) => (
            <Badge
              key={item}
              variant="outline"
              className="text-xs"
            >
              {item}
              <button
                type="button"
                className="ml-1 hover:text-destructive"
                onClick={() => handleRemove(item)}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
} 