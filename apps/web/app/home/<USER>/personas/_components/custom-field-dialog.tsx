'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Badge } from '@kit/ui/badge';
import { Plus, X, Settings } from 'lucide-react';
import { CustomField } from '../_lib/schema/icp.schema';

const CreateCustomFieldSchema = z.object({
  label: z.string().min(1, 'Field label is required'),
  type: z.enum(['text', 'textarea', 'number', 'numberrange', 'select', 'multiselect']),
  options: z.array(z.string()).optional(),
});

type CreateCustomFieldSchemaType = z.infer<typeof CreateCustomFieldSchema>;

interface CustomFieldDialogProps {
  onAddField: (field: CustomField) => void;
}

export default function CustomFieldDialog({ onAddField }: CustomFieldDialogProps) {
  const [open, setOpen] = useState(false);
  const [optionInput, setOptionInput] = useState('');
  
  const form = useForm<CreateCustomFieldSchemaType>({
    resolver: zodResolver(CreateCustomFieldSchema),
    defaultValues: {
      label: '',
      type: 'text',
      options: [],
    },
  });

  const watchedType = form.watch('type');
  const watchedOptions = form.watch('options') || [];

  const addOption = () => {
    const trimmed = optionInput.trim();
    if (trimmed && !watchedOptions.includes(trimmed)) {
      form.setValue('options', [...watchedOptions, trimmed]);
      setOptionInput('');
    }
  };

  const removeOption = (index: number) => {
    const newOptions = watchedOptions.filter((_, i) => i !== index);
    form.setValue('options', newOptions);
  };

  const onSubmit = (data: CreateCustomFieldSchemaType) => {
    const customField: CustomField = {
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      label: data.label,
      type: data.type,
      value: getDefaultValue(data.type),
      options: (data.type === 'select' || data.type === 'multiselect') ? data.options : undefined,
    };

    onAddField(customField);
    setOpen(false);
    form.reset();
    setOptionInput('');
  };

  const getDefaultValue = (type: string) => {
    switch (type) {
      case 'multiselect':
        return [];
      case 'number':
        return null;
      case 'numberrange':
        return { min: null, max: null };
      case 'text':
      case 'textarea':
      case 'select':
      default:
        return '';
    }
  };

  const requiresOptions = watchedType === 'select' || watchedType === 'multiselect';

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="h-auto p-3 flex flex-col gap-1 text-left border-dashed border-2"
        >
          <div className="flex items-center gap-2 w-full">
            <Settings className="h-4 w-4 flex-shrink-0" />
            <span className="text-xs font-medium">Add Custom Field</span>
          </div>
          <span className="text-xs text-muted-foreground">Create your own field</span>
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create Custom Field</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              name="label"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Field Label *</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Budget Authority" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Field Type *</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select field type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="text">Text Input</SelectItem>
                      <SelectItem value="textarea">Long Text (Textarea)</SelectItem>
                      <SelectItem value="number">Number</SelectItem>
                      <SelectItem value="numberrange">Number Range (Min/Max)</SelectItem>
                      <SelectItem value="select">Single Select (Dropdown)</SelectItem>
                      <SelectItem value="multiselect">Multi Select (Tags)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {requiresOptions && (
              <div className="space-y-3">
                <FormLabel>Options *</FormLabel>
                
                <div className="flex gap-2">
                  <Input
                    value={optionInput}
                    onChange={(e) => setOptionInput(e.target.value)}
                    placeholder="Add an option..."
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addOption();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addOption}
                    disabled={!optionInput.trim() || watchedOptions.includes(optionInput.trim())}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {watchedOptions.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {watchedOptions.map((option, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1 px-2 py-1"
                      >
                        <span>{option}</span>
                        <button
                          type="button"
                          onClick={() => removeOption(index)}
                          className="ml-1 h-3 w-3 flex items-center justify-center rounded-full hover:bg-muted-foreground/20 transition-colors"
                        >
                          <X className="h-2 w-2" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}

                {requiresOptions && watchedOptions.length === 0 && (
                  <p className="text-xs text-muted-foreground">
                    Add at least one option for {watchedType === 'select' ? 'dropdown' : 'multi-select'} fields
                  </p>
                )}
              </div>
            )}

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={requiresOptions && watchedOptions.length === 0}
              >
                Add Field
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 