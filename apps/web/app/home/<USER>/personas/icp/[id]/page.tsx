'use client';
import { notFound, usePara<PERSON>, usePathname, useRouter } from "next/navigation";
import { If } from "@kit/ui/if";
import { Trans } from "@kit/ui/trans";
import { PageBody } from "@kit/ui/page";
import { But<PERSON> } from "@kit/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { ArrowLeft, Plus, Edit, ChevronDown, Sparkles } from "lucide-react";
import PersonasList from "../../_components/personas-list";
import CreatePersonaDialog from "../../_components/create-persona-dialog";
import EmptyPersonas from "../../_components/empty-personas";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useZero } from "~/hooks/use-zero";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { useState } from "react";
import { Persona } from "~/types/persona";

export default function ICPDetailPage() {
  const router = useRouter();
  const { id } = useParams();
  const workspace = useTeamAccountWorkspace();
  const [createPersonaOpen, setCreatePersonaOpen] = useState(false);

  const zero = useZero();

  const [personas, personasResult] = useZeroQuery(
    zero.query.personas
    .where(
      "icp_id", "=", id as string
    ).where(
      "company_id", "=", workspace.account.id,
    ).orderBy("created_at", "desc"), 
    {
      ttl: '1d'
    }
  );

  const handleAIGeneration = () => {
    zero.mutate.personas.insert({
      id: crypto.randomUUID(),
      company_id: workspace.account.id,
      icp_id: id as string,
      withAi: true,
      data: {},
      name: 'null',
      error_generating: false,
    });
  };

  const typedPersonas = personas as Persona[];

 

  return (
    <div>
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              <Trans i18nKey="common:back" defaults="Back to ICPs" />
          </Button>
        </div>
        
        <div className="flex justify-between items-start">
          <div>
            {/* <h1 className="text-2xl font-bold">{icp[0]?.name ?? ''}</h1> */}
            <p className="text-muted-foreground mt-2">
              <Trans 
                i18nKey="personas:icpPersonasDescription" 
                defaults="Manage personas for this Ideal Customer Profile"
              />
            </p>
          </div>
          
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  <Trans i18nKey="personas:createPersona" defaults="Add Persona" />
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem className='cursor-pointer' onClick={handleAIGeneration}>
                  <Sparkles className="h-4 w-4 mr-2" />
                  <Trans i18nKey="personas:generateWithAI" defaults="Generate With AI" />
                </DropdownMenuItem>
                <DropdownMenuItem className='cursor-pointer' onClick={() => setCreatePersonaOpen(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  <Trans i18nKey="personas:createManually" defaults="Create Manually" />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <PageBody>
 
        <If condition={personasResult.type !== 'complete' && typedPersonas.length === 0}>
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
          </div>
        </If>
       
        <If condition={typedPersonas.length > 0 && personasResult.type === 'complete'}>
          <PersonasList personas={typedPersonas} accountSlug={workspace.account.slug} />
        </If>
        
        <If condition={typedPersonas.length === 0 && personasResult.type === 'complete'}>
          <EmptyPersonas icpId={id as string} setCreatePersonaOpen={setCreatePersonaOpen} />
        </If>
        
        <CreatePersonaDialog 
          isNew={createPersonaOpen} 
          setIsNew={setCreatePersonaOpen}
          companyId={workspace.account.id}
          icpId={id as string}
        />
      </PageBody>
    </div>
  );
} 