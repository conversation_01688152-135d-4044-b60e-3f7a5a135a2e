'use server';

import { enhanceAction } from '@kit/next/actions';
import { createICP, updateICP, deleteICP } from '~/services/icp';

// Helper function to transform form data to database schema
export function transformFormDataToSchema(formData: any) {
  const { name, company_id, ...dataFields } = formData;
  console.log('dataFields', dataFields);
  return {
    name,
    company_id,
    data: dataFields,
  };
}

export const createICPAction = enhanceAction(
  async function (formData: any) {
    console.log("Creating ICP for user:", formData);
    
    const transformedData = transformFormDataToSchema(formData);
    console.log('transformedData', transformedData);
    return await createICP({
      ...transformedData,
      // Override company_id for now - remove this when properly implemented
    });
  },
  {
    auth: true,
    // We'll validate the transformed data in the service layer
  },
);

export const updateICPAction = enhanceAction(
  async function (formData: any, user) {
    console.log("Updating ICP for user:", user);
    
    const { id, ...restData } = formData;
    const transformedData = transformFormDataToSchema(restData);
    
    return await updateICP({
      id,
      ...transformedData,
    });
  },
  {
    auth: true,
    // We'll validate the transformed data in the service layer
  },
);

export const deleteICPAction = enhanceAction(
  async function (data: { id: string }, user) {
    console.log("Deleting ICP for user:", user);
    await deleteICP(data.id);
    return { success: true };
  },
  {
    auth: true,
  },
); 