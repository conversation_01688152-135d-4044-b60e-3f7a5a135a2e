'use server';

import { z } from 'zod';
import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

const SaveGeneratedResearchSchema = z.object({
  accountId: z.string().uuid(),
  icpId: z.string().uuid(),
  personaId: z.string().uuid(),
  researchType: z.enum(['pain-points', 'trending-topics', 'recent-news']),
  timeFilter: z.string(),
  title: z.string(),
  results: z.array(z.any()),
  contentSuggestions: z.array(z.any()),
  topic: z.string().optional(),
});

const UpdateGeneratedResearchSchema = z.object({
  researchId: z.string().uuid(),
  title: z.string().optional(),
  results: z.array(z.any()).optional(),
  contentSuggestions: z.array(z.any()).optional(),
});

const DeleteGeneratedResearchSchema = z.object({
  researchId: z.string().uuid(),
});

export const saveGeneratedResearchAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const supabase = getSupabaseServerClient();

    logger.info('Saving generated research', {
      accountId: data.accountId,
      researchType: data.researchType,
      userId: user.id,
    });

    try {
      const { data: savedResearch, error } = await (supabase as any)
        .from('generated_research')
        .insert({
          account_id: data.accountId,
          icp_id: data.icpId,
          persona_id: data.personaId,
          research_type: data.researchType,
          time_filter: data.timeFilter,
          title: data.title,
          results: data.results,
          content_suggestions: data.contentSuggestions,
          topic: data.topic,
        })
        .select()
        .single();

      if (error) {
        logger.error('Failed to save generated research', {
          error: error.message,
          accountId: data.accountId,
          userId: user.id,
        });
        throw new Error(`Failed to save research: ${error.message}`);
      }

      logger.info('Generated research saved successfully', {
        researchId: savedResearch.id,
        accountId: data.accountId,
        userId: user.id,
      });

      return {
        success: true,
        data: savedResearch,
      };
    } catch (error) {
      logger.error('Error saving generated research', {
        error: error instanceof Error ? error.message : 'Unknown error',
        accountId: data.accountId,
        userId: user.id,
      });

      throw error;
    }
  },
  {
    auth: true,
    schema: SaveGeneratedResearchSchema,
  },
);

export const updateGeneratedResearchAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const supabase = getSupabaseServerClient();

    logger.info('Updating generated research', {
      researchId: data.researchId,
      userId: user.id,
    });

    try {
      const updateData: any = {};
      
      if (data.title !== undefined) {
        updateData.title = data.title;
      }
      if (data.results !== undefined) {
        updateData.results = data.results;
      }
      if (data.contentSuggestions !== undefined) {
        updateData.content_suggestions = data.contentSuggestions;
      }

      const { data: updatedResearch, error } = await supabase
        .from('generated_research')
        .update(updateData)
        .eq('id', data.researchId)
        .select()
        .single();

      if (error) {
        logger.error('Failed to update generated research', {
          error: error.message,
          researchId: data.researchId,
          userId: user.id,
        });
        throw new Error(`Failed to update research: ${error.message}`);
      }

      logger.info('Generated research updated successfully', {
        researchId: data.researchId,
        userId: user.id,
      });

      return {
        success: true,
        data: updatedResearch,
      };
    } catch (error) {
      logger.error('Error updating generated research', {
        error: error instanceof Error ? error.message : 'Unknown error',
        researchId: data.researchId,
        userId: user.id,
      });

      throw error;
    }
  },
  {
    auth: true,
    schema: UpdateGeneratedResearchSchema,
  },
);

export const deleteGeneratedResearchAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const supabase = getSupabaseServerClient();

    logger.info('Deleting generated research', {
      researchId: data.researchId,
      userId: user.id,
    });

    try {
      const { error } = await supabase
        .from('generated_research')
        .delete()
        .eq('id', data.researchId);

      if (error) {
        logger.error('Failed to delete generated research', {
          error: error.message,
          researchId: data.researchId,
          userId: user.id,
        });
        throw new Error(`Failed to delete research: ${error.message}`);
      }

      logger.info('Generated research deleted successfully', {
        researchId: data.researchId,
        userId: user.id,
      });

      return {
        success: true,
      };
    } catch (error) {
      logger.error('Error deleting generated research', {
        error: error instanceof Error ? error.message : 'Unknown error',
        researchId: data.researchId,
        userId: user.id,
      });

      throw error;
    }
  },
  {
    auth: true,
    schema: DeleteGeneratedResearchSchema,
  },
); 