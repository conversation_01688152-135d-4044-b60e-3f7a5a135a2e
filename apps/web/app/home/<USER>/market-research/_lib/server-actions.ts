'use server';

import { z } from 'zod';
import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

const SaveResearchSchema = z.object({
  accountId: z.string().uuid(),
  icpId: z.string().uuid(),
  personaId: z.string().uuid(),
  title: z.string().min(1),
  description: z.string().min(1),
  source: z.string().min(1),
  source_url: z.string().min(1),
  relevanceScore: z.number().min(0).max(10),
  researchType: z.enum(['pain-points', 'trending-topics', 'recent-news']),
  timeFilter: z.string().min(1),
  topic: z.string().optional(),
});

const RemoveSavedResearchSchema = z.object({
  accountId: z.string().uuid(),
  icpId: z.string().uuid(),
  personaId: z.string().uuid(),
  title: z.string().min(1),
  researchType: z.enum(['pain-points', 'trending-topics', 'recent-news']),
});

export const saveResearchAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const supabase = getSupabaseServerClient();

    logger.info('Saving research item', {
      userId: user.id,
      accountId: data.accountId,
      title: data.title,
    });

    try {
      const { error } = await supabase
        .from('saved_research')
        .insert({
          account_id: data.accountId,
          icp_id: data.icpId,
          persona_id: data.personaId,
          title: data.title,
          description: data.description,
          source: data.source,
          source_url: data.source_url,
          relevance_score: data.relevanceScore,
          research_type: data.researchType,
          time_filter: data.timeFilter,
          topic: data.topic,
        });

      if (error) {
        logger.error('Failed to save research item', { error: error.message });
        throw new Error(`Failed to save research item: ${error.message}`);
      }

      logger.info('Research item saved successfully');

      return {
        success: true,
        message: 'Research item saved successfully',
      };
    } catch (error) {
      logger.error('Error saving research item', { error });
      throw error;
    }
  },
  {
    auth: true,
    schema: SaveResearchSchema,
  },
);

export const removeSavedResearchAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const supabase = getSupabaseServerClient();

    logger.info('Removing saved research item', {
      userId: user.id,
      accountId: data.accountId,
      title: data.title,
    });

    try {
      const { error } = await supabase
        .from('saved_research')
        .delete()
        .eq('account_id', data.accountId)
        .eq('icp_id', data.icpId)
        .eq('persona_id', data.personaId)
        .eq('title', data.title)
        .eq('research_type', data.researchType);

      if (error) {
        logger.error('Failed to remove saved research item', { error: error.message });
        throw new Error(`Failed to remove saved research item: ${error.message}`);
      }

      logger.info('Saved research item removed successfully');

      return {
        success: true,
        message: 'Research item removed from saved items',
      };
    } catch (error) {
      logger.error('Error removing saved research item', { error });
      throw error;
    }
  },
  {
    auth: true,
    schema: RemoveSavedResearchSchema,
  },
);

export const getSavedResearchAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const supabase = getSupabaseServerClient();

    logger.info('Fetching saved research items', {
      userId: user.id,
      accountId: data.accountId,
    });

    try {
      const { data: savedResearch, error } = await supabase
        .from('saved_research')
        .select('*')
        .eq('account_id', data.accountId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Failed to fetch saved research items', { error: error.message });
        throw new Error(`Failed to fetch saved research items: ${error.message}`);
      }

      return {
        success: true,
        data: savedResearch || [],
      };
    } catch (error) {
      logger.error('Error fetching saved research items', { error });
      throw error;
    }
  },
  {
    auth: true,
    schema: z.object({
      accountId: z.string().uuid(),
    }),
  },
);
