'use client';

import { Badge } from '@kit/ui/badge';
import { Card, CardContent, CardHeader } from '@kit/ui/card';
import { ScrollArea } from '@kit/ui/scroll-area';
import { TikTokCard } from './tiktok-card';
import { XCard } from './x-card';

interface SocialsResearchResultsProps {
  results: any[];
  isVisible: boolean;
  keywords?: string[];
  platform?: string;
}

export function SocialsResearchResults({ 
  results, 
  isVisible, 
  keywords, 
  platform 
}: SocialsResearchResultsProps) {
  if (!isVisible || !results || results.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Summary Header */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Social Media Analysis Results</h3>
        <div className="flex flex-wrap gap-2">
          {keywords && keywords.length > 0 && (
            <Badge variant="outline">
              Keywords: {keywords.join(', ')}
            </Badge>
          )}
          {platform && (
            <Badge variant="secondary">
              Platform: {platform.toUpperCase() === 'X' ? 'X (Twitter)' : platform.toUpperCase()}
            </Badge>
          )}
          <Badge variant="outline">
            Total Results: {results.length}
          </Badge>
        </div>
      </div>

      {/* Results Grid */}
      <ScrollArea className="h-[600px] pr-4">
        <div className="grid gap-4">
          {results.map((result, index) => {
            // Platform-specific rendering
            if (platform === 'tiktok' || result.platform === 'tiktok') {
              return (
                <TikTokCard 
                  key={result.id || index} 
                  data={result} 
                />
              );
            }
            
            if (platform === 'x' || result.platform === 'x') {
              return (
                <XCard 
                  key={result.id || index} 
                  data={result} 
                />
              );
            }
            
            // Fallback for other platforms or unrecognized data
            return (
              <Card key={result.id || index} className="w-full">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="mb-2">
                      {result.platform?.toUpperCase() || 'SOCIAL POST'}
                    </Badge>
                    {result.scraped_at && (
                      <span className="text-xs text-muted-foreground">
                        {new Date(result.scraped_at).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Content Preview */}
                  {(result.text || result.content) && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Content:</h4>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {result.text || result.content}
                      </p>
                    </div>
                  )}

                  {/* URL if available */}
                  {result.webVideoUrl && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">URL:</h4>
                      <a 
                        href={result.webVideoUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:underline break-all"
                      >
                        {result.webVideoUrl}
                      </a>
                    </div>
                  )}

                  {/* Raw Data (for debugging) */}
                  <details className="mt-4">
                    <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                      View Raw Data
                    </summary>
                    <pre className="mt-2 text-xs bg-muted p-2 rounded-md overflow-auto max-h-40">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </details>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
} 