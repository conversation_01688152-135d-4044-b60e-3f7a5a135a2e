'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Badge } from '@kit/ui/badge';
import { Loader2 } from 'lucide-react';
import { toast } from '@kit/ui/sonner';
import { useICPs, usePersonas, useResearchGeneration, type GeneratedResearch } from '../_lib/hooks/use-market-research-data';
import { ICPPersonaSelector } from './shared/icp-persona-selector';

const TIME_FILTERS = [
  'Last 3 months',
  'Last 6 months', 
  'Last 12 months'
];

const RESEARCH_TYPES = [
  { value: 'pain-points', label: 'Pain Points' },
  { value: 'trending-topics', label: 'Trending Topics' },
  { value: 'recent-news', label: 'Recent News' }
];

interface WebResearchFormData {
  icpId: string;
  personaId: string;
  timeFilter: string;
  type: string;
  topic: string;
}

interface WebResearchProps {
  selectedResearch?: GeneratedResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  isGenerating?: boolean;
}

export function WebResearch({ selectedResearch, onResearchSaved, isGenerating }: WebResearchProps) {
  const [pending, startTransition] = useTransition();
  const [formData, setFormData] = useState<WebResearchFormData>({
    icpId: selectedResearch?.icp_id || '',
    personaId: selectedResearch?.persona_id || '',
    timeFilter: selectedResearch?.time_filter || '',
    type: selectedResearch?.research_type || '',
    topic: selectedResearch?.topic || ''
  });

  const { data: icps = [] } = useICPs();
  const { generateWebResearch } = useResearchGeneration();

  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: ''
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handleTimeFilterChange = (value: string) => {
    setFormData(prev => ({ ...prev, timeFilter: value }));
  };

  const handleTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, type: value }));
  };

  const handleTopicChange = (value: string) => {
    setFormData(prev => ({ ...prev, topic: value }));
  };

  const handleSubmit = () => {
    if (!isFormValid) return;

    startTransition(async () => {
      try {
        const selectedICP = icps.find(icp => icp.id === formData.icpId);
        const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);
        
        const research = await generateWebResearch({
          icpId: formData.icpId,
          personaId: formData.personaId,
          timeFilter: formData.timeFilter,
          type: formData.type,
          topic: formData.topic,
          icpName: selectedICP?.name || 'Unknown ICP',
          typeName: selectedType?.label || 'Research',
        });

        onResearchSaved?.(research);
      } catch (error) {
        console.error('Error generating web research:', error);
        toast.error('Failed to generate web research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.icpId && formData.timeFilter && formData.type;

  const selectedICP = icps.find(icp => icp.id === formData.icpId);
  const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);

  return (
    <div className="space-y-6">
      {/* ICP and Persona Selection */}
      <ICPPersonaSelector
        icpId={formData.icpId}
        personaId={formData.personaId}
        onICPChange={handleICPChange}
        onPersonaChange={handlePersonaChange}
      />

      {/* Topic Input */}
      <div className="space-y-2">
        <Label htmlFor="topic-input">Topic</Label>
        <Input
          id="topic-input"
          type="text"
          placeholder="Enter research topic or question about ICP or persona, or leave blank for general insights."
          value={formData.topic}
          onChange={(e) => handleTopicChange(e.target.value)}
          className="w-full"
        />
      </div>

      {/* Research Type */}
      <div className="space-y-3">
        <Label>Type</Label>
        <RadioGroup 
          value={formData.type} 
          onValueChange={handleTypeChange} 
          className="grid grid-cols-1 gap-3"
        >
          {RESEARCH_TYPES.map(type => (
            <div key={type.value} className="flex items-center space-x-2">
              <RadioGroupItem value={type.value} id={type.value} />
              <Label htmlFor={type.value} className="cursor-pointer">
                {type.label}
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>

      {/* Time Filter */}
      <div className="space-y-2">
        <Label htmlFor="time-filter">Time Filter</Label>
        <Select 
          value={formData.timeFilter} 
          onValueChange={handleTimeFilterChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select time period" />
          </SelectTrigger>
          <SelectContent>
            {TIME_FILTERS.map(filter => (
              <SelectItem key={filter} value={filter}>
                {filter}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Submit Button */}
      <Button 
        onClick={handleSubmit}
        disabled={!isFormValid || pending || isGenerating}
        className="w-full"
        size="lg"
      >
        {pending || isGenerating ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Generating Web Research...
          </div>
        ) : (
          'Generate Web Research'
        )}
      </Button>

      {/* Selected Configuration Summary */}
      {(selectedICP || selectedType) && (
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <h4 className="font-medium text-sm">Research Configuration:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedICP && (
              <Badge variant="outline">ICP: {selectedICP.name}</Badge>
            )}
            {selectedType && (
              <Badge variant="outline">Type: {selectedType.label}</Badge>
            )}
            {formData.timeFilter && (
              <Badge variant="outline">Period: {formData.timeFilter}</Badge>
            )}
            {formData.topic && (
              <Badge variant="outline">Topic: {formData.topic}</Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

