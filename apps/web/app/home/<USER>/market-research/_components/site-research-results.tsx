'use client';

import { Card, CardContent } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { ExternalLink, FileText } from 'lucide-react';

interface SiteResearchResultsProps {
  results: Record<string, any>[];
  isVisible?: boolean;
}

export function SiteResearchResults({ results, isVisible = false }: SiteResearchResultsProps) {
  // Only show if explicitly marked as visible (when site research is selected)
  if (!isVisible) {
    return null;
  }

  // Show empty state if no results but component should be visible
  if (results.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Site Analysis Results</h3>
          <Badge variant="outline">0 records</Badge>
        </div>
        
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h4 className="text-lg font-medium mb-2">No Results Yet</h4>
            <p className="text-sm text-muted-foreground">
              This site analysis has not generated any results yet. If it&apos;s still generating, results will appear here automatically.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Site Analysis Results</h3>
        <Badge variant="outline">{results.length} records</Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {results.map((result, index) => (
          <Card key={index} className="h-full">
            <CardContent className="p-4 space-y-3">
              <div className="space-y-2">
                <h4 className="font-bold text-sm leading-tight">Record {index + 1}</h4>
                <Badge variant="secondary" className="text-xs">
                  {Object.keys(result).length} fields
                </Badge>
              </div>
              
              <div className="space-y-2">
                {Object.entries(result).map(([key, value]) => (
                  <div key={key} className="space-y-1">
                    <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {key}
                    </p>
                    <p className="text-sm text-foreground leading-relaxed">
                      {typeof value === 'string' && value.startsWith('http') ? (
                        <a 
                          href={value} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-primary hover:underline inline-flex items-center gap-1"
                        >
                          {value}
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      ) : (
                        String(value)
                      )}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
} 