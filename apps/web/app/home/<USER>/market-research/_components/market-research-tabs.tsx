'use client';

import { useState, useEffect } from 'react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { MarketResearchForm } from './market-research-form';
import { SavedResearch } from './saved-research';
import { ResearchSidebar } from './research-sidebar';
import { type GeneratedResearch, type SiteResearch, type SocialsResearch } from '../_lib/hooks/use-market-research-data';

export function MarketResearchTabs() {
  const [activeTab, setActiveTab] = useState('generate');
  const [selectedResearch, setSelectedResearch] = useState<GeneratedResearch | null>(null);
  const [selectedSiteResearch, setSelectedSiteResearch] = useState<SiteResearch | null>(null);
  const [selectedSocialsResearch, setSelectedSocialsResearch] = useState<SocialsResearch | null>(null);

  const handleSelectResearch = (research: GeneratedResearch | null) => {
    setSelectedResearch(research);
    setSelectedSiteResearch(null);
    setSelectedSocialsResearch(null);
    if (research) {
      setActiveTab('generate');
    }
  };

  const handleSelectSiteResearch = (siteResearch: SiteResearch | null) => {
    setSelectedSiteResearch(siteResearch);
    setSelectedResearch(null);
    setSelectedSocialsResearch(null);
    if (siteResearch) {
      setActiveTab('generate');
    }
  };

  const handleSelectSocialsResearch = (socialsResearch: SocialsResearch | null) => {
    setSelectedSocialsResearch(socialsResearch);
    setSelectedResearch(null);
    setSelectedSiteResearch(null);
    if (socialsResearch) {
      setActiveTab('generate');
    }
  };

  const handleNewResearch = () => {
    setSelectedResearch(null);
    setSelectedSiteResearch(null);
    setSelectedSocialsResearch(null);
    setActiveTab('generate');
  };

  return (
    <div className="">
    
      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full flex flex-col">
          <div className="border-b px-6 py-4">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="generate">
                <Trans i18nKey="marketResearch:generateTab" defaults="Generate Research" />
              </TabsTrigger>
              <TabsTrigger value="saved">
                <Trans i18nKey="marketResearch:savedTab" defaults="Saved Research" />
              </TabsTrigger>
            </TabsList>
          </div>
          
          <div className="flex-1 overflow-auto">
            <TabsContent value="generate" className="mt-0 h-full">
              <div className="flex flex h-[calc(100vh-200px)] w-full">
                <div>
              {/* Sidebar - only show on generate tab */}
              {activeTab === 'generate' && (
                    <ResearchSidebar
                      selectedResearchId={selectedResearch?.id}
                      selectedSiteResearchId={selectedSiteResearch?.id}
                      selectedSocialsResearchId={selectedSocialsResearch?.id}
                      onSelectResearch={handleSelectResearch}
                      onSelectSiteResearch={handleSelectSiteResearch}
                      onSelectSocialsResearch={handleSelectSocialsResearch}
                      onNewResearch={handleNewResearch}
                    />
                  )}
                </div>
                <div className='p-6 w-full h-full'>
                <MarketResearchForm 
                  selectedResearch={selectedResearch}
                  selectedSiteResearch={selectedSiteResearch}
                  selectedSocialsResearch={selectedSocialsResearch}
                  onResearchSaved={handleSelectResearch}
                  onSiteResearchSaved={handleSelectSiteResearch}
                  onSocialsResearchSaved={handleSelectSocialsResearch}
                />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="saved" className="mt-0 h-full">
              <div className="p-6">
                <SavedResearch />
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
} 