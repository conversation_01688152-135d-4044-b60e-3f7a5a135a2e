'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';
import { ExternalLink, Star } from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { If } from '@kit/ui/if';
import { useZero } from '~/hooks/use-zero';

interface ResearchResult {
  title: string;
  description: string;
  source: string;
  source_url: string;
  relevance_score: number;
}

interface ResearchResultsProps {
  results: ResearchResult[];
  formData: {
    icpId: string;
    personaId: string;
    type: string;
    timeFilter: string;
    topic: string;
  };
  accountId: string;
}

export function ResearchResults({ results, formData, accountId }: ResearchResultsProps) {
  const [pending, startTransition] = useTransition();
  const [savePending, setSavePending] = useState<string | null>(null);
  const [savedItems, setSavedItems] = useState<Set<string>>(new Set());
  const zero = useZero();

  // Generate a unique key for each research item
  const getResearchKey = (result: ResearchResult) => {
    return `${formData.icpId}-${formData.personaId === 'no-persona' ? 'no-persona' : formData.personaId}-${result.title}-${formData.type}`;
  };

  // Helper function to parse comma-separated sources and URLs
  const parseSourcesAndUrls = (sources: string | null | undefined, urls: string | null | undefined) => {
    // Handle null/undefined cases by defaulting to empty strings
    const sourceArray = (sources || '').split(',').map(s => s.trim()).filter(Boolean);
    const urlArray = (urls || '').split(',').map(u => u.trim()).filter(Boolean);
    
    // Pair sources with URLs, handling cases where counts might not match
    return sourceArray.map((source, index) => ({
      source,
      url: urlArray[index] || undefined
    }));
  };

  const handleSaveToggle = (result: ResearchResult) => {
    const key = getResearchKey(result);
    const isSaved = savedItems.has(key);
    
    setSavePending(key);

    startTransition(async () => {
      try {
        if (isSaved) {
          setSavedItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(key);
            return newSet;
          });

          toast.success('Research item removed from saved items');
        } else {
          if (!zero) return;

    
          zero.mutate.saved_research.insert({
            id: crypto.randomUUID(),
            account_id: accountId,
            icp_id: formData.icpId,
            persona_id: formData.personaId == '' ? null : formData.personaId,
            research_type: formData.type as 'pain-points' | 'trending-topics' | 'recent-news',
            time_filter: formData.timeFilter,
            title: result.title,
            topic: formData.topic || '',
            description: result.description,
            source: result.source,
            source_url: result.source_url,
          });

          setSavedItems(prev => new Set(prev).add(key));
          toast.success('Research item saved successfully!');
        }
      } catch (error) {
        console.error('Error toggling save state:', error);
        toast.error('Failed to update saved state', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      } finally {
        setSavePending(null);
      }
    });
  };

  if (results.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Research Results</h3>
        <Badge variant="outline">{results.length} insights</Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {results.map((result, index) => {
          const key = getResearchKey(result);
          console.log('key', key);
          const isSaved = savedItems.has(key);
          const isPending = savePending === key;
          
          return (
            <Card key={index} className="h-full relative">
              {/* Star button in top right corner */}
              <button
                onClick={() => handleSaveToggle(result)}
                disabled={isPending || !formData.icpId}
                className={cn(
                  "absolute top-3 right-3 p-1.5 rounded-full transition-all duration-200",
                  "hover:bg-muted focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
                title={isSaved ? "Remove from saved" : "Save research item"}
              >
                <Star 
                  className={cn(
                    "h-4 w-4 transition-colors duration-200",
                    isSaved 
                      ? "fill-yellow-400 text-yellow-400" 
                      : "text-muted-foreground hover:text-foreground",
                    isPending && "animate-pulse"
                  )} 
                />
              </button>

              <CardContent className="p-4 space-y-3 pr-12">
                <div className="space-y-2">
                  <h4 className="font-bold text-sm leading-tight">{result.title}</h4>
                  <Badge 
                    variant={result.relevance_score >= 8 ? 'default' : result.relevance_score >= 6 ? 'secondary' : 'outline'}
                    className="text-xs"
                  >
                    Score: {result.relevance_score}/10
                  </Badge>
                </div>
                
                <p className="text-sm text-foreground leading-relaxed">
                  {result.description}
                </p>
                
                {/* Sources */}
                <div className="text-xs text-muted-foreground mt-auto space-y-1">
                  <div className="font-medium text-muted-foreground/80 mb-1">Sources</div>
                  {parseSourcesAndUrls(result.source, result.source_url).map((sourceItem, sourceIndex) => (
                    <div key={sourceIndex} className="flex items-center gap-2">
                      <If condition={!!sourceItem.url} fallback={
                        <span>{sourceItem.source}</span>
                      }>
                        <a 
                          href={sourceItem.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-primary hover:underline"
                        >
                          {sourceItem.source}
                        </a>
                        <Button
                          variant="ghost"
                          size="sm"
                          asChild
                          className="h-4 w-4 p-0"
                        >
                          <a 
                            href={sourceItem.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            title="Open source"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        </Button>
                      </If>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
} 