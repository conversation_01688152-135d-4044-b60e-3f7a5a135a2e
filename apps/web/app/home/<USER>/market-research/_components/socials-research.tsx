'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Checkbox } from '@kit/ui/checkbox';
import { Badge } from '@kit/ui/badge';
import { Separator } from '@kit/ui/separator';
import { Loader2, Plus, X } from 'lucide-react';
import { toast } from '@kit/ui/sonner';
import { useSocialsResearchGeneration, type SocialsResearch } from '../_lib/hooks/use-market-research-data';

interface SocialResearchFormData {
  keywords: string[];
  platform: string;
  // Twitter/X specific options
  onlyTwitterBlue: boolean;
  onlyVerifiedUsers: boolean;
  minimumFavorites: number;
  // TikTok specific options
  maxProfilesPerQuery: number;
}

const PLATFORMS = [
  { value: 'tiktok', label: 'TikTok' },
  { value: 'x', label: 'X (formerly Twitter)' },
];

interface SocialResearchProps {
  selectedSocialsResearch?: SocialsResearch | null;
  onSocialsResearchSaved?: (research: SocialsResearch) => void;
  isGenerating?: boolean;
}

export function SocialResearch({ selectedSocialsResearch, onSocialsResearchSaved, isGenerating }: SocialResearchProps) {
  const [pending, startTransition] = useTransition();
  const [newKeyword, setNewKeyword] = useState('');
  const [formData, setFormData] = useState<SocialResearchFormData>({
    keywords: [],
    platform: 'tiktok', // Default to TikTok
    // Twitter/X defaults
    onlyTwitterBlue: false,
    onlyVerifiedUsers: false,
    minimumFavorites: 0,
    // TikTok defaults
    maxProfilesPerQuery: 5,
  });

  const { generateSocialsResearch } = useSocialsResearchGeneration();

  const handleAddKeyword = () => {
    if (newKeyword.trim() && !formData.keywords.includes(newKeyword.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, newKeyword.trim()]
      }));
      setNewKeyword('');
    }
  };

  const handleRemoveKeyword = (keywordToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter(keyword => keyword !== keywordToRemove)
    }));
  };

  const handleSubmit = () => {
    if (!isFormValid) return;

    startTransition(async () => {
      try {        
        const research = await generateSocialsResearch({
          keywords: formData.keywords,
          platform: formData.platform,
          // Include platform-specific options
          ...(formData.platform === 'x' && {
            onlyTwitterBlue: formData.onlyTwitterBlue,
            onlyVerifiedUsers: formData.onlyVerifiedUsers,
            minimumFavorites: formData.minimumFavorites,
          }),
          ...(formData.platform === 'tiktok' && {
            maxProfilesPerQuery: formData.maxProfilesPerQuery,
          }),
        });

        onSocialsResearchSaved?.(research);
      } catch (error) {
        console.error('Error generating social research:', error);
        toast.error('Failed to generate social research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.keywords.length > 0 && formData.platform;

  const selectedPlatform = PLATFORMS.find(p => p.value === formData.platform);

  return (
    <div className="space-y-6">
      {/* Keywords Input */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="keyword-input">Keywords</Label>
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={handleAddKeyword}
            disabled={!newKeyword.trim()}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">Add keywords one at a time to search across social media platforms</p>
        <Input
          id="keyword-input"
          type="text"
          placeholder="Enter a keyword (e.g., 'AI tools', 'marketing')"
          value={newKeyword}
          onChange={(e) => setNewKeyword(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleAddKeyword();
            }
          }}
          className="w-full"
        />
        
        {/* Display added keywords */}
        {formData.keywords.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Added Keywords:</Label>
            <div className="flex flex-wrap gap-2">
              {formData.keywords.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {keyword}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 w-4 h-4"
                    onClick={() => handleRemoveKeyword(keyword)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Platform Selection */}
      <div className="space-y-3">
        <Label>Platform</Label>
        <Select value={formData.platform} onValueChange={(value) => setFormData(prev => ({ ...prev, platform: value }))}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a social media platform" />
          </SelectTrigger>
          <SelectContent>
            {PLATFORMS.map((platform) => (
              <SelectItem key={platform.value} value={platform.value}>
                {platform.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">More platforms coming soon!</p>
      </div>

      {/* Platform-Specific Advanced Options */}
      {formData.platform && (
        <>
          <Separator />
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">Advanced Options</Label>
              <Badge variant="outline" className="text-xs">
                {selectedPlatform?.label}
              </Badge>
            </div>

            {/* Twitter/X Specific Options */}
            {formData.platform === 'x' && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Twitter Blue Filter */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="twitter-blue"
                      checked={formData.onlyTwitterBlue}
                      onCheckedChange={(checked) => 
                        setFormData(prev => ({ ...prev, onlyTwitterBlue: checked as boolean }))
                      }
                    />
                    <div className="grid gap-1.5 leading-none">
                      <Label htmlFor="twitter-blue" className="text-sm cursor-pointer">
                        Twitter Blue Only
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Only show content from Twitter Blue subscribers
                      </p>
                    </div>
                  </div>

                  {/* Verified Users Filter */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="verified-users"
                      checked={formData.onlyVerifiedUsers}
                      onCheckedChange={(checked) => 
                        setFormData(prev => ({ ...prev, onlyVerifiedUsers: checked as boolean }))
                      }
                    />
                    <div className="grid gap-1.5 leading-none">
                      <Label htmlFor="verified-users" className="text-sm cursor-pointer">
                        Verified Users Only
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Only show content from verified accounts
                      </p>
                    </div>
                  </div>
                </div>

                {/* Minimum Favorites */}
                <div className="space-y-2">
                  <Label htmlFor="min-favorites" className="text-sm">
                    Minimum Favorites (Likes)
                  </Label>
                  <Input
                    id="min-favorites"
                    type="number"
                    min="0"
                    value={formData.minimumFavorites}
                    onChange={(e) => 
                      setFormData(prev => ({ 
                        ...prev, 
                        minimumFavorites: Math.max(0, parseInt(e.target.value) || 0)
                      }))
                    }
                    placeholder="0"
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Filter posts with at least this many likes
                  </p>
                </div>
              </div>
            )}

            {/* TikTok Specific Options */}
            {formData.platform === 'tiktok' && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                <div className="space-y-2">
                  <Label htmlFor="max-profiles" className="text-sm">
                    Max Profiles Per Query
                  </Label>
                  <Input
                    id="max-profiles"
                    type="number"
                    min="1"
                    max="20"
                    value={formData.maxProfilesPerQuery}
                    onChange={(e) => 
                      setFormData(prev => ({ 
                        ...prev, 
                        maxProfilesPerQuery: Math.min(20, Math.max(1, parseInt(e.target.value) || 5))
                      }))
                    }
                    placeholder="5"
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Number of profiles to scrape per keyword (1-20)
                  </p>
                </div>
              </div>
            )}
          </div>
        </>
      )}

      {/* Submit Button */}
      <Button 
        onClick={handleSubmit}
        disabled={!isFormValid || pending || isGenerating}
        className="w-full"
        size="lg"
      >
        {pending || isGenerating ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Analyzing {selectedPlatform?.label} Content...
          </div>
        ) : (
          `Start ${selectedPlatform?.label} Analysis`
        )}
      </Button>

      {/* Selected Configuration Summary */}
      {formData.keywords.length > 0 && formData.platform && (
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <h4 className="font-medium text-sm">Analysis Configuration:</h4>
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline">Keywords: {formData.keywords.length}</Badge>
            <Badge variant="outline">Platform: {selectedPlatform?.label}</Badge>
            
            {/* Platform-specific summary badges */}
            {formData.platform === 'x' && (
              <>
                {formData.onlyTwitterBlue && (
                  <Badge variant="outline" className="text-blue-600">Twitter Blue Only</Badge>
                )}
                {formData.onlyVerifiedUsers && (
                  <Badge variant="outline" className="text-green-600">Verified Only</Badge>
                )}
                {formData.minimumFavorites > 0 && (
                  <Badge variant="outline">Min Likes: {formData.minimumFavorites}</Badge>
                )}
              </>
            )}
            
            {formData.platform === 'tiktok' && (
              <Badge variant="outline">Max Profiles: {formData.maxProfilesPerQuery}</Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

