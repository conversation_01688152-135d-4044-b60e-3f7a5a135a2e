'use client';

import { Label } from '@kit/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';

export const SEARCH_TYPES = [
  { value: 'web-search', label: 'Web Search', description: 'Search across the web for insights' },
  { value: 'social-media-search', label: 'Social Media Search', description: 'Search social media platforms' },
  { value: 'search-by-url', label: 'Search By URL', description: 'Analyze specific websites' }
];

interface ResearchTypeSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
}

export function ResearchTypeSelector({ value, onValueChange, disabled = false }: ResearchTypeSelectorProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="search-type">Research Method</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger>
          <SelectValue placeholder="Select research method" />
        </SelectTrigger>
        <SelectContent>
          {SEARCH_TYPES.map(type => (
            <SelectItem key={type.value} value={type.value}>
              <div className="flex flex-col">
                <span>{type.label}</span>
                <span className="text-xs text-muted-foreground">{type.description}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
} 