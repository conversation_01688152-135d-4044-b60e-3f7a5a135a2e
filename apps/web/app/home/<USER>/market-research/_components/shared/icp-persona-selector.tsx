'use client';

import { Label } from '@kit/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { useICPs, usePersonas, type ICP, type Persona } from '../../_lib/hooks/use-market-research-data';

interface ICPPersonaSelectorProps {
  icpId: string;
  personaId: string;
  onICPChange: (value: string) => void;
  onPersonaChange: (value: string) => void;
  allowNoPersona?: boolean;
}

export function ICPPersonaSelector({ 
  icpId, 
  personaId, 
  onICPChange, 
  onPersonaChange, 
  allowNoPersona = true 
}: ICPPersonaSelectorProps) {
  const { data: icps = [], isLoading: icpsLoading } = useICPs();
  const { data: personas = [], isLoading: personasLoading } = usePersonas(icpId);

  const handleICPChange = (value: string) => {
    onICPChange(value);
    // Reset persona when ICP changes
    onPersonaChange('');
  };

  return (
    <div className="space-y-4">
      {/* ICP Selection */}
      <div className="space-y-2">
        <Label htmlFor="icp-select">Ideal Customer Profile (ICP)</Label>
        <Select 
          value={icpId} 
          onValueChange={handleICPChange} 
          disabled={icpsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder={icpsLoading ? "Loading ICPs..." : "Select an ICP"} />
          </SelectTrigger>
          <SelectContent>
            {icps.map((icp: ICP) => (
              <SelectItem key={icp.id} value={icp.id}>
                {icp.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Persona Selection */}
      <div className="space-y-2">
        <Label htmlFor="persona-select">
          Persona {allowNoPersona && <span className="text-muted-foreground">(Optional)</span>}
        </Label>
        <Select 
          value={personaId} 
          onValueChange={onPersonaChange} 
          disabled={personasLoading || !icpId}
        >
          <SelectTrigger>
            <SelectValue 
              placeholder={
                !icpId 
                  ? "Select an ICP first" 
                  : personasLoading 
                    ? "Loading personas..." 
                    : "Select a persona"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            {allowNoPersona && (
              <SelectItem value="no-persona">
                <span className="text-muted-foreground">No specific persona</span>
              </SelectItem>
            )}
            {personas.map((persona: Persona) => (
              <SelectItem key={persona.id} value={persona.id}>
                <div className="flex flex-col">
                  <span>{persona.name}</span>
                  <span className="text-xs text-muted-foreground">{persona.role}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
} 