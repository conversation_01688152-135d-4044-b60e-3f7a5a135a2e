'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Checkbox } from '@kit/ui/checkbox';
import { Badge } from '@kit/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@kit/ui/accordion';
import { toast } from '@kit/ui/sonner';
import { Loader2, Plus, X } from 'lucide-react';
import { useICPs, useResearchGeneration, type GeneratedResearch, type SiteResearch } from '../_lib/hooks/use-market-research-data';
import { ICPPersonaSelector } from './shared/icp-persona-selector';

const FIELD_TYPES = [
  { value: 'text', label: 'Text' },
  { value: 'number', label: 'Number' },
  { value: 'boolean', label: 'True/False' }
];

interface FieldToFind {
  name: string;
  type: 'text' | 'number' | 'boolean';
}

interface SiteSearchFormData {
  icpId: string;
  personaId: string;
  urls: string[];
  fieldsToFind: FieldToFind[];
  instructions: string;
  allowWebSearch: boolean;
  agentMode: boolean;
}

interface SiteSearchProps {
  selectedResearch?: GeneratedResearch | null;
  selectedSiteResearch?: SiteResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  onSiteResearchSaved?: (research: SiteResearch) => void;
  isGenerating?: boolean;
}

// Utility function to convert text to snake_case
function toSnakeCase(text: string): string {
  return text
    .trim()
    .toLowerCase()
    .replace(/\s+/g, '_')
    .replace(/[^a-z0-9_]/g, '')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');
}

export function SiteSearch({ selectedResearch, selectedSiteResearch, onResearchSaved, onSiteResearchSaved, isGenerating }: SiteSearchProps) {
  const [pending, startTransition] = useTransition();
  const [newUrl, setNewUrl] = useState('');
  const [formData, setFormData] = useState<SiteSearchFormData>({
    icpId: selectedResearch?.icp_id || '',
    personaId: selectedResearch?.persona_id || '',
    urls: [],
    fieldsToFind: [{ name: '', type: 'text' }],
    instructions: '',
    allowWebSearch: false,
    agentMode: false
  });

  const { data: icps = [] } = useICPs();
  const { generateSiteResearch } = useResearchGeneration();

  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: ''
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handleAddUrl = () => {
    if (newUrl.trim()) {
      setFormData(prev => ({
        ...prev,
        urls: [...prev.urls, newUrl.trim()]
      }));
      setNewUrl('');
    }
  };

  const handleRemoveUrl = (index: number) => {
    setFormData(prev => ({
      ...prev,
      urls: prev.urls.filter((_, i) => i !== index)
    }));
  };

  const handleFieldNameChange = (index: number, name: string) => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: prev.fieldsToFind.map((field, i) => 
        i === index ? { ...field, name } : field
      )
    }));
  };

  const handleFieldTypeChange = (index: number, type: 'text' | 'number' | 'boolean') => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: prev.fieldsToFind.map((field, i) => 
        i === index ? { ...field, type } : field
      )
    }));
  };

  const handleAddField = () => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: [...prev.fieldsToFind, { name: '', type: 'text' }]
    }));
  };

  const handleRemoveField = (index: number) => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: prev.fieldsToFind.filter((_, i) => i !== index)
    }));
  };

  const handleInstructionsChange = (value: string) => {
    setFormData(prev => ({ ...prev, instructions: value }));
  };

  const handleAllowWebSearchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, allowWebSearch: checked }));
  };

  const handleAgentModeChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, agentMode: checked }));
  };

  const handleSubmit = () => {
    if (!isFormValid) return;

    startTransition(async () => {
      try {
        // Transform form data to expected API format
        const properties: Record<string, any> = {};
        const required: string[] = [];
        
        // Convert fieldsToFind to schema format
        formData.fieldsToFind.forEach(field => {
          if (field.name.trim()) {
            let jsonSchemaType: string;
            switch (field.type) {
              case 'text':
                jsonSchemaType = 'string';
                break;
              case 'number':
                jsonSchemaType = 'number';
                break;
              case 'boolean':
                jsonSchemaType = 'boolean';
                break;
              default:
                jsonSchemaType = 'string';
            }
            
            // Convert field name to snake_case
            const snakeCaseFieldName = toSnakeCase(field.name);
            
            properties[snakeCaseFieldName] = {
              type: jsonSchemaType
            };
            required.push(snakeCaseFieldName);
          }
        });

        // Create proper JSON Schema structure
        const schema = {
          type: "object",
          properties: {
            results: {
              type: "array",
              items: {
                type: "object",
                properties: properties,
                required: required
              }
            }
          },
          required: ["results"]
        };

        const siteResearch = await generateSiteResearch({
          icpId: formData.icpId,
          personaId: formData.personaId,
          urls: formData.urls,
          instructions: formData.instructions,
          schema: schema,
          allowWebSearch: formData.allowWebSearch,
          agentMode: formData.agentMode,
        });

        onSiteResearchSaved?.(siteResearch);

      } catch (error) {
        console.error('Error generating site research:', error);
        toast.error('Failed to generate site research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.urls.length > 0 && formData.fieldsToFind.some(field => field.name.trim());

  const selectedICP = icps.find(icp => icp.id === formData.icpId);

  return (
    <div className="space-y-6">
      {/* URLs to Analyze */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="url-input">URLs to Analyze</Label>
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={handleAddUrl}
            disabled={!newUrl.trim()}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">Enter URLs one at a time to analyze and extract information from</p>
        <Input
          id="url-input"
          type="url"
          placeholder="https://example.com"
          value={newUrl}
          onChange={(e) => setNewUrl(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleAddUrl();
            }
          }}
        />
        
        {/* Display added URLs */}
        {formData.urls.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Added URLs:</Label>
            <div className="space-y-1">
              {formData.urls.map((url, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-muted rounded-md">
                  <span className="text-sm truncate">{url}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveUrl(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Fields to Find */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Fields to Find</Label>
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={handleAddField}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Field
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">Define the specific data fields you want to extract from each URL</p>
        
        <div className="space-y-3">
          {formData.fieldsToFind.map((field, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="flex-1">
                  <Input
                    placeholder="Field name (e.g., Company Name)"
                    value={field.name}
                    onChange={(e) => handleFieldNameChange(index, e.target.value)}
                  />
                </div>
                <div className="w-32">
                  <Select
                    value={field.type}
                    onValueChange={(value) => handleFieldTypeChange(index, value as 'text' | 'number' | 'boolean')}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {FIELD_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                {formData.fieldsToFind.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveField(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              {/* Show snake_case preview */}
              {field.name.trim() && (
                <div className="ml-0 text-xs text-muted-foreground">
                  API field name: <code className="bg-muted px-1 py-0.5 rounded">{toSnakeCase(field.name)}</code>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="space-y-2">
        <Label htmlFor="instructions">Instructions</Label>
        <p className="text-xs text-muted-foreground">Provide specific instructions for how to analyze the websites and extract the data</p>
        <textarea
          id="instructions"
          placeholder="Enter any specific instructions for the analysis..."
          value={formData.instructions}
          onChange={(e) => handleInstructionsChange(e.target.value)}
          className="w-full min-h-[80px] p-3 border border-input rounded-md bg-background text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          rows={3}
        />
      </div>

      {/* Allow Web Search */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="allow-web-search"
            checked={formData.allowWebSearch}
            onCheckedChange={(checked) => handleAllowWebSearchChange(checked as boolean)}
          />
          <Label htmlFor="allow-web-search" className="cursor-pointer">
            Allow Web Search
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">Enable additional web searches to gather more context and information</p>
      </div>

      {/* Agent Mode */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="agent-mode"
            checked={formData.agentMode}
            onCheckedChange={(checked) => handleAgentModeChange(checked as boolean)}
          />
          <Label htmlFor="agent-mode" className="cursor-pointer">
            Agent Mode
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">Enable advanced AI agent capabilities for more thorough analysis</p>
      </div>

      {/* Additional Fields Accordion */}
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="additional-fields">
          <AccordionTrigger className="text-sm font-medium">
            Additional Fields (Optional)
          </AccordionTrigger>
          <AccordionContent>
            <div className="pt-2">
              <p className="text-xs text-muted-foreground mb-4">
                Optionally select an ICP and persona to provide additional context for the analysis
              </p>
              <ICPPersonaSelector
                icpId={formData.icpId}
                personaId={formData.personaId}
                onICPChange={handleICPChange}
                onPersonaChange={handlePersonaChange}
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Submit Button */}
      <Button 
        onClick={handleSubmit}
        disabled={!isFormValid || pending || isGenerating}
        className="w-full"
        size="lg"
      >
        {pending || isGenerating ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Analyzing Sites...
          </div>
        ) : (
          'Start Site Analysis'
        )}  
      </Button>

      {/* Selected Configuration Summary */}
      {(selectedICP || formData.urls.length > 0) && (
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <h4 className="font-medium text-sm">Analysis Configuration:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedICP && (
              <Badge variant="outline">ICP: {selectedICP.name}</Badge>
            )}
            {formData.urls.length > 0 && (
              <Badge variant="outline">URLs: {formData.urls.length}</Badge>
            )}
            {formData.fieldsToFind.filter(field => field.name.trim()).length > 0 && (
              <Badge variant="outline">Fields: {formData.fieldsToFind.filter(field => field.name.trim()).length}</Badge>
            )}
            {formData.allowWebSearch && (
              <Badge variant="outline">Web Search: Enabled</Badge>
            )}
            {formData.agentMode && (
              <Badge variant="outline">Agent Mode: Enabled</Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

