'use client';

import { useState, useTransition } from 'react';
import { Card, CardContent } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';
import { Star } from 'lucide-react';
import { cn } from '@kit/ui/utils';

interface ContentSuggestion {
  topic: string;
  description: string;
  content_type: string;
  target_audience: string;
}

interface ContentSuggestionsProps {
  suggestions: ContentSuggestion[];
  formData: {
    icpId: string;
    personaId: string;
    type: string;
    timeFilter: string;
    topic: string;
  };
}

export function ContentSuggestions({ suggestions, formData }: ContentSuggestionsProps) {
  const [pending, startTransition] = useTransition();
  const [savePending, setSavePending] = useState<string | null>(null);
  const [savedSuggestions, setSavedSuggestions] = useState<Set<string>>(new Set());

  // Generate a unique key for each content suggestion
  const getSuggestionKey = (suggestion: ContentSuggestion) => {
    return `${formData.icpId}-${formData.personaId === 'no-persona' ? 'no-persona' : formData.personaId}-${suggestion.topic}-suggestion`;
  };

  const handleSaveSuggestionToggle = (suggestion: ContentSuggestion) => {
    const key = getSuggestionKey(suggestion);
    const isSaved = savedSuggestions.has(key);
    
    setSavePending(key);

    startTransition(async () => {
      try {
        if (isSaved) {
          // Remove from saved suggestions
          setSavedSuggestions(prev => {
            const newSet = new Set(prev);
            newSet.delete(key);
            return newSet;
          });
          toast.success('Content suggestion removed from saved items');
        } else {
          // Save suggestion
          setSavedSuggestions(prev => new Set(prev).add(key));
          toast.success('Content suggestion saved successfully!');
        }
      } catch (error) {
        console.error('Error toggling save state:', error);
        toast.error('Failed to update saved state', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      } finally {
        setSavePending(null);
      }
    });
  };

  if (suggestions.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Content Suggestions</h3>
        <Badge variant="outline">{suggestions.length} suggestions</Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {suggestions.map((suggestion, index) => {
          const key = getSuggestionKey(suggestion);
          const isSaved = savedSuggestions.has(key);
          const isPending = savePending === key;
          
          return (
            <Card key={index} className="h-full relative">
              {/* Star button in top right corner */}
              <button
                onClick={() => handleSaveSuggestionToggle(suggestion)}
                disabled={isPending || !formData.icpId}
                className={cn(
                  "absolute top-3 right-3 p-1.5 rounded-full transition-all duration-200",
                  "hover:bg-muted focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
                title={isSaved ? "Remove from saved" : "Save content suggestion"}
              >
                <Star 
                  className={cn(
                    "h-4 w-4 transition-colors duration-200",
                    isSaved 
                      ? "fill-yellow-400 text-yellow-400" 
                      : "text-muted-foreground hover:text-foreground",
                    isPending && "animate-pulse"
                  )} 
                />
              </button>

              <CardContent className="p-4 space-y-3 pr-12">
                <div className="space-y-2">
                  <h4 className="font-bold text-sm leading-tight">{suggestion.topic}</h4>
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="secondary" className="text-xs">
                      {suggestion.content_type}
                    </Badge>
                  </div>
                </div>
                
                <p className="text-sm text-foreground leading-relaxed">
                  {suggestion.description}
                </p>
                
                <div className="mt-auto pt-2">
                  <p className="text-xs text-muted-foreground">
                    <span className="font-medium">Target:</span> {suggestion.target_audience}
                  </p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
} 