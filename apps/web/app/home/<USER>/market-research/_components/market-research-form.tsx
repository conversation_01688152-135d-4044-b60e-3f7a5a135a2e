'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Loader2 } from 'lucide-react';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { toast } from '@kit/ui/sonner';

import { If } from '@kit/ui/if';
import { WebResearch } from './web-research';
import { SocialResearch } from './socials-research';
import { SiteSearch } from './site-search';
import { ResearchResults } from './research-results';
import { SiteResearchResults } from './site-research-results';
import { SocialsResearchResults } from './socials-research-results';
import { ResearchTypeSelector } from './shared/research-type-selector';
import { type GeneratedResearch, type SiteResearch, type SocialsResearch } from '../_lib/hooks/use-market-research-data';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface ResearchResult {
  title: string;
  description: string;
  source: string;
  source_url: string;
  relevance_score: number;
}

interface ContentSuggestion {
  topic: string;
  description: string;
  content_type: string;
  target_audience: string;
}

interface MarketResearchFormProps {
  selectedResearch?: GeneratedResearch | null;
  selectedSiteResearch?: SiteResearch | null;
  selectedSocialsResearch?: SocialsResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  onSiteResearchSaved?: (research: SiteResearch) => void;
  onSocialsResearchSaved?: (research: SocialsResearch) => void;
}

export function MarketResearchForm({ 
  selectedResearch, 
  selectedSiteResearch, 
  selectedSocialsResearch,
  onResearchSaved, 
  onSiteResearchSaved,
  onSocialsResearchSaved
}: MarketResearchFormProps) {
  const [results, setResults] = useState<ResearchResult[]>([]);
  const [contentSuggestions, setContentSuggestions] = useState<ContentSuggestion[]>([]);
  const [siteResults, setSiteResults] = useState<Record<string, any>[]>([]);
  const [socialsResults, setSocialsResults] = useState<any[]>([]);
  const [currentResearchId, setCurrentResearchId] = useState<string | null>(null);
  const [currentSiteResearchId, setCurrentSiteResearchId] = useState<string | null>(null);
  const [currentSocialsResearchId, setCurrentSocialsResearchId] = useState<string | null>(null);
  const [searchType, setSearchType] = useState<string>('web-search');

  const zero = useZero();
  const workspace = useTeamAccountWorkspace();



  // Efficient - filters at database level
  const [allResearch] = useZeroQuery(zero.query.generated_research
    .where('account_id', '=', workspace.account.id)
    .where('id', '=', currentResearchId || ''),
    { ttl: '1d' });
  const liveResearch = currentResearchId 
    ? allResearch.find(r => r.id === currentResearchId)
    : null;


  const [allSiteResearch] = useZeroQuery(zero.query.site_research
    .where('company_id', '=', workspace.account.id),
    { ttl: '1d' });
  const liveSiteResearch = currentSiteResearchId 
    ? allSiteResearch.find(r => r.id === currentSiteResearchId)
    : null;

  const [allSocialsResearch] = useZeroQuery(zero.query.socials_research
    .where('company_id', '=', workspace.account.id),
    { ttl: '1d' });
  const liveSocialsResearch = currentSocialsResearchId 
    ? allSocialsResearch.find(r => r.id === currentSocialsResearchId)
    : null;

  // Update results when live research data changes
  useEffect(() => {
    if (liveResearch && liveResearch.id === currentResearchId) {
      
      // Update results and content suggestions from the live data
      if (liveResearch.results && Array.isArray(liveResearch.results)) {
        setResults(liveResearch.results as ResearchResult[]);
      }
      
      if (liveResearch.content_suggestions && Array.isArray(liveResearch.content_suggestions)) {
        setContentSuggestions(liveResearch.content_suggestions as ContentSuggestion[]);
      }

      // If research is no longer generating, show success message
      if (liveResearch.is_generating === false && results.length === 0 && 
          liveResearch.results && Array.isArray(liveResearch.results) && liveResearch.results.length > 0) {
        const contentSuggestionsCount = liveResearch.content_suggestions && Array.isArray(liveResearch.content_suggestions) 
          ? liveResearch.content_suggestions.length 
          : 0;
        toast.success('Market research completed!', {
          description: `Found ${liveResearch.results.length} research insights and ${contentSuggestionsCount} content suggestions.`
        });
      }
    }
  }, [liveResearch, currentResearchId, results.length]);

  // Update site research results when live site research data changes
  useEffect(() => {
    if (liveSiteResearch && liveSiteResearch.id === currentSiteResearchId) {
      
      // Update site results from the live data
      let liveResultsArray: Record<string, any>[] = [];
      if (liveSiteResearch.results) {
        if (Array.isArray(liveSiteResearch.results)) {
          liveResultsArray = liveSiteResearch.results;
        } else if (typeof liveSiteResearch.results === 'object') {
          // If results is a single object, wrap it in an array
          liveResultsArray = [liveSiteResearch.results];
        }
      }
      setSiteResults(liveResultsArray);

      // If site research is no longer generating, show success message
      if (liveSiteResearch.is_generating === false && siteResults.length === 0 && 
          liveSiteResearch.results && Array.isArray(liveSiteResearch.results) && liveSiteResearch.results.length > 0) {
        const urlsCount = Array.isArray(liveSiteResearch.urls) ? liveSiteResearch.urls.length : 0;
        toast.success('Site analysis completed!', {
          description: `Analyzed ${urlsCount} URLs and extracted ${liveSiteResearch.results.length} data records.`
        });
      }
    }
  }, [liveSiteResearch, currentSiteResearchId, siteResults.length]);

  // Update socials research results when live socials research data changes
  useEffect(() => {
    if (liveSocialsResearch && liveSocialsResearch.id === currentSocialsResearchId) {
      
      // Update socials results from the live data
      let liveSocialsResultsArray: any[] = [];
      if (liveSocialsResearch.results) {
        if (Array.isArray(liveSocialsResearch.results)) {
          liveSocialsResultsArray = liveSocialsResearch.results;
        }
      }
      setSocialsResults(liveSocialsResultsArray);

      // If socials research is no longer generating, show success message
      if (liveSocialsResearch.is_generating === false && socialsResults.length === 0 && 
          liveSocialsResearch.results && Array.isArray(liveSocialsResearch.results) && liveSocialsResearch.results.length > 0) {
        const keywordsCount = Array.isArray(liveSocialsResearch.keywords) ? liveSocialsResearch.keywords.length : 0;
        toast.success('Social media analysis completed!', {
          description: `Analyzed ${keywordsCount} keywords on ${liveSocialsResearch.platform} and found ${liveSocialsResearch.results.length} posts.`
        });
      }
    }
  }, [liveSocialsResearch, currentSocialsResearchId, socialsResults.length]);

  // Load selected research data when selectedResearch changes
  useEffect(() => {
    if (selectedResearch) {
      setResults(selectedResearch.results || []);
      setContentSuggestions(selectedResearch.content_suggestions || []);
      setCurrentResearchId(selectedResearch.id);
      setCurrentSiteResearchId(null);
      setSiteResults([]);
      setSearchType('web-search');
    } else if (selectedSiteResearch) {
      setResults([]);
      setContentSuggestions([]);
      // Convert results to array if it's an object
      let siteResultsArray: Record<string, any>[] = [];
      if (selectedSiteResearch.results) {
        if (Array.isArray(selectedSiteResearch.results)) {
          siteResultsArray = selectedSiteResearch.results;
        } else if (typeof selectedSiteResearch.results === 'object') {
          // If results is a single object, wrap it in an array
          siteResultsArray = [selectedSiteResearch.results];
        }
      }
      setSiteResults(siteResultsArray);
      setSocialsResults([]);
      setCurrentResearchId(null);
      setCurrentSiteResearchId(selectedSiteResearch.id);
      setCurrentSocialsResearchId(null);
      setSearchType('search-by-url');
    } else if (selectedSocialsResearch) {
      setResults([]);
      setContentSuggestions([]);
      setSiteResults([]);
      setSocialsResults(selectedSocialsResearch.results || []);
      setCurrentResearchId(null);
      setCurrentSiteResearchId(null);
      setCurrentSocialsResearchId(selectedSocialsResearch.id);
      setSearchType('social-media-search');
    } else {
      // Reset form for new research
      setResults([]);
      setContentSuggestions([]);
      setSiteResults([]);
      setSocialsResults([]);
      setCurrentResearchId(null);
      setCurrentSiteResearchId(null);
      setCurrentSocialsResearchId(null);
      setSearchType('web-search');
    }
  }, [selectedResearch, selectedSiteResearch, selectedSocialsResearch]);

  const handleResearchSaved = (research: GeneratedResearch) => {
    setCurrentResearchId(research.id);
    setCurrentSiteResearchId(null);
    onResearchSaved?.(research);
  };

  const handleSiteResearchSaved = (siteResearch: SiteResearch) => {
    setCurrentSiteResearchId(siteResearch.id);
    setCurrentResearchId(null);
    setCurrentSocialsResearchId(null);
    onSiteResearchSaved?.(siteResearch);
  };

  const handleSocialsResearchSaved = (socialsResearch: SocialsResearch) => {
    setCurrentSocialsResearchId(socialsResearch.id);
    setCurrentResearchId(null);
    setCurrentSiteResearchId(null);
    onSocialsResearchSaved?.(socialsResearch);
  };

  const isGenerating = liveResearch?.is_generating === true || 
                      liveSiteResearch?.is_generating === true || 
                      liveSocialsResearch?.is_generating === true;

  return (
    <div className="space-y-6">
      {/* Show research title if viewing existing research */}
      {selectedResearch && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold">{selectedResearch.title}</h2>
          <p className="text-sm text-muted-foreground">
            Created on {new Date(selectedResearch.created_at).toLocaleDateString()}
          </p>
        </div>
      )}

      {/* Show site research title if viewing existing site research */}
      {selectedSiteResearch && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold">Site Analysis</h2>
          <p className="text-sm text-muted-foreground">
            Created on {new Date(selectedSiteResearch.created_at).toLocaleDateString()} • 
            {Array.isArray(selectedSiteResearch.urls) ? selectedSiteResearch.urls.length : 0} URLs analyzed
          </p>
        </div>
      )}

      {/* Show socials research title if viewing existing socials research */}
      {selectedSocialsResearch && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold">Social Media Analysis</h2>
          <p className="text-sm text-muted-foreground">
            Created on {new Date(selectedSocialsResearch.created_at).toLocaleDateString()} • 
            {Array.isArray(selectedSocialsResearch.keywords) ? selectedSocialsResearch.keywords.length : 0} keywords • 
            Platform: {selectedSocialsResearch.platform}
          </p>
        </div>
      )}

      {/* Show generating status */}
      {isGenerating && (
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">
                  {liveSiteResearch?.is_generating ? 'Site Analysis' : 
                   liveSocialsResearch?.is_generating ? 'Social Media Analysis' : 'Research Generation'} in Progress
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Your {liveSiteResearch?.is_generating ? 'site analysis' : 
                          liveSocialsResearch?.is_generating ? 'social media analysis' : 'market research'} is being generated. Results will appear here automatically.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Research Configuration Form - only show when creating new research */}
      {!selectedResearch && !selectedSiteResearch && !selectedSocialsResearch && (
        <Card className="w-full">
          <CardHeader>
            <div className="flex justify-between items-start gap-4">
              <div className="flex-1">
                <CardTitle>Generate New Research</CardTitle>
                <CardDescription>Fill in the form fields below to get insights about your ICP or persona relevant to your business.</CardDescription>
              </div>
              {/* <div className="flex-shrink-0 min-w-[200px]">
                <ResearchTypeSelector
                  value={searchType}
                  onValueChange={setSearchType}
                />
              </div> */}
            </div>
          </CardHeader>
            <CardContent className="space-y-6">
              {/* Conditional rendering based on search type */}
              {searchType === 'web-search' && (
                <WebResearch 
                  selectedResearch={selectedResearch}
                  onResearchSaved={handleResearchSaved}
                  isGenerating={isGenerating}
                />
              )}
              
              {searchType === 'social-media-search' && (
                <SocialResearch 
                  onSocialsResearchSaved={handleSocialsResearchSaved}
                  isGenerating={isGenerating}
                />
              )}
              
              {searchType === 'search-by-url' && (
                <SiteSearch 
                  selectedResearch={selectedResearch}
                  selectedSiteResearch={selectedSiteResearch}
                  onResearchSaved={handleResearchSaved}
                  onSiteResearchSaved={handleSiteResearchSaved}
                  isGenerating={isGenerating}
                />
              )}
          </CardContent>
        </Card>
      )}

      {/* Research Results */}
      <If condition={!!selectedResearch}>
        <ResearchResults 
          results={results}
          formData={{
            icpId: selectedResearch?.icp_id || '',
            personaId: selectedResearch?.persona_id || '',
            type: selectedResearch?.research_type || '',
            timeFilter: selectedResearch?.time_filter || '',
            topic: selectedResearch?.topic || '',
          }}
          accountId={selectedResearch?.account_id || ''}
        />
      </If>

      {/* Site Research Results */}
      <SiteResearchResults 
        results={siteResults}
        isVisible={!!selectedSiteResearch}
      />

      {/* Socials Research Results */}
      <SocialsResearchResults 
        results={socialsResults}
        isVisible={!!selectedSocialsResearch}
        keywords={selectedSocialsResearch?.keywords || []}
        platform={selectedSocialsResearch?.platform || ''}
      />
    </div>
  );
} 