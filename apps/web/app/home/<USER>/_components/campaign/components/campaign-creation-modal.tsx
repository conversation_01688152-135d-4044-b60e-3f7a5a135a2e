'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { Plus, Loader2, ChevronDown, ChevronRight } from 'lucide-react';
import { CampaignAdvancedOptions } from './campaign-advanced-options';
import type { CampaignFormData } from '../types';

interface CampaignCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  formData: CampaignFormData;
  onFormChange: (field: keyof CampaignFormData, value: string | string[]) => void;
  showAdvanced: boolean;
  onToggleAdvanced: () => void;
  onCreateCampaign: () => void;
  isCreating: boolean;
  // Advanced options data
  savedResearch: any[];
  products: any[];
  icps: any[];
  personas: any[];
  // Advanced options handlers
  onResearchToggle: (researchId: string, checked: boolean) => void;
  onProductToggle: (productId: string, checked: boolean) => void;
  onIcpToggle: (icpId: string, checked: boolean) => void;
  onPersonaToggle: (personaId: string, checked: boolean) => void;
}

export const CampaignCreationModal = ({
  isOpen,
  onClose,
  formData,
  onFormChange,
  showAdvanced,
  onToggleAdvanced,
  onCreateCampaign,
  isCreating,
  savedResearch,
  products,
  icps,
  personas,
  onResearchToggle,
  onProductToggle,
  onIcpToggle,
  onPersonaToggle,
}: CampaignCreationModalProps) => {

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="dashboard:createCampaign" defaults="Create Campaign" />
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {/* Campaign Name */}
          <div className="space-y-2">
            <Label htmlFor="campaign-name">
              <Trans i18nKey="dashboard:campaignName" defaults="Campaign Name" />
            </Label>
            <Input
              id="campaign-name"
              value={formData.name}
              onChange={(e) => onFormChange('name', e.target.value)}
              placeholder="Enter campaign name"
            />
          </div>

          {/* Objective */}
          <div className="space-y-2">
            <Label htmlFor="objective">
              <Trans i18nKey="dashboard:objective" defaults="Objective" />
            </Label>
            <Textarea
              id="objective"
              value={formData.objective}
              onChange={(e) => onFormChange('objective', e.target.value)}
              placeholder="Campaign objective"
              rows={3}
            />
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="start-date">
              <Trans i18nKey="dashboard:startDate" defaults="Posting Start Date" />
            </Label>
            <Input
              id="start-date"
              type="date"
              value={formData.startDate}
              onChange={(e) => onFormChange('startDate', e.target.value)}
            />
          </div>

          {/* Advanced Section Toggle */}
          <div className="space-y-4">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onToggleAdvanced}
              className="w-full justify-between p-2 h-auto text-left"
            >
              <span className="text-sm font-medium">
                <Trans i18nKey="dashboard:advanced" defaults="Advanced Options" />
              </span>
              {showAdvanced ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>

            {/* Advanced Fields - Only show when expanded */}
            {showAdvanced && (
              <CampaignAdvancedOptions
                formData={formData}
                savedResearch={savedResearch}
                products={products}
                icps={icps}
                personas={personas}
                onResearchToggle={onResearchToggle}
                onProductToggle={onProductToggle}
                onIcpToggle={onIcpToggle}
                onPersonaToggle={onPersonaToggle}
              />
            )}
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              <Trans i18nKey="dashboard:cancel" defaults="Cancel" />
            </Button>
            <Button
              onClick={onCreateCampaign}
              className="flex-1"
              disabled={!formData.objective.trim() || !formData.name.trim() || isCreating}
            >
              {isCreating ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              <Trans i18nKey="dashboard:createContentSchedule" defaults="Create Content Schedule" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
