'use client';

import { Card, CardContent } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { Badge } from '@kit/ui/badge';
import { ArrowRight, MousePointer } from 'lucide-react';
import { formatDuration } from '../utils';
import type { CampaignTemplate } from '../types';

interface CampaignTemplateCardProps {
  template: CampaignTemplate;
  onQuickStart: (template: CampaignTemplate) => void;
}

export const CampaignTemplateCard = ({ template, onQuickStart }: CampaignTemplateCardProps) => {
  return (
    <Card 
      className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:border-primary/50 overflow-hidden flex-shrink-0 w-64 snap-start"
      onClick={() => onQuickStart(template)}
    >
      <CardContent className="p-0 h-full">
        <div className="flex flex-col h-full">
          {/* Top Icon Section with Background Image */}
          <div 
            className="h-24 bg-gradient-to-br from-primary/5 to-primary/10 flex items-center justify-center relative bg-cover bg-center"
            style={{
              backgroundImage: template.image_url 
                ? `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url(${template.image_url})`
                : undefined
            }}
          >
            {/* Duration Badge */}
            <Badge 
              variant="outline" 
              className="absolute top-2 right-2 text-xs px-2 py-1 bg-background/90 backdrop-blur-sm border-primary/20"
            >
              {formatDuration(template.duration_weeks || 0)}
            </Badge>
          </div>

          {/* Content Section */}
          <div className="flex-1 p-4 flex flex-col">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-lg font-semibold">{template.title}</h3>
                </div>
              </div>
              <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all ml-3 flex-shrink-0" />
            </div>

            {/* description Section */}
            <div className="space-y-2 mb-4 flex-grow">
              <div className="flex  space-x-2 items-start gap-2">
                <span className="text-xs text-muted-foreground ">{template.description}</span>
              </div>
            </div>

            {/* Action Button */}
            <Button 
              className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
              variant="outline"
              size="sm"
            >
              <MousePointer className="h-3 w-3 mr-2" />
              <Trans i18nKey="dashboard:startCampaign" defaults="Start Campaign" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
