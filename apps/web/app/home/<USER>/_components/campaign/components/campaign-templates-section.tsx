'use client';

import { Trans } from '@kit/ui/trans';
import { CampaignTemplateCard } from './campaign-template-card';
import type { CampaignTemplate } from '../types';

interface CampaignTemplatesSectionProps {
  campaignTemplates: CampaignTemplate[];
  onQuickStartCampaign: (template: CampaignTemplate) => void;
}

export const CampaignTemplatesSection = ({ 
  campaignTemplates, 
  onQuickStartCampaign 
}: CampaignTemplatesSectionProps) => {
  return (
    <div className="mb-8 flex flex-col gap-2 justify-center items-center">
      <div className="flex gap-6 overflow-x-auto pb-6 snap-x snap-mandatory scrollbar-hide">
        {campaignTemplates.map((template: any) => (
          <CampaignTemplateCard
            key={template.id}
            template={template}
            onQuickStart={onQuickStartCampaign}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="flex justify-center gap-2 mt-4">
        {campaignTemplates.map((_, index) => (
          <div 
            key={index}
            className="w-2 h-2 rounded-full bg-muted-foreground/30"
          />
        ))}
      </div>
    </div>
  );
};
