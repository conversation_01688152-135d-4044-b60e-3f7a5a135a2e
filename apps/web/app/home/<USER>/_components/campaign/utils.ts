export const formatDuration = (weeks: number): string => {
  if (weeks === 1) return '1 week';
  if (weeks < 4) return `${weeks} weeks`;
  if (weeks === 4) return '1 month';
  return `${Math.round(weeks / 4)} months`;
};

export const getTodayDate = (): string => {
  const dateString = new Date().toISOString().split('T')[0];
  return dateString || '';
};

export const calculateEndDate = (startDate: string, durationWeeks: number): string => {
  const start = new Date(startDate);
  const end = new Date(start);
  end.setDate(start.getDate() + (durationWeeks * 7));
  return end.toISOString().split('T')[0] || startDate;
};

export const generateCampaignId = (): string => {
  return crypto.randomUUID();
};
