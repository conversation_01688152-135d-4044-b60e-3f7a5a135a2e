//scaffold 
'use client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Skeleton } from '@kit/ui/skeleton';
import { If } from '@kit/ui/if';
import { useUser } from '@kit/supabase/hooks/use-user';
import { InitialCampaignResponse } from '~/types/Campaign';
import { useQuery } from '@tanstack/react-query';
import { getContentProgress } from '~/home/<USER>/_lib/server/get-content-progress';
import { cn } from '@kit/ui/utils';
import { Button } from '@kit/ui/button';
import Link from 'next/link';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

// Define the onboarding steps  
const onboardingSteps = [
  { name: 'Setup Brand', completed: true },
  { name: 'Upload Product Documentation', completed: true },
  { name: 'Start a campaign', completed: true },
  { name: 'Create a post', completed: true },
  { name: 'Publish some Content', completed: false },
];

// Checklist Icon Component
function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={3}
      stroke="currentColor"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
    </svg>
  );
}

// Onboarding Checklist Component
function OnboardingChecklist() {
  return (
    <Card className="max-w-lg">
      <CardHeader>
        <CardTitle>Get Started</CardTitle>
        <CardDescription>
          Follow these steps to get your content generation engine running!
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="space-y-4">
          {onboardingSteps.map((step, index) => (
            <li key={index} className="flex items-center gap-3">
              <div
                className={cn(
                  'flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full',
                  step.completed
                    ? 'bg-primary text-primary-foreground'
                    : 'border-2 border-muted-foreground',
                )}
              >
                {step.completed && <CheckIcon className="h-3 w-3" />}
              </div>
              <span
                className={cn(
                  'text-sm font-medium',
                  step.completed
                    ? 'text-muted-foreground line-through'
                    : 'text-foreground',
                )}
              >
                {step.name}
              </span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

export function IntialContentProgress() {
  const { data: user } = useUser();
  console.log('user', user);
  const initialCampaignId = user?.user_metadata?.initialCampaignId;
  const workspace = useTeamAccountWorkspace();

  const { data: campaignStatus, error } = useQuery<InitialCampaignResponse, Error>({
    queryKey: ['campaign-status', initialCampaignId],
    queryFn: async () => {
      return await getContentProgress(user);
    },
    enabled: !!initialCampaignId,
    refetchInterval: (query) => {
      const data = query.state.data;
      return data?.isComplete || data?.isFailed ? false : 2000;
    },
    refetchOnWindowFocus: (query) => {
      const data = query.state.data;
      return !data?.isComplete && !data?.isFailed;
    },
    retry: 3,
  });

  console.log('campaignStatus', initialCampaignId);
  // If no campaign ID, don't show anything
  if (!initialCampaignId) {
    return null;
  }
  console.log('workspace', workspace);
  return (
    <div className="flex flex-col justify-around gap-4">
    <Card className="w-full mx-auto">
      <CardHeader className="flex flex-col items-center justify-center">
        <CardTitle className="flex items-center gap-2">
        {campaignStatus?.isComplete ? 
        (
              <div className="flex justify-between flex-col gap-2">
              <div className="flex justify-between items-center gap-2" role="img" aria-label="status emoji">
              <div>{`${campaignStatus?.progressEmoji} ${"Ready for blast-off!  "} `}</div>
              <div>
              <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                  {campaignStatus?.status}
                </span>
                </div>
            </div>
              <Alert>
                <AlertDescription>
                  Your campaign has been successfully created! You can now start exploring your workspace.
                </AlertDescription>
                <Button className="mt-5">
                  <Link href={`/home/<USER>/tasks`}>
                    Go To Content
                  </Link>
                </Button>
              </Alert>
              </div>
            ) : (
              <div className="flex justify-between flex-col gap-2">
              <span className="mb-4" role="img" aria-label="status emoji">
              {`${campaignStatus?.progressEmoji} ${"  "} ${campaignStatus?.progressMessage}`}
              <Alert className="mt-4">
                <AlertDescription>
                  <span className="text-md">No one likes an empty workspace, so we&apos;re cooking up your first campaign! 👩🏼‍🍳 Hold tight!</span>
                </AlertDescription>
                
              </Alert>
            </span>
            </div>
            )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <If condition={!!error}>
          <Alert variant="destructive">
            <AlertDescription>
              {error instanceof Error ? error.message : 'Failed to fetch status'}
            </AlertDescription>
          </Alert>
        </If>

        <If
          condition={!!campaignStatus}
          fallback={
            <div className="space-y-4">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          }
        >
          <div className="">
            {campaignStatus?.isFailed && (
              <Alert variant="destructive">
                <AlertDescription>
                  There was an error creating your campaign. Please try again or contact support if the issue persists.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </If>
        </CardContent>
      </Card>
      {campaignStatus?.isComplete && <OnboardingChecklist />}
    </div>
  );
}
