 {/* Get Stuff Section */}
      {/* <Card className="min-h-[400px] flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-primary" />
            <Trans i18nKey="dashboard:getStuff.title" defaults="Get Started" />
          </CardTitle>
          <CardDescription>
            <Trans 
              i18nKey="dashboard:getStuff.description" 
              defaults="Get the best out of access. Complete steps below" 
            />
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col justify-between space-y-4">
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 rounded-lg border border-green-200 bg-green-50/50">
              <Badge variant="outline" className="min-w-6 h-6 flex items-center justify-center text-xs border-green-300 text-green-700">0</Badge>
              <div className="flex items-center gap-2">
                <UserPlus className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  <Trans i18nKey="dashboard:getStuff.signUp" defaults="Sign Up for Axcels" />
                </span>
                <span className="text-green-600">✓</span>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
              <Badge variant="outline" className="min-w-6 h-6 flex items-center justify-center text-xs">1</Badge>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  <Trans i18nKey="dashboard:getStuff.serveBrand" defaults="Setup Brand" />
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
              <Badge variant="outline" className="min-w-6 h-6 flex items-center justify-center text-xs">2</Badge>
              <div className="flex items-center gap-2">
                <Upload className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  <Trans i18nKey="dashboard:getStuff.uploadProduct" defaults="Upload Product Info" />
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
              <Badge variant="outline" className="min-w-6 h-6 flex items-center justify-center text-xs">3</Badge>
              <div className="flex items-center gap-2">
                <Share2 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  <Trans i18nKey="dashboard:getStuff.connectSocials" defaults="Connect Socials" />
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
              <Badge variant="outline" className="min-w-6 h-6 flex items-center justify-center text-xs">4</Badge>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  <Trans i18nKey="dashboard:getStuff.produceContent" defaults="Produce Content" />
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card> */}