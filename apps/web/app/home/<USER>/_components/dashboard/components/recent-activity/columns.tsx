"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Circle } from "lucide-react"
import { formatRelativeTime } from "@kit/shared/utils"
import { Trans } from "@kit/ui/trans"
import { AssigneeAvatar } from "../../../../tasks/components/assignee-avatar"
import { TaskTitleButtonProps, RecentActivityColumnsProps } from "./types"
import { memo } from "react"

// Memoized clickable task title component for performance
const TaskTitleButton = memo<TaskTitleButtonProps>(({ task, title, onTaskClick }) => (
  <button
    onClick={() => onTaskClick?.(task)}
    className="max-w-[200px] truncate font-medium text-sm hover:text-primary hover:underline transition-colors cursor-pointer text-left"
    type="button"
    aria-label={`Edit task: ${title}`}
  >
    {title}
  </button>
));

TaskTitleButton.displayName = 'TaskTitleButton';

// Status formatting utility
const formatStatus = (status: string | undefined): string => {
  const statusText = status || 'draft';
  return statusText.charAt(0).toUpperCase() + statusText.slice(1);
};

export const createRecentActivityColumns = ({
  onTaskClick
}: RecentActivityColumnsProps = {}): ColumnDef<any>[] => [
  {
    accessorKey: "task_title",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.task'} />,
    cell: ({ row }) => {
      return (
        <TaskTitleButton
          task={row.original}
          title={row.getValue("task_title")}
          onTaskClick={onTaskClick}
        />
      )
    },
  },
  {
    accessorKey: "status",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.status'} />,
    cell: ({ row }) => (
      <div className="flex items-center">
        <Circle className="mr-2 h-3 w-3 text-muted-foreground" />
        <span className="text-xs">{formatStatus(row.original.status)}</span>
      </div>
    ),
  },
  {
    accessorKey: "assigned_to",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.assignee'} />,
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <AssigneeAvatar
          taskId={row.original.id}
          assignedTo={row.original.assigned_to}
        />
      </div>
    ),
    enableSorting: false,
  },
  {
    accessorKey: "updated_at",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.updated'} />,
    cell: ({ row }) => {
      const updatedAt = row.original.updated_at;

      if (!updatedAt) {
        return (
          <span className="text-xs text-muted-foreground">
            <Trans i18nKey={'dashboard:recentActivity.neverUpdated'} />
          </span>
        );
      }

      return (
        <span className="text-xs text-muted-foreground">
          {formatRelativeTime(updatedAt)}
        </span>
      );
    },
  },
];
