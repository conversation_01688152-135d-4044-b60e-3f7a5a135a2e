import { CompanyContent } from '~/types/company-content';

/**
 * Safely converts various timestamp formats to number
 */
export function getTimestamp(timestamp: any): number {
  if (!timestamp) return 0;
  if (typeof timestamp === 'number') return timestamp;
  if (typeof timestamp === 'string') return new Date(timestamp).getTime();
  return 0;
}

/**
 * Checks if a task is valid for display in Recent Activity
 */
export function isValidTask(task: any): boolean {
  // Must have a task title
  if (!task.task_title || task.task_title.trim() === '') {
    return false;
  }

  // Must not be archived
  if (task.archived === true) {
    return false;
  }

  return true;
}

/**
 * Checks if a task was actually updated (not just created)
 * A task is considered "updated" if there's a meaningful time difference
 * between when it was created and when it was last modified
 */
export function wasTaskUpdated(task: any): boolean {
  if (!task.created_at || !task.updated_at) return false;

  const createdTime = getTimestamp(task.created_at);
  const updatedTime = getTimestamp(task.updated_at);

  // Ensure we have valid timestamps
  if (createdTime === 0 || updatedTime === 0) return false;

  // Task must have been updated AFTER creation
  if (updatedTime <= createdTime) return false;

  // Consider it updated if there's more than 5 seconds difference
  // This accounts for potential database timing differences and ensures
  // we only show tasks that were genuinely modified, not just created
  const timeDifference = updatedTime - createdTime;
  return timeDifference > 5000; // 5 seconds minimum difference
}

/**
 * Sorts tasks by timestamp in descending order (most recent first)
 */
export function sortByTimestamp(tasks: any[], timestampField: 'updated_at' | 'created_at'): any[] {
  return tasks.sort((a, b) => {
    const aTime = getTimestamp(a[timestampField]);
    const bTime = getTimestamp(b[timestampField]);
    return bTime - aTime;
  });
}

/**
 * Processes company content to get recent tasks for display
 * Only shows tasks that have been actually updated (not just created)
 */
export function getRecentTasks(company_content: any[]): any[] {
  if (!company_content || company_content.length === 0) {
    return [];
  }

  // Filter for valid tasks that have titles and are not archived
  const validTasks = company_content.filter(isValidTask);

  // Only show tasks that were actually updated (not just created)
  const updatedTasks = validTasks.filter(wasTaskUpdated);

  // Sort by update time (most recent first) and return top 5
  return sortByTimestamp(updatedTasks, 'updated_at').slice(0, 5);
}
