import { Trans } from "@kit/ui/trans";

/**
 * Props for the error state component
 */
export interface RecentActivityErrorProps {
  error?: any;
}

/**
 * Error state component for Recent Activity
 * Shows when data loading fails
 */
export function RecentActivityError({ error }: RecentActivityErrorProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">
        <Trans i18nKey={'dashboard:recentActivity.title'} defaults="Recent Activity" />
      </h3>
      <div className="flex items-center justify-center h-32 text-destructive">
        <p className="text-sm">
          <Trans
            i18nKey={'dashboard:recentActivity.error'}
            defaults="Failed to load recent activity. Please try again."
          />
        </p>
      </div>
    </div>
  );
}
