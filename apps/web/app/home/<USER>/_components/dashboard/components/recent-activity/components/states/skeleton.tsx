import { Spinner } from "@kit/ui/spinner";
import { Trans } from "@kit/ui/trans";

/**
 * Loading skeleton component for Recent Activity
 * Shows a spinner while data is being fetched
 */
export function RecentActivitySkeleton() {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">
        <Trans i18nKey={'dashboard:recentActivity.title'} defaults="Recent Activity" />
      </h3>
      <div className="flex items-center justify-center h-32">
        <Spinner className="h-6 w-6" />
      </div>
    </div>
  );
}
