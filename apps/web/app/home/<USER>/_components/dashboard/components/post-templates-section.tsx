'use client';

import { Trans } from '@kit/ui/trans';
import { PostTemplateCard } from './post-template-card';
import type { PostTemplate } from '../../campaign/types';

interface PostTemplatesSectionProps {
  postTemplates: PostTemplate[];
  onQuickStartPost: (template: PostTemplate) => void;
}

export const PostTemplatesSection = ({ 
  postTemplates, 
  onQuickStartPost 
}: PostTemplatesSectionProps) => {
  if (!postTemplates || postTemplates.length === 0) {
    return null;
  }

  return (
    <div className="mb-8 flex flex-col gap-2 justify-center items-center">
      <h2 className="text-2xl font-bold tracking-tight mb-2">
        <Trans i18nKey="dashboard:postQuickStart" defaults="Post Quick Start" />
      </h2>
      <p className="text-md text-muted-foreground">
        <Trans 
          i18nKey="dashboard:postQuickStartSubtitle" 
          defaults="Create engaging posts using proven templates" 
        />
      </p>
      
      <div className="flex gap-6 overflow-x-auto pb-6 snap-x snap-mandatory scrollbar-hide">
        {postTemplates.map((template: any) => (
          <PostTemplateCard
            key={template.id}
            template={template}
            onQuickStart={onQuickStartPost}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="flex justify-center gap-2 mt-4">
        {postTemplates.map((_, index) => (
          <div 
            key={index}
            className="w-2 h-2 rounded-full bg-muted-foreground/30"
          />
        ))}
      </div>
    </div>
  );
};
