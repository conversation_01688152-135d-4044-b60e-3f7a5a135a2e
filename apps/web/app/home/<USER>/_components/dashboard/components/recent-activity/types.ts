import { CompanyContent } from '~/types/company-content';

/**
 * Props for the TaskTitleButton component
 */
export interface TaskTitleButtonProps {
  task: CompanyContent;
  title: string;
  onTaskClick?: (task: CompanyContent) => void;
}

/**
 * Props for creating Recent Activity columns
 */
export interface RecentActivityColumnsProps {
  onTaskClick?: (task: CompanyContent) => void;
}

/**
 * Task data structure for Recent Activity display
 */
export interface RecentActivityTask {
  id: string;
  task_title: string;
  status?: string;
  assigned_to?: string;
  updated_at?: string | number;
  created_at?: string | number;
  archived?: boolean;
}

// export interface RecentActivityTask extends Pick<CompanyContent, 'id' | 'created_at' | 'archived' | 'status' | 'assigned_to'> {
//   task_title: string; // Required
//   updated_at: string | number; // Different type than CompanyContent
// }
