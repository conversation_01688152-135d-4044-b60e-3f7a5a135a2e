'use client';

import { Trans } from '@kit/ui/trans';
import { Loader2 } from 'lucide-react';
import { DashboardHeader } from './dashboard-header';

export const DashboardLoading = () => {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <DashboardHeader />
      
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">
            <Trans i18nKey="dashboard:loadingTemplates" defaults="Loading campaign templates..." />
          </p>
        </div>
      </div>
    </div>
  );
};
