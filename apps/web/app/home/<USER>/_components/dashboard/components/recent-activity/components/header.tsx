import { Trans } from "@kit/ui/trans";

/**
 * Header component for Recent Activity section
 * Displays title and description
 */
export function RecentActivityHeader() {
  return (
    <>
      <h3 className="text-lg font-semibold">
        <Trans i18nKey={'dashboard:recentActivity.title'} defaults="Recent Activity" />
      </h3>
      <p className="text-sm text-muted-foreground">
        <Trans
          i18nKey={'dashboard:recentActivity.description'}
          defaults="Your 5 most recently updated tasks (or most recent if none updated)"
        />
      </p>
    </>
  );
}
