import React from 'react';
import { SettingsNavigationSidebar } from './settings-navigation-sidebar';

interface SettingsLayoutProps {
  children: React.ReactNode;
  account: string;
}

export function SettingsLayout({ children, account }: SettingsLayoutProps) {
  return (
    <div className="flex h-full">
      <SettingsNavigationSidebar account={account} />
      <div className="flex-1 overflow-auto">
        {children}
      </div>
    </div>
  );
}