'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Users, 
  Blocks, 
  User, 
  Settings, 
  CreditCard 
} from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { Trans } from '@kit/ui/trans';

interface SettingsNavigationSidebarProps {
  account: string;
}

const navigationItems = [
  { 
    id: 'members', 
    label: 'common:routes.members', 
    icon: <Users size={18} />,
    path: `/home/<USER>/members`,
    description: 'Manage team members and roles'
  },
  { 
    id: 'integrations', 
    label: 'Integrations', 
    icon: <Blocks size={18} />,
    path: `/home/<USER>/integrations`,
    description: 'Connect external services'
  },
  { 
    id: 'user-settings', 
    label: 'common:routes.account', 
    icon: <User size={18} />,
    path: `/home/<USER>/user-settings`,
    description: 'Personal account settings'
  },
  { 
    id: 'settings', 
    label: 'common:routes.settings', 
    icon: <Settings size={18} />,
    path: `/home/<USER>/settings`,
    description: 'Account configuration'
  },
  { 
    id: 'billing', 
    label: 'common:routes.billing', 
    icon: <CreditCard size={18} />,
    path: `/home/<USER>/billing`,
    description: 'Billing and subscriptions'
  }
];

export function SettingsNavigationSidebar({ account }: SettingsNavigationSidebarProps) {
  const pathname = usePathname();

  const getItemPath = (path: string) => path.replace('[account]', account);

  const isActiveItem = (itemPath: string) => {
    const fullPath = getItemPath(itemPath);
    return pathname?.startsWith(fullPath);
  };

  return (
    <div className="w-64 bg-background border-r border-border h-full overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-semibold text-foreground mb-4">
          <Trans i18nKey="common:routes.settings" defaults="Settings" />
        </h2>
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const itemPath = getItemPath(item.path);
            const isActive = isActiveItem(item.path);
            
            return (
              <Link
                key={item.id}
                href={itemPath}
                className={cn(
                  'w-full flex flex-col items-start p-3 rounded-lg text-left transition-colors',
                  isActive
                    ? 'bg-accent text-accent-foreground border border-border'
                    : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                )}
              >
                <div className="flex items-center w-full mb-1">
                  <span className="mr-3">{item.icon}</span>
                  <span className="font-medium">
                    <Trans i18nKey={item.label} defaults={item.label} />
                  </span>
                </div>
                <span className="text-xs text-muted-foreground ml-9">{item.description}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
}