import { Card, CardContent } from '@kit/ui/card';
import { cn } from '@kit/ui/utils';

interface FourQuadrantLayoutProps {
  topLeft: React.ReactNode;
  topRight: React.ReactNode;
  bottomLeft: React.ReactNode;
  bottomRight: React.ReactNode;
  className?: string;
}

export function FourQuadrantLayout({
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  className,
}: FourQuadrantLayoutProps) {
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 gap-6 h-full', className)}>
      <Card className="h-fit">
        <CardContent className="p-6">
          {topLeft}
        </CardContent>
      </Card>
      
      <Card className="h-fit">
        <CardContent className="p-6">
          {topRight}
        </CardContent>
      </Card>
      
      <Card className="h-fit">
        <CardContent className="p-6">
          {bottomLeft}
        </CardContent>
      </Card>
      
      <Card className="h-fit">
        <CardContent className="p-6">
          {bottomRight}
        </CardContent>
      </Card>
    </div>
  );
} 