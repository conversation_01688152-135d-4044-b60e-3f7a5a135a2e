'use client';

import { useState } from 'react';
import { Trans } from '@kit/ui/trans';
import { Badge } from '@kit/ui/badge';
import { OnboardingTaskItem } from './onboarding-task-item';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface OnboardingTask {
  id: string;
  title: string;
  videoUrl: string;
  isBonus?: boolean;
  isCompleted?: boolean;
  pageUrl?: string;
}

export function OnboardingChecklist() {
  const [completedTasks, setCompletedTasks] = useState<Set<string>>(new Set());
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const accountSlug = workspace.account.slug || workspace.account.id;

  const [companyCampaigns] = useZeroQuery(
    zero.query.company_campaigns
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  const [products] = useZeroQuery(
    zero.query.products
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );

  console.log({products});
  const [ayrshareSocialProfiles] = useZeroQuery(
    zero.query.ayrshare_social_profiles
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  
  const [generatedResearch] = useZeroQuery(
    zero.query.generated_research
    .where("account_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  
  const [icps] = useZeroQuery(
    zero.query.icps
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  
 
  
  const ONBOARDING_TASKS: OnboardingTask[] = [
    {
      id: 'setup-brand',
      title: 'Setup Brand',
      videoUrl: 'https://www.youtube.com/watch?v=T0iSK6kJ6JI',
      isCompleted: companyBrand?.length > 0,
      pageUrl: '/home/<USER>/brand',
    },
    {
      id: 'add-products',
      title: 'Add Product Documents',
      videoUrl: 'https://www.youtube.com/watch?v=pCUgTku4sGY',
      isCompleted: products?.length > 0,
      pageUrl: '/home/<USER>/product-information',
    },
    {
      id: 'connect-socials',
      title: 'Connect Your Socials',
      videoUrl: 'https://www.youtube.com/watch?v=vVDx6yzw57k',
      isCompleted: ayrshareSocialProfiles?.length > 0,
      pageUrl: '/home/<USER>/integrations/socials',
    },
    {
      id: 'setup-profiles',
      title: 'Setup Customer Profiles',
      videoUrl: 'https://www.youtube.com/watch?v=c_nJkf7O3bE',
      isCompleted: icps?.length > 0,
      pageUrl: '/home/<USER>/personas',
    },
    {
      id: 'launch-campaign',
      title: 'Launch A Campaign',
      videoUrl: 'https://www.youtube.com/watch?v=jjDfrzNJqwQ',
      isCompleted: companyCampaigns?.length > 0,
      pageUrl: '/home/<USER>/create',
      },
    {
      id: 'market-research',
      title: 'Generate Market Research',
      videoUrl: 'https://www.youtube.com/watch?v=f5VTjMZbYlc',
      isCompleted: generatedResearch?.length > 0,
      pageUrl: '/home/<USER>/market-research',
    },
  ];

  const toggleTask = (taskId: string) => {
    setCompletedTasks(prev => {
      const updated = new Set(prev);
      if (updated.has(taskId)) {
        updated.delete(taskId);
      } else {
        updated.add(taskId);
      }
      return updated;
    });
  };

  const regularTasks = ONBOARDING_TASKS.filter(task => !task.isBonus);
  const completedRegularTasks = regularTasks.filter(task => task.isCompleted).length;
  const progressPercentage = Math.round((completedRegularTasks / regularTasks.length) * 100) || 0;
  
  // Hide the checklist when all tasks are completed
  const allTasksCompleted = completedRegularTasks === regularTasks.length;
  
  if (allTasksCompleted) {
    return null;
  }

  return (
    <div className="space-y-2">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            <Trans i18nKey={'onboarding:title'} defaults="Getting Started" />
          </h3>
          <Badge variant="secondary" className="text-sm">
            {progressPercentage}%
          </Badge>
        </div>
        
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      <div className="space-y-3">
        {regularTasks.map((task) => (
          <OnboardingTaskItem
            key={task.id}
            title={task.title}
            completed={task.isCompleted || false}
            onToggle={() => toggleTask(task.id)}
            videoUrl={task.videoUrl}
            pageUrl={task.pageUrl}
            accountSlug={accountSlug}
          />
        ))}
      </div>

    </div>
  );
}