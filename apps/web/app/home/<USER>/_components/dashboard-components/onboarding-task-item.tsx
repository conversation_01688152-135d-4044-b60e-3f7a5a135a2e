import { Button } from '@kit/ui/button';
import { cn } from '@kit/ui/utils';
import { CheckCircle, ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface OnboardingTaskItemProps {
  title: string;
  completed: boolean;
  onToggle: () => void;
  videoUrl: string;
  pageUrl?: string;
  accountSlug: string;
  compact?: boolean;
}

export function OnboardingTaskItem({
  title,
  completed,
  onToggle,
  videoUrl,
  pageUrl,
  accountSlug,
  compact = false,
}: OnboardingTaskItemProps) {
  const router = useRouter();

  const handleNavigate = () => {
    if (pageUrl) {
      const finalUrl = pageUrl.replace('[account]', accountSlug);
      router.push(finalUrl);
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2 group py-1">
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-auto hover:bg-transparent flex-shrink-0"
          onClick={onToggle}
        >
          <div className="relative">
            {completed ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <div className="h-4 w-4 border-2 border-dashed border-muted-foreground rounded-full group-hover:border-primary transition-colors" />
            )}
          </div>
        </Button>
        
        <div className="flex items-center gap-1 flex-1 min-w-0">
          <Button
            variant="ghost"
            className={cn(
              'p-0 h-auto text-xs font-medium truncate hover:bg-transparent flex-1 justify-start',
              completed ? 'text-green-600 line-through' : 'text-foreground group-hover:text-primary'
            )}
            onClick={handleNavigate}
          >
            {title}
          </Button>
          {pageUrl && (
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-auto hover:bg-transparent flex-shrink-0"
              onClick={handleNavigate}
            >
              <ArrowRight className="h-3 w-3 text-muted-foreground hover:text-primary" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3 group">
      <Button
        variant="ghost"
        size="sm"
        className="p-0 h-auto hover:bg-transparent"
        onClick={onToggle}
      >
        <div className="relative">
          {completed ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <div className="h-5 w-5 border-2 border-dashed border-muted-foreground rounded-full group-hover:border-primary transition-colors" />
          )}
        </div>
      </Button>
      
      <Button
        variant="ghost"
        className={cn(
          'flex-1 justify-start p-0 h-auto font-medium text-left hover:bg-transparent',
          completed ? 'text-green-600 line-through' : 'text-foreground group-hover:text-primary'
        )}
        onClick={handleNavigate}
      >
        {title}
      </Button>

      {pageUrl && (
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-auto hover:bg-transparent opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={handleNavigate}
        >
          <ArrowRight className="h-4 w-4 text-muted-foreground hover:text-primary" />
        </Button>
      )}
    </div>
  );
} 