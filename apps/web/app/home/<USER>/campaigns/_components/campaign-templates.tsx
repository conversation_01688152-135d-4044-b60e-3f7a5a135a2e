'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { But<PERSON> } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import { ArrowRight, Rocket, Users, Target, Plus, Palette, Brain, Clock } from 'lucide-react';

type DifficultyLevel = 'easy' | 'medium' | 'hard';

interface CampaignTemplate {
  id: string;
  title: string;
  goal: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  isCustom?: boolean;
  difficulty: DifficultyLevel;
  timeframe: string;
}

const getDifficultyConfig = (difficulty: DifficultyLevel) => {
  switch (difficulty) {
    case 'easy':
      return {
        label: 'Easy',
        color: 'bg-green-100 text-green-800 border-green-200',
        dotColor: 'bg-green-500'
      };
    case 'medium':
      return {
        label: 'Medium',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        dotColor: 'bg-yellow-500'
      };
    case 'hard':
      return {
        label: 'Advanced',
        color: 'bg-red-100 text-red-800 border-red-200',
        dotColor: 'bg-red-500'
      };
  }
};

const campaignTemplates: CampaignTemplate[] = [
  {
    id: 'product-launch',
    title: 'Launch a New Feature',
    goal: 'Create awareness and drive early adoption from target users or developers for a new product, API, or feature release.',
    description: 'Perfect for announcing new features, API releases, or product launches to your developer community.',
    icon: <Rocket className="h-6 w-6" />,
    color: 'bg-blue-500/10 text-blue-600 border-blue-200',
    difficulty: 'medium',
    timeframe: '2-3 weeks',
  },
  {
    id: 'lead-generation',
    title: 'Generate Leads for a Sales Funnel',
    goal: 'Create inbound interest among qualified leads to book sales calls or request demos.',
    description: 'Drive qualified prospects through targeted campaigns that convert visitors into potential customers.',
    icon: <Target className="h-6 w-6" />,
    color: 'bg-green-500/10 text-green-600 border-green-200',
    difficulty: 'easy',
    timeframe: '1-2 weeks',
  },
  {
    id: 'hire-engineers',
    title: 'Hire Engineers for Your Team',
    goal: 'Attract high-quality candidates from your tech stack\'s ecosystem or online communities.',
    description: 'Reach top talent in developer communities and tech-focused platforms to grow your engineering team.',
    icon: <Users className="h-6 w-6" />,
    color: 'bg-purple-500/10 text-purple-600 border-purple-200',
    difficulty: 'hard',
    timeframe: '4-6 weeks',
  },
  {
    id: 'thought-leadership',
    title: 'Build Thought Leadership',
    goal: 'Establish authority and expertise in your industry by sharing insights, trends, and valuable content.',
    description: 'Position yourself or your company as a trusted voice in your field through strategic content and engagement.',
    icon: <Brain className="h-6 w-6" />,
    color: 'bg-indigo-500/10 text-indigo-600 border-indigo-200',
    difficulty: 'hard',
    timeframe: '6-8 weeks',
  },
  {
    id: 'custom',
    title: 'Create Custom Campaign',
    goal: 'Design your own campaign template tailored to your specific business objectives and target audience.',
    description: 'Build a completely custom campaign from scratch with your own goals, messaging, and targeting strategy.',
    icon: <Palette className="h-6 w-6" />,
    color: 'bg-orange-500/10 text-orange-600 border-orange-200',
    isCustom: true,
    difficulty: 'medium',
    timeframe: 'Variable',
  },
];

export default function CampaignTemplates() {
  const handleCreateCampaign = (templateId: string) => {
    if (templateId === 'custom') {
      // TODO: Navigate to custom campaign builder
      console.log('Opening custom campaign builder');
    } else {
      // TODO: Implement template-based campaign creation logic
      console.log('Creating campaign with template:', templateId);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight mb-4">
          <Trans i18nKey="campaigns:templates.title" defaults="Choose Your Campaign Type" />
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          <Trans 
            i18nKey="campaigns:templates.description" 
            defaults="Select a template that matches your goals and start building your campaign in minutes." 
          />
        </p>
      </div>

      {/* Campaign Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5 gap-8 mb-8">
        {campaignTemplates.map((template) => {
          const difficultyConfig = getDifficultyConfig(template.difficulty);
          
          return (
            <Card 
              key={template.id}
              className={`group relative overflow-hidden border-2 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 flex flex-col ${
                template.isCustom 
                  ? 'border-dashed border-2 border-muted-foreground/30 hover:border-primary/70 bg-gradient-to-br from-background to-muted/20' 
                  : ''
              }`}
            >
              <CardHeader className="pb-4 flex-1">
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 rounded-lg ${template.color} ${template.isCustom ? 'bg-gradient-to-br from-orange-500/10 to-pink-500/10' : ''}`}>
                    {template.isCustom ? (
                      <div className="relative">
                        {template.icon}
                        <Plus className="h-3 w-3 absolute -top-1 -right-1 text-orange-600" />
                      </div>
                    ) : (
                      template.icon
                    )}
                  </div>
                  <Badge 
                    variant={template.isCustom ? "outline" : "secondary"} 
                    className={`text-xs ${template.isCustom ? 'border-orange-200 text-orange-600' : ''}`}
                  >
                    {template.isCustom ? (
                      <Trans i18nKey="campaigns:templates.custom" defaults="Custom" />
                    ) : (
                      <Trans i18nKey="campaigns:templates.template" defaults="Template" />
                    )}
                  </Badge>
                </div>
                
                <CardTitle className="text-xl font-semibold leading-tight mb-2">
                  {template.title}
                </CardTitle>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                        <Trans i18nKey="campaigns:templates.goal" defaults="Goal" />
                      </span>
                    </div>
                    <p className="text-sm text-foreground leading-relaxed">
                      {template.goal}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0 flex flex-col justify-end">
                <CardDescription className="text-sm leading-relaxed mb-4">
                  {template.description}
                </CardDescription>

                {/* Difficulty and Time Section */}
                <div className="flex items-center justify-between mb-4 p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${difficultyConfig.dotColor}`}></div>
                    <span className={`text-xs font-medium px-2 py-1 rounded-full border ${difficultyConfig.color}`}>
                      {difficultyConfig.label}
                    </span>
                  </div>
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span className="text-xs font-medium">{template.timeframe}</span>
                  </div>
                </div>

                <Button 
                  onClick={() => handleCreateCampaign(template.id)}
                  className={`w-full transition-colors ${
                    template.isCustom 
                      ? 'bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white border-0' 
                      : 'group-hover:bg-primary group-hover:text-primary-foreground'
                  }`}
                  variant={template.isCustom ? "default" : "outline"}
                >
                  <span>
                    {template.isCustom ? (
                      <Trans i18nKey="campaigns:templates.startFromScratch" defaults="Start from Scratch" />
                    ) : (
                      <Trans i18nKey="campaigns:templates.createCampaign" defaults="Create Campaign" />
                    )}
                  </span>
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>

              {/* Subtle background pattern */}
              <div className="absolute top-0 right-0 w-20 h-20 opacity-5 overflow-hidden">
                {template.isCustom ? (
                  <div className="absolute top-4 right-4 w-16 h-16">
                    <div className="w-4 h-4 rounded-full bg-orange-500 absolute top-0 left-0"></div>
                    <div className="w-4 h-4 rounded-full bg-pink-500 absolute top-2 right-0"></div>
                    <div className="w-4 h-4 rounded-full bg-purple-500 absolute bottom-0 left-2"></div>
                  </div>
                ) : (
                  <div className="absolute top-4 right-4 w-16 h-16 rounded-full bg-current" />
                )}
              </div>
            </Card>
          );
        })}
      </div>

      {/* Legend */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center gap-6 p-4 bg-muted/30 rounded-lg">
          <span className="text-sm font-medium text-muted-foreground">
            <Trans i18nKey="campaigns:templates.difficultyLegend" defaults="Conversion Difficulty:" />
          </span>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <span className="text-xs">Easy</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
              <span className="text-xs">Medium</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-red-500"></div>
              <span className="text-xs">Advanced</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom CTA */}
      <div className="text-center">
        <p className="text-muted-foreground mb-4">
          <Trans 
            i18nKey="campaigns:templates.needHelp" 
            defaults="Need help choosing the right template?" 
          />
        </p>
        <Button variant="ghost" className="text-primary hover:text-primary/80">
          <Trans i18nKey="campaigns:templates.contactSupport" defaults="Contact Support" />
        </Button>
      </div>
    </div>
  );
}
