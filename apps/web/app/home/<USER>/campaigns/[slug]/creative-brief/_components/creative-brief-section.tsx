'use client';

import { useState, useEffect } from 'react';
import { Separator } from '@kit/ui/separator';
import { CampaignIdea } from '~/types/campaign-idea';
import { Campaign } from '~/types/Campaign';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';
import { upsertCampaignIdea } from '~/services/campaign';
import { MarketingContentProps } from '../_types/brief-types';
import BriefDisplay from './brief-display';
import IdeaSelector from './idea-selector';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief, extractCampaignBrief } from '~/utils/brief.util';
import { useCampaignContext } from '../../_components/campaign-context';
import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import LanguageSelect from '../../ideation/_components/language-select';
import ChannelSelect from '../../ideation/_components/channel-select';
import ContentTypeSelect from '../../ideation/_components/content-type-select';
import { createLanguageHandler, createChannelHandler, createContentTypeHandler } from '../../_utils/selection-handlers';
import { useCreateBlockNote } from '@blocknote/react';
import { jsonToMarkdown } from '../_lib/jsonToMarkdown';

interface CreativeBriefSectionProps {
  campaign: Campaign;
  campaignIdeas: CampaignIdea[];
}

export default function CreativeBriefSection({
  campaign,
  campaignIdeas
}: CreativeBriefSectionProps) {
  const workspace = useTeamAccountWorkspace();
  const brand = useBrandData(workspace.account.id);
  
  // Safely get the first idea if available
  const firstIdea = campaignIdeas && campaignIdeas.length > 0 ? campaignIdeas[0] : undefined;
  
  // State management
  const [selectedIdea, setSelectedIdea] = useState<CampaignIdea | undefined>(firstIdea);
  const [brief, setBrief] = useState<MarketingContentProps | Record<string, never>>(
    (firstIdea?.brief as MarketingContentProps) || {}
  );
  const [briefLoading, setBriefLoading] = useState(false);
  
  // New state for selections with proper type assertions
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(
    Array.isArray(firstIdea?.languages) ? (firstIdea.languages as string[]) : []
  );
  const [selectedChannels, setSelectedChannels] = useState<string[]>(
    Array.isArray(firstIdea?.channels) ? (firstIdea.channels as string[]) : []
  );
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(
    Array.isArray(firstIdea?.content_types) ? (firstIdea.content_types as string[]) : []
  );
  const editor = useCreateBlockNote({});
  // Create handlers using the shared utility functions
  const handleLanguageSelect = createLanguageHandler(selectedLanguages, setSelectedLanguages, selectedIdea);
  const handleChannelSelect = createChannelHandler(selectedChannels, setSelectedChannels, selectedIdea);
  const handleContentTypeChange = createContentTypeHandler(selectedContentTypes, setSelectedContentTypes, selectedIdea);

  // Generate brief on page load if no brief exists and selections are made
  console.log({briefDFD: brief});
  useEffect(() => {
    if (selectedIdea && 
        (!selectedIdea.brief || Object.keys(selectedIdea.brief || {}).length === 0) &&
        selectedLanguages.length > 0 &&
        selectedChannels.length > 0 &&
        selectedContentTypes.length > 0) {
      generateBrief();
    } else if (selectedIdea?.brief && Object.keys(selectedIdea.brief).length > 0) {
      setBrief(selectedIdea.brief as MarketingContentProps);
    }
  }, [selectedIdea, selectedLanguages, selectedChannels, selectedContentTypes]);

  const generateBrief = async () => {
    console.log('Generating!!!! brief');
    if (!selectedIdea) {
      toast.error('No idea selected');
      return;
    }

    // Validate selections
    if (selectedLanguages.length === 0) {
      toast.error('Please select at least one language');
      return;
    }
    if (selectedChannels.length === 0) {
      toast.error('Please select at least one channel');
      return;
    }
    if (selectedContentTypes.length === 0) {
      toast.error('Please select at least one content type');
      return;
    }

    try {
      setBriefLoading(true);
      
      // Make sure we have at least the idea content available
      if (!selectedIdea.content || selectedIdea.content.trim() === '') {
        toast.error('The selected idea has no content');
        setBriefLoading(false);
        return;
      }
      
      // Set defaults for API fields to prevent validation errors
      const brandName = workspace.account.name || 'Company Name';
      const brandBrief = brand.data ? extractBrandBrief(brand.data) : 'Brief Not Provided';
      const campaignBrief = extractCampaignBrief(campaign) || 'No objective provided';
      console.log({brandName});
      // Safely handle documents array
      const productInfo = Array.isArray(campaign.documents) 
        ? campaign.documents
            .map((doc: any) => `${doc.title}: ${doc.content}`)
            .join('\n\n')
        : "No Product Info Provided";

      const response = await fetch('/api/ai/brief', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_name: brandName,
          brand_brief: brandBrief,
          campaign_brief: campaignBrief,
          product_info: productInfo,
          idea: selectedIdea.content,
          languages: selectedLanguages,
          supported_content_types: selectedContentTypes,
          supported_channels: selectedChannels,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate brief');
      }
 
      const data = await response.json();
      console.log({data});
      const briefBlocks = await editor.tryParseMarkdownToBlocks(jsonToMarkdown(data.creative_brief));
      console.log({briefBlocks});
      
      if (selectedIdea?.id) {
        await upsertCampaignIdea({
          id: selectedIdea.id,
          brief: data.creative_brief,
          brief_blocks: briefBlocks
        });
      }
      setBrief(data.creative_brief);
    
    } catch (error) {
      console.error('Error generating brief:', error);
      toast.error('Failed to generate brief');
    } finally {
      setBriefLoading(false);
    }
  };

  const handleIdeaSelect = (idea: CampaignIdea) => {
    console.log('handleIdeaSelect@EKBWD', idea);
    
    // Reset loading state when switching ideas
    setBriefLoading(false);
    setSelectedIdea(idea);
    
    // Update selections with the new idea's values
    setSelectedLanguages(Array.isArray(idea?.languages) ? (idea.languages as string[]) : []);
    setSelectedChannels(Array.isArray(idea?.channels) ? (idea.channels as string[]) : []);
    setSelectedContentTypes(Array.isArray(idea?.content_types) ? (idea.content_types as string[]) : []);
    
    // Update brief with the new idea's brief
    if (idea?.brief && Object.keys(idea.brief).length > 0) {
      setBrief(idea.brief as MarketingContentProps);
    } else {
      // Clear brief if the new idea doesn't have one
      setBrief({});
      
   

        // Small timeout to ensure state updates are complete
        setTimeout(() => {
          generateBrief();
        }, 100);

    }
  };

  // Make content tasks and campaign ideas available via context for the bottom nav
  const { 
    setCampaignIdeas: setContextCampaignIdeas,
    setSelectedIdeaIds
  } = useCampaignContext();
  
  // Store campaign ideas in context
  useEffect(() => {
    setContextCampaignIdeas(campaignIdeas);
    // Initialize selected ideas with the current selected idea
    if (selectedIdea?.id) {
      setSelectedIdeaIds([selectedIdea.id]);
    }
  }, [campaignIdeas, selectedIdea, setContextCampaignIdeas, setSelectedIdeaIds]);

  return (
    <div className="space-y-8">
      {/* Idea Selection Section */}
      <IdeaSelector
        campaignIdeas={campaignIdeas}
        selectedIdea={selectedIdea}
        onIdeaSelect={handleIdeaSelect}
      />

      <Separator className="my-8" />

      {/* Selection Components */}
      {(campaignIdeas && campaignIdeas.length > 0) && 
      <><div className="space-y-6">
        <div className="space-y-4">
          <Label className="text-lg font-medium">Distribution Channels</Label>
          <ChannelSelect 
            selectedChannels={selectedChannels}
            onChannelSelect={handleChannelSelect}
          />
        </div>

        <div className="space-y-4">
          <Label className="text-lg font-medium">Content Types</Label>
          <ContentTypeSelect 
            selectedContentTypes={selectedContentTypes}
            onContentTypeChange={handleContentTypeChange}
          />
        </div>

        <div className="space-y-4">
          <Label className="text-lg font-medium">Target Languages</Label>
          <LanguageSelect 
            selectedLanguages={selectedLanguages}
            onLanguageSelect={handleLanguageSelect}
          />
        </div>
      </div>

      <div className="flex justify-end">
        <Button 
          onClick={() => generateBrief()}
          disabled={selectedLanguages.length === 0 || selectedChannels.length === 0 || selectedContentTypes.length === 0}
        >
          {briefLoading ? 'Generating...' : Object.keys(brief).length > 0 ? 'Re-generate Brief' : 'Generate Brief'}
        </Button>
      </div>
    
      {/* Brief Display Section */}
      <BriefDisplay
        brief={brief as MarketingContentProps}
        briefBlocks={selectedIdea?.brief_blocks}
        campaignIdeaId={selectedIdea?.id}
        isLoading={briefLoading}
      />
      </>
    }
    </div>
    
  );
} 