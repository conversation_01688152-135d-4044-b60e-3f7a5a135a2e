import { Suspense } from 'react';
import { getCampaignBySlug } from '~/services/campaign';
import { getCampaignIdeas } from '~/services/campaign-idea';
import { getCompanyContent } from '~/services/company-content';
import { notFound } from 'next/navigation';

import { Skeleton } from '@kit/ui/skeleton';
import CreativeBriefSection from './_components/creative-brief-section';

interface CreativeBriefPageProps {
  params: {
    account: string;
    slug: string;
  };
}

export default async function CreativeBriefPage({ params }: CreativeBriefPageProps) {
  const { slug } = await params;
  
  // Fetch campaign data
  const campaign = await getCampaignBySlug(slug);
  
  if (!campaign) {
    return notFound();
  }
  
  // Fetch campaign ideas - we'll need to check the correct function for this
  const campaignIdeas = await getCampaignIdeas(campaign.id);

  // Fetch company content
  const companyContent = await getCompanyContent(campaign.id);

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-8">Creative Brief</h1>
      
      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <CreativeBriefSection
          // @ts-expect-error TO DO fix this weird error
          campaign={campaign}
          campaignIdeas={campaignIdeas}
          companyContent={companyContent}
        />
      </Suspense>
    </div>
  );
}