import { MarketingContentProps } from '../_types/brief-types';

/**
 * Safely joins array elements with a separator
 * @param arr The array to join
 * @param separator The separator to use
 * @returns Joined string or empty string if not an array
 */
const safeJoin = (arr: any[] | undefined | null, separator: string = ', '): string => {
  return Array.isArray(arr) && arr.length > 0 ? arr.join(separator) : '';
};

/**
 * Safely maps and joins array elements with a separator and prefix
 * @param arr The array to process
 * @param prefix The prefix to add before each item
 * @returns Joined string with prefix or empty string if not an array
 */
const safeMapJoin = (arr: any[] | undefined | null, prefix: string = '- '): string => {
  return Array.isArray(arr) && arr.length > 0 
    ? arr.map(item => `${prefix}${item}`).join('\n') 
    : '';
};

/**
 * Creates a markdown section only if the data exists
 * @param title The section title
 * @param content The section content
 * @returns Markdown section or empty string
 */
// const createSection = (title: string, content: string | null | undefined): string => {
//   if (!content) return '';
//   return `
// ### ${title}
// ${content}
// `;
// };

/**
 * Generates markdown from structured marketing content
 * @param data Marketing content data
 * @returns Formatted markdown string
 */
const generateMarkdown = (data: MarketingContentProps): string => {
  if (!data) {
    return '## No content available';
  }

  try {
    // Extract from creative_brief wrapper if it exists
    const contentData = data.creative_brief || data;

    if (!contentData) {
      return '## No content available';
    }

    const { 
      key_messaging, 
      target_audience, 
      brand_guidelines, 
      competitive_context, 
      content_specifications
    } = contentData;

    // Determine the outline data location (could be in content_specifications or at the top level)
    const contentOutline = content_specifications?.content_outline || contentData.content_outline;

    let markdown = '';

    // Only add sections if they exist
    if (key_messaging?.headline) {
      markdown += `### Headline\n**${key_messaging.headline}**\n\n`;
    }

    if (Array.isArray(key_messaging?.key_messages) && key_messaging.key_messages.length > 0) {
      markdown += `### Key Messages\n${safeMapJoin(key_messaging.key_messages)}\n\n`;
    }

    if (key_messaging?.call_to_actions) {
      markdown += '### Call to Actions\n';
      if (key_messaging.call_to_actions.primary_cta) {
        markdown += `- **Primary CTA:** [${key_messaging.call_to_actions.primary_cta}](#link-to-registration-page)\n`;
      }
      if (key_messaging.call_to_actions.secondary_cta) {
        markdown += `- **Secondary CTA:** [${key_messaging.call_to_actions.secondary_cta}](#link-to-smartberry-ai-website)\n`;
      }
      markdown += '\n';
    }

    if (key_messaging?.primary_message) {
      markdown += `### Primary Message\n${key_messaging.primary_message}\n\n`;
    }

    if (Array.isArray(key_messaging?.supporting_points) && key_messaging.supporting_points.length > 0) {
      markdown += `### Supporting Points\n${safeMapJoin(key_messaging.supporting_points)}\n\n`;
    }

    if (key_messaging?.value_proposition) {
      markdown += `### Value Proposition\n${key_messaging.value_proposition}\n\n`;
    }

    // Only add Target Audience section if it exists
    if (target_audience) {
      markdown += '---\n\n## Target Audience\n\n';

      if (target_audience.primary_audience) {
        markdown += '### Primary Audience\n\n';

        if (target_audience.primary_audience.demographics) {
          markdown += '#### Demographics\n';
          const demographics = target_audience.primary_audience.demographics;
          if (demographics.gender) markdown += `- **Gender:** ${demographics.gender}\n`;
          if (demographics.location) markdown += `- **Location:** ${demographics.location}\n`;
          if (demographics.age_range) markdown += `- **Age Range:** ${demographics.age_range}\n`;
          if (demographics.occupation) markdown += `- **Occupation:** ${demographics.occupation}\n`;
          markdown += '\n';
        }

        if (target_audience.primary_audience.psychographics) {
          markdown += '#### Psychographics\n';
          const psycho = target_audience.primary_audience.psychographics;
          
          if (Array.isArray(psycho.goals) && psycho.goals.length > 0) {
            markdown += '- **Goals:**\n';
            markdown += psycho.goals.map(goal => `  - ${goal}`).join('\n') + '\n';
          }
          
          if (Array.isArray(psycho.interests) && psycho.interests.length > 0) {
            markdown += '- **Interests:**\n';
            markdown += psycho.interests.map(interest => `  - ${interest}`).join('\n') + '\n';
          }
          
          if (Array.isArray(psycho.pain_points) && psycho.pain_points.length > 0) {
            markdown += '- **Pain Points:**\n';
            markdown += psycho.pain_points.map(point => `  - ${point}`).join('\n') + '\n';
          }
        }
      }

      if (target_audience.content_preferences) {
        markdown += '### Content Preferences\n';
        const prefs = target_audience.content_preferences;
        
        if (prefs.consumption_habits) {
          markdown += `- **Consumption Habits:** ${prefs.consumption_habits}\n`;
        }
        
        if (Array.isArray(prefs.preferred_platforms) && prefs.preferred_platforms.length > 0) {
          markdown += `- **Preferred Platforms:** ${prefs.preferred_platforms.join(', ')}\n`;
        }
        
        markdown += '\n';
      }
    }

    // Only add Brand Guidelines section if it exists
    if (brand_guidelines) {
      markdown += '---\n\n## Brand Guidelines\n\n';

      if (brand_guidelines.visual_identity) {
        markdown += '### Visual Identity\n';
        const visual = brand_guidelines.visual_identity;
        
        if (visual.typography) markdown += `- **Typography:** ${visual.typography}\n`;
        if (visual.color_palette) markdown += `- **Color Palette:** ${visual.color_palette}\n`;
        if (visual.imagery_style) markdown += `- **Imagery Style:** ${visual.imagery_style}\n`;
        
        markdown += '\n';
      }

      if (brand_guidelines.language_guidelines) {
        markdown += '### Language Guidelines\n';
        const lang = brand_guidelines.language_guidelines;
        
        if (Array.isArray(lang.terminology) && lang.terminology.length > 0) {
          markdown += '- **Terminology:**\n';
          markdown += lang.terminology.map(term => `  - ${term}`).join('\n') + '\n';
        }
        
        if (Array.isArray(lang.phrases_to_avoid) && lang.phrases_to_avoid.length > 0) {
          markdown += '- **Phrases to Avoid:**\n';
          markdown += lang.phrases_to_avoid.map(phrase => `  - ${phrase}`).join('\n') + '\n';
        }
      }
    }

    // Only add Competitive Context section if it exists
    if (competitive_context) {
      markdown += '---\n\n## Competitive Context\n\n';

      if (Array.isArray(competitive_context.key_competitors) && competitive_context.key_competitors.length > 0) {
        markdown += '### Key Competitors\n';
        markdown += safeMapJoin(competitive_context.key_competitors) + '\n\n';
      }

      if (Array.isArray(competitive_context.differentiation_points) && competitive_context.differentiation_points.length > 0) {
        markdown += '### Differentiation Points\n';
        markdown += safeMapJoin(competitive_context.differentiation_points) + '\n';
      }
    }

    // Only add Content Specifications section if it exists
    if (content_specifications) {
      markdown += '---\n\n## Content Specifications\n\n';

      if (content_specifications.visuals) {
        markdown += '### Visuals\n';
        const visuals = content_specifications.visuals;
        
        if (Array.isArray(visuals.graphics) && visuals.graphics.length > 0) {
          markdown += '- **Graphics:**\n';
          markdown += visuals.graphics.map(graphic => `  - ${graphic}`).join('\n') + '\n';
        }
        
        if (Array.isArray(visuals.brand_assets) && visuals.brand_assets.length > 0) {
          markdown += '- **Brand Assets:**\n';
          markdown += visuals.brand_assets.map(asset => `  - ${asset}`).join('\n') + '\n';
        }
        
        if (Array.isArray(visuals.required_images) && visuals.required_images.length > 0) {
          markdown += '- **Required Images:**\n';
          markdown += visuals.required_images.map(image => `  - ${image}`).join('\n') + '\n';
        }
      }

      if (Array.isArray(content_specifications.channels) && content_specifications.channels.length > 0) {
        markdown += `### Channels\n${safeJoin(content_specifications.channels, ', ')}\n\n`;
      }

      if (Array.isArray(content_specifications.key_themes) && content_specifications.key_themes.length > 0) {
        markdown += `### Key Themes\n${safeJoin(content_specifications.key_themes, ', ')}\n\n`;
      }

      // Handle SEO keywords - check both possible property names
      const seoKeywords = Array.isArray(content_specifications['SEO keywords']) 
        ? content_specifications['SEO keywords'] 
        : (Array.isArray(content_specifications.SEO_keywords) 
            ? content_specifications.SEO_keywords 
            : null);
            
      if (seoKeywords && seoKeywords.length > 0) {
        markdown += `### SEO Keywords\n${safeJoin(seoKeywords, ', ')}\n\n`;
      }

      if (Array.isArray(content_specifications.content_types) && content_specifications.content_types.length > 0) {
        markdown += `### Content Types\n${safeJoin(content_specifications.content_types, ', ')}\n\n`;
      }

      if (Array.isArray(content_specifications.content_langauges) && content_specifications.content_langauges.length > 0) {
        markdown += `### Content Languages\n${safeJoin(content_specifications.content_langauges, ', ')}\n\n`;
      }

      if (content_specifications.format_details) {
        markdown += '### Format Details\n';
        const format = content_specifications.format_details;
        
        if (format.length) markdown += `- **Length:**\n  - ${format.length}\n`;
        if (format.structure) markdown += `- **Structure:**\n  - ${format.structure}\n`;
        
        markdown += '\n';
      }

      if (content_specifications.tone_and_style) {
        markdown += '### Tone and Style\n';
        const tone = content_specifications.tone_and_style;
        
        if (tone.voice) markdown += `- **Voice:** ${tone.voice}\n`;
        
        if (Array.isArray(tone.style_guidelines) && tone.style_guidelines.length > 0) {
          markdown += '- **Style Guidelines:**\n';
          markdown += tone.style_guidelines.map(guideline => `  - ${guideline}`).join('\n') + '\n';
        }
        
        markdown += '\n';
      }

      if (content_specifications.content_volume) {
        markdown += '### Content Volume\n';
        const volume = content_specifications.content_volume;
        
        if (volume.frequency) markdown += `- **Frequency:** ${volume.frequency}\n`;
        if (volume.pieces_per_frequency !== undefined) markdown += `- **Pieces per Frequency:** ${volume.pieces_per_frequency}\n`;
        if (volume.total_period) markdown += `- **Total Period:** ${volume.total_period}\n`;
        if (volume.total_pieces !== undefined) markdown += `- **Total Pieces:** ${volume.total_pieces}\n`;
        
        markdown += '\n';
      }

      if (content_specifications.publishing_window) {
        markdown += '### Publishing Window\n';
        const window = content_specifications.publishing_window;
        
        if (window.start_date) markdown += `- **Start Date:** ${window.start_date}\n`;
        if (window.end_date) markdown += `- **End Date:** ${window.end_date}\n`;
        
        markdown += '\n';
      }

      // Handle marketing_content_formula - check if it's an array
      if (Array.isArray(content_specifications.marketing_content_formula) && 
          content_specifications.marketing_content_formula.length > 0) {
        markdown += '### Marketing Content Formula\n';
        markdown += safeMapJoin(content_specifications.marketing_content_formula) + '\n';
      }
    }

    // Only add Content Outline section if it exists
    if (contentOutline) {
      markdown += '---\n\n## Content Outline\n\n';

      if (contentOutline.introduction) {
        markdown += `### Introduction\n${contentOutline.introduction}\n\n`;
      }

      if (Array.isArray(contentOutline.sections) && contentOutline.sections.length > 0) {
        contentOutline.sections.forEach(section => {
          if (section.title) {
            markdown += `### ${section.title}\n\n`;
            
            if (Array.isArray(section.key_points) && section.key_points.length > 0) {
              markdown += `${safeMapJoin(section.key_points)}\n\n`;
            }
            
            if (Array.isArray(section.examples) && section.examples.length > 0) {
              markdown += '**Examples:**\n';
              markdown += safeMapJoin(section.examples) + '\n\n';
            }
          }
        });
      }

      if (contentOutline.conclusion) {
        markdown += `### Conclusion\n${contentOutline.conclusion}\n`;
      }
    }

    return markdown;
  } catch (error) {
    console.error('Error in generateMarkdown:', error);
    return '## Error generating content\nThere was an error processing the content. Please try again.';
  }
};

/**
 * Converts JSON marketing content to markdown format
 * @param params Object containing the marketing content data
 * @returns Formatted markdown string
 */
export const jsonToMarkdown = ({ data }: { data: MarketingContentProps }): string => {
  if (!data) {
    return '## No content available';
  }
  
  try {
    return generateMarkdown(data);
  } catch (error) {
    // Log with specific error information
    console.error('Error generating markdown:', error instanceof Error ? error.message : 'Unknown error');
    return '## Error generating content\nThere was an error processing the content. Please try again.';
  }
}; 