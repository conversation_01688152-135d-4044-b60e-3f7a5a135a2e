'use client';

import React, { useState } from 'react';
import { LightbulbIcon } from 'lucide-react';

interface EmptyStateProps {
  campaignId: string;
  companyId: string;
  onAddIdea: () => void;
}

const EmptyState = ({
  onAddIdea,
}: EmptyStateProps) => {
  const [loading, _setLoading] = useState(false);

  const handleAddIdea = async () => {
    onAddIdea();
  };

  if (loading) return null;

  return (
    <div className="flex max-w-md grow flex-col items-center justify-center">
      <div className="flex flex-col">
        <div className="flex p-4">
          <LightbulbIcon size={64} />
        </div>
        <div className="align-center items-center justify-center p-4">
          <h1 className="text-md font-bold">No ideas found</h1>
          <p>Start generating ideas by clicking the button below</p>
        </div>
      </div>
      <div className="flex flex-col items-center justify-center"> 
        <button
          onClick={handleAddIdea}
          disabled={loading}
          className="inline-flex items-center rounded-lg bg-black px-4 py-2 text-sm font-medium text-white transition-colors duration-150 hover:bg-black disabled:opacity-50 cursor-pointer"
        >
          {loading ? 'Adding...' : 'Add Idea'}
        </button>
      </div>
    </div>
  );
};

export default EmptyState;
