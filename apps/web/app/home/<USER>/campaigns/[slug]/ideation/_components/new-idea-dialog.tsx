import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@kit/ui/dialog";
import { But<PERSON> } from "@kit/ui/button";
import { Loader2 } from "lucide-react";

interface NewIdeaDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onManualCreate: () => void;
  onAIGenerate: () => void;
  isGenerating: boolean;
}

export default function NewIdeaDialog({
  isOpen,
  onClose,
  onManualCreate,
  onAIGenerate,
  isGenerating,
}: NewIdeaDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Idea</DialogTitle>
          <DialogDescription>
            Choose how you want to create your new campaign idea
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-4 py-4">
          <Button
            
            onClick={onManualCreate}
            disabled={isGenerating}
          >
            Enter Manually
          </Button>
          <Button
            onClick={onAIGenerate}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              "Generate with AI"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 