'use client';

import SelectableItems from './selectable-items';

// Top 10 most spoken languages in the world
const POPULAR_LANGUAGES = [
  { code: 'English', name: 'English' },
  { code: 'Chinese', name: 'Chinese' },
  { code: 'Hindi', name: 'Hindi' },
  { code: 'Finnish', name: 'Finnish' },
  { code: 'Spanish', name: 'Spanish' },
  { code: 'French', name: 'French' },
  { code: 'Arabic', name: 'Arabic' },
  { code: 'Portuguese', name: 'Portuguese' },
] as const;

type LanguageSelectProps = {
  selectedLanguages: string[];
  onLanguageSelect: (languageCode: string) => Promise<void>;
};

export default function LanguageSelect({ selectedLanguages, onLanguageSelect }: LanguageSelectProps) {
  return (
    <SelectableItems
      title="Select Languages"
      description="Choose from popular languages to add to your campaign."
      items={POPULAR_LANGUAGES}
      selectedItems={selectedLanguages || []} // Ensure we always pass an array
      onItemSelect={onLanguageSelect}
    />
  );
}

export { POPULAR_LANGUAGES }; 