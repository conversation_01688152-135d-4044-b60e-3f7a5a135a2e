/**
 * Formats a campaign idea's content from JSON to a markdown string
 * @param content The content string that might be JSON
 * @returns Formatted markdown string or original content if not JSON
 */
export const formatCampaignIdeaContent = (content: string): string => {
    try {
      const parsedContent = JSON.parse(content);
      const formattedContent = `# ${parsedContent.idea_title}

  ## Key Insight
  ${parsedContent.key_insight}

  ## Main Message
  ${parsedContent.main_message}

  ## Emotional Hook
  ${parsedContent.emotional_hook}

  ## Idea Description
  ${parsedContent.idea_description}

  ## Execution Approach
  ${parsedContent.execution_approach}

  ## Campaign Ratings
  - **Brand Fit**: ${parsedContent.ratings.brand_fit}/5
  - **Feasibility**: ${parsedContent.ratings.feasibility}/5
  - **Originality**: ${parsedContent.ratings.originality}/5
  - **Potential Impact**: ${parsedContent.ratings.potential_impact}/5`;
      return formattedContent;
    } catch {
      // If parsing fails, return the content as is
      return content;
    }
  };