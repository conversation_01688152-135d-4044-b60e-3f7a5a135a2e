import { Badge } from "@kit/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@kit/ui/card";
import { cn } from "@kit/ui/utils";
import { ContentSectionProps } from "../_types/component-types";

export default function ContentGrid({ 
    content, 
    contentTypes,
    activeContentType, 
    onContentTypeChange 
}: ContentSectionProps) {
    // Early return if we don't have the necessary data
    if(!content || !contentTypes || content.length === 0) {
        return null;
    }
    return (
        <div className="flex flex-col gap-4">
            <h1>Content</h1>
            
            <div className="border-b flex gap-2">
                {(contentTypes) && contentTypes.map((type) => (
                    <button
                        key={type}
                        onClick={() => onContentTypeChange(type)}
                        className={cn(
                            "px-4 py-2 relative",
                            "hover:text-foreground/80 transition-colors",
                            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                            activeContentType === type && "text-foreground",
                            activeContentType !== type && "text-muted-foreground"
                        )}
                    >
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                        {activeContentType === type && (
                            <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-foreground" />
                        )}
                    </button>
                ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {(content) && content.map((content) => (
                    <Card key={content.id} className="hover:shadow-md transition-shadow">
                        <CardHeader>
                            <div className="flex flex-col gap-2 items-center justify-between">
                                <CardTitle className="text-lg font-semibold">
                                    {content.task_title}
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <p className="text-muted-foreground text-sm">
                                {content.task_description}
                            </p>
                            <Badge className="text-xs m-4" variant={"default"}>
                                    <span className="text-xs"> {content.content_type}</span>
                                </Badge>
                        </CardContent>
                    </Card>
                ))}
                {content.length === 0 && (
                    <div className="col-span-full text-center py-8 text-muted-foreground">
                        No content found for the selected filter.
                    </div>
                )}
            </div>
        </div>
    );
} 