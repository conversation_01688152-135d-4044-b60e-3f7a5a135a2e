import { MarketingContentProps } from '../_types/brief-types';

/**
 * Generates markdown from structured marketing content
 * @param data Marketing content data
 * @returns Formatted markdown string
 */

// Helper function to safely join arrays
const safeJoin = (arr: unknown[] | unknown, separator: string = '\n- '): string => {
  if (!arr) return 'None specified';
  if (!Array.isArray(arr)) return 'Invalid format';
  if (arr.length === 0) return 'None specified';
  return arr.join(separator);
};

const generateMarkdown = (data: MarketingContentProps): string => {
  if (!data) {
    return '## No content available';
  }

  const { 
    key_messaging, 
    target_audience, 
    brand_guidelines, 
    competitive_context, 
    content_specifications, 
    content_outline
  } = data;

  return `
## Key Messaging

### Headline
**${key_messaging?.headline ?? 'No headline provided'}**

### Key Messages
- ${safeJoin(key_messaging?.key_messages)}

### Call to Actions
- **Primary CTA:** [${key_messaging?.call_to_actions?.primary_cta ?? 'N/A'}](#link-to-registration-page)
- **Secondary CTA:** [${key_messaging?.call_to_actions?.secondary_cta ?? 'N/A'}](#link-to-website)

### Primary Message
${key_messaging?.primary_message ?? 'No primary message provided'}

### Supporting Points
- ${safeJoin(key_messaging?.supporting_points)}

### Value Proposition
${key_messaging?.value_proposition ?? 'No value proposition provided'}

---

## Target Audience

### Primary Audience

#### Demographics
- **Gender:** ${target_audience?.primary_audience?.demographics?.gender ?? 'N/A'}
- **Location:** ${target_audience?.primary_audience?.demographics?.location ?? 'N/A'}
- **Age Range:** ${target_audience?.primary_audience?.demographics?.age_range ?? 'N/A'}
- **Occupation:** ${target_audience?.primary_audience?.demographics?.occupation ?? 'N/A'}

#### Psychographics
- **Goals:**
  - ${safeJoin(target_audience?.primary_audience?.psychographics?.goals)}
- **Interests:**
  - ${safeJoin(target_audience?.primary_audience?.psychographics?.interests)}
- **Pain Points:**
  - ${safeJoin(target_audience?.primary_audience?.psychographics?.pain_points)}

### Content Preferences
- **Consumption Habits:** ${target_audience?.content_preferences?.consumption_habits ?? 'N/A'}
- **Preferred Platforms:** ${safeJoin(target_audience?.content_preferences?.preferred_platforms, ', ')}

---

## Brand Guidelines

### Visual Identity
- **Typography:** ${brand_guidelines?.visual_identity?.typography ?? 'N/A'}
- **Color Palette:** ${brand_guidelines?.visual_identity?.color_palette ?? 'N/A'}
- **Imagery Style:** ${brand_guidelines?.visual_identity?.imagery_style ?? 'N/A'}

### Language Guidelines
- **Terminology:**
  - ${safeJoin(brand_guidelines?.language_guidelines?.terminology)}
- **Phrases to Avoid:**
  - ${safeJoin(brand_guidelines?.language_guidelines?.phrases_to_avoid)}

---

## Competitive Context

### Key Competitors
- ${safeJoin(competitive_context?.key_competitors)}

### Differentiation Points
- ${safeJoin(competitive_context?.differentiation_points)}

---

## Content Specifications

### Visuals
- **Graphics:**
  - ${safeJoin(content_specifications?.visuals?.graphics)}
- **Brand Assets:**
  - ${safeJoin(content_specifications?.visuals?.brand_assets)}
- **Required Images:**
  - ${safeJoin(content_specifications?.visuals?.required_images)}

### Channels
- ${safeJoin(content_specifications?.channels, ', ')}

### Key Themes
- ${safeJoin(content_specifications?.key_themes, ', ')}

### SEO Keywords
- ${safeJoin(content_specifications?.SEO_keywords, ', ')}

### Content Types
- ${safeJoin(content_specifications?.content_types, ', ')}

### Format Details
- **Length:**
  - ${content_specifications?.format_details?.length ?? 'N/A'}
- **Structure:**
  - ${content_specifications?.format_details?.structure ?? 'N/A'}

### Tone and Style
- **Voice:** ${content_specifications?.tone_and_style?.voice ?? 'N/A'}
- **Style Guidelines:**
  - ${safeJoin(content_specifications?.tone_and_style?.style_guidelines)}

---

## Content Outline

### Sections

${content_outline?.sections ? content_outline.sections.map(section => `
1. **${section.title ?? 'Untitled Section'}**
   - **Key Points:**
     - ${safeJoin(section.key_points)}
   - **Examples:**
     - ${safeJoin(section.examples)}
`).join('') : 'No sections specified'}

### Conclusion
${content_outline?.conclusion ?? 'No conclusion provided'}

### Introduction
${content_outline?.introduction ?? 'No introduction provided'}

  `;
};

/**
 * Converts JSON marketing content to markdown format
 * @param params Object containing the marketing content data
 * @returns Formatted markdown string
 */
export const jsonToMarkdown = ({ data }: { data: MarketingContentProps }): string => {
  if (!data) {
    return '## No content available';
  }
  
  try {
    return generateMarkdown(data);
  } catch (error) {
    // Log with specific error information
    console.error('Error generating markdown:', error instanceof Error ? error.message : 'Unknown error');
    return '## Error generating content\nThere was an error processing the content. Please try again.';
  }
};