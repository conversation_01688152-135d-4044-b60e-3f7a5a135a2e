import { getCampaignIdeas } from "~/services/campaign-idea";
import IdeaDisplaySection from "./_components/idea-display-section";
import { getCampaignBySlug } from "~/services/campaign";

interface CampaignIdeationPageProps {
  params: { 
    slug: string;
    account: string; 
  };
}

export default async function CampaignIdeationPage({ params }: CampaignIdeationPageProps) {
  try {
    const campaign = await getCampaignBySlug((await params).slug);
    
    if (!campaign?.id || !campaign?.company_id) {
      throw new Error('Campaign not found');
    }

    const campaignIdeas = await getCampaignIdeas(campaign.id);
    console.log("CAMPAIGN IDEAS", campaignIdeas);
    return (
      <div>
        <IdeaDisplaySection 
          campaignIdeas={campaignIdeas}
          campaign={campaign}
        />
      </div>
    );
  } catch (error) {
    console.error('Error loading campaign ideation page:', error);
    return (
      <div className="container py-10">
        <h2 className="text-2xl font-semibold mb-4">Error Loading Campaign</h2>
        <p>There was an error loading the campaign. Please try again later.</p>
      </div>
    );
  }
}
