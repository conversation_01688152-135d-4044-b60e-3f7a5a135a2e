import { CampaignIdea } from "~/types/campaign-idea";
import { CompanyContent } from "~/types/company-content";
import { Campaign } from "~/types/Campaign";
import { MarketingContentProps } from "./brief-types";
import { Block } from "@blocknote/core";

export interface IdeaDisplaySectionProps {
  campaignIdeas: CampaignIdea[];
  campaign: Campaign;
}

export interface IdeaSectionProps {
  campaignIdeas: CampaignIdea[];
  selectedIdea?: CampaignIdea;
  onIdeaSelect: (idea: CampaignIdea) => void;
  onOpenNewIdeaDialog: () => void;
}

export interface IdeaDetailsProps {
  isRefining: boolean;
  onRefineIdea: () => Promise<void>;
  selectedIdea?: CampaignIdea;
  selectedLanguages: string[];
  selectedChannels: string[];
  selectedContentTypes: string[];
  selectedIdeaContent: string;
  selectedIdeaContentBlocks: Block[];
  onTextareaChange: (content: string, contentBlocks: Block[]) => void;
  onLanguageSelect: (languageCode: string) => Promise<void>;
  onChannelSelect: (channelCode: string) => Promise<void>;
  onContentTypeChange: (contentTypeCode: string) => Promise<void>;
}

export interface BriefSectionProps {
  brief: MarketingContentProps | Record<string, never>;
  tasksLoading: boolean;
  onGenerateContent: () => Promise<void>;
}

export interface ContentSectionProps {
  companyContent: CompanyContent[];
  selectedContentTypes: string[];
  activeContentType: string;
  onContentTypeChange: (type: string) => Promise<void>;
}

export interface Item {
  code: string;
  name: string;
}

export interface SelectableItemsProps {
  title: string;
  description: string;
  items: readonly Item[];
  selectedItems: string[];
  onItemSelect: (code: string) => Promise<void>;
}