'use client';

import SelectableItems from './selectable-items';

// Popular marketing channels
const POPULAR_CHANNELS = [
  // { code: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
  { code: 'LinkedIn', name: 'LinkedIn' },
  { code: 'Instagram', name: 'Instagram' },
  { code: 'Twitter/X', name: 'Twitter/X' },
  { code: 'Blogs', name: 'Blogs' },
] as const;

type ChannelSelectProps = {
  selectedChannels: string[];
  onChannelSelect: (channelCode: string) => Promise<void>;
};

export default function ChannelSelect({ selectedChannels, onChannelSelect }: ChannelSelectProps) {
  return (
    <SelectableItems
      title="Select Channels"
      description="Choose marketing channels for your campaign."
      items={POPULAR_CHANNELS}
      selectedItems={selectedChannels || []} // Ensure we always pass an array
      onItemSelect={onChannelSelect}
    />
  );
}

export { POPULAR_CHANNELS }; 