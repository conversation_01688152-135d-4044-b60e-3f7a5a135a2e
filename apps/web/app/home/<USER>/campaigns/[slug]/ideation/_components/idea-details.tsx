'use client';
import { useEffect } from "react";
import { But<PERSON> } from "@kit/ui/button";
import { Label } from "@kit/ui/label";
import LanguageSelect from "./language-select";
import ChannelSelect from "./channel-select";
import ContentTypeSelect from "./content-type-select";
import { IdeaDetailsProps } from "../_types/component-types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@kit/ui/alert-dialog";
import { deleteCampaignIdea } from "~/services/campaign-idea";
import { toast } from "sonner";
import "@blocknote/core/fonts/inter.css";
import "@blocknote/mantine/style.css";
import { useCreateBlockNote } from "@blocknote/react";
import { BlockNoteView } from "@blocknote/mantine";
import { useTheme } from "next-themes";


export default function IdeaDetails({
  isRefining,
  onRefineIdea,
  selectedIdea,
  selectedLanguages,
  selectedChannels,
  selectedContentTypes,
  onTextareaChange,
  onLanguageSelect,
  onChannelSelect,
  onContentTypeChange,
}: IdeaDetailsProps) {
  
  // Format the content using the extracted function

  const editor = useCreateBlockNote();
  const {theme} = useTheme();
  useEffect(() => {
    async function loadInitialHTML() {
      const blocks = await editor.tryParseMarkdownToBlocks(selectedIdea?.content || '');
      editor.replaceBlocks(editor.document, blocks);
    }
    //if the content blocks are not empty, replace the blocks

    if (selectedIdea?.content_blocks && selectedIdea?.content_blocks?.length > 0) {
      editor.replaceBlocks(editor.document, selectedIdea?.content_blocks);
    } else {
      //if there's is no content blocks, likely its using the old format and we should to blocks (evenutally we can remove this check)
      loadInitialHTML();
    }
  }, [editor, selectedIdea]);
 
  const handleDelete = async () => {
    try {
      if (!selectedIdea?.id) {
        throw new Error('No idea selected');
      }
      await deleteCampaignIdea(selectedIdea.id);
      toast.success('Idea deleted successfully');
      window.location.reload();
    } catch (error) {
      console.error('Error deleting idea:', error);
      toast.error('Failed to delete idea');
    }
  };

  const handleContentChange = async (content: any) => {

    // convert into markdown
    const markdownContent = await editor.blocksToMarkdownLossy(content);

    onTextareaChange(markdownContent, content);
  }
  return (
    <section>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold tracking-tight">Idea Details</h2>
          <div className="flex gap-4">
            <Button variant="secondary" disabled={isRefining} onClick={onRefineIdea}>
              {isRefining ? 'Refining...' : 'Refine with AI'}
            </Button>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" disabled={!selectedIdea}>
                  Delete Idea
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete this campaign idea.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
        {/* <Textarea 
          value={selectedIdeaContent} 
          className="min-h-[200px] font-mono"
          onChange={onTextareaChange}
          placeholder="Enter your campaign idea here..."
        /> */}
        <BlockNoteView 
          theme={theme as "light" | "dark"}
          editor={editor} 
          onChange={() => handleContentChange(editor.document)}
        />
        {/* Channel Selection */}
        <div className="space-y-4">
          <Label className="text-lg font-medium">Distribution Channels</Label>
          <ChannelSelect 
            selectedChannels={selectedChannels}
            onChannelSelect={onChannelSelect}
          />
        </div>

        {/* Content Type Selection */}
        <div className="space-y-4">
          <Label className="text-lg font-medium">Content Types</Label>
          <ContentTypeSelect 
            selectedContentTypes={selectedContentTypes}
            onContentTypeChange={onContentTypeChange}
          />
        </div>

        {/* Language Selection */}
        <div className="space-y-4">
          <Label className="text-lg font-medium">Target Languages</Label>
          <LanguageSelect 
            selectedLanguages={selectedLanguages}
            onLanguageSelect={onLanguageSelect}
          />
        </div>
      </div>
    </section>
  );
}