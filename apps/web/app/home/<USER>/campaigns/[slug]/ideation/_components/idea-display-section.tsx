'use client';

import { useState, useCallback, useEffect } from "react";
import { Separator } from "@kit/ui/separator";
import { CampaignIdea } from "~/types/campaign-idea";
import { createCampaignIdea } from "~/services/campaign-idea";
import debounce from 'lodash/debounce';
import { toast } from 'sonner';
import NewIdeaDialog from "./new-idea-dialog";
import { upsertCampaignIdea } from "~/services/campaign";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { IdeaDisplaySectionProps } from "../_types/component-types";
import IdeaSection from "./idea-section";
import IdeaDetails from "./idea-details";
import EmptyState from "./empty-state";
import { extractBrandBrief, extractCampaignBrief } from "~/utils/brief.util";
import { useBrandData } from "~/hooks/use-brand-data";
import { formatCampaignIdeaContent } from "../_lib/utils";
import { Block } from "@blocknote/core";
import { useCreateBlockNote } from "@blocknote/react";
import { create<PERSON>anguage<PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, create<PERSON>ontentTypeHandler } from '../../_utils/selection-handlers';
import { POPULAR_LANGUAGES } from "./language-select";
import { POPULAR_CHANNELS } from "./channel-select";
import { CONTENT_TYPES } from "./content-type-select";

export default function IdeaDisplaySection({ 
  campaignIdeas,
  campaign,
}: IdeaDisplaySectionProps) {
  const workspace = useTeamAccountWorkspace();
  const brand = useBrandData(workspace.account.id);
  const editor = useCreateBlockNote();
  
  // Sort ideas by creation time or ID for consistent ordering
  const sortedIdeas = [...campaignIdeas].sort((a, b) => {
    if (a.created_at && b.created_at) {
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    }
    return (a.id || '').localeCompare(b.id || '');
  });
  
  // Safely get the first idea if available
  const firstIdea = sortedIdeas.length > 0 ? sortedIdeas[0] : undefined;
  
  // State management
  const [selectedIdea, setSelectedIdea] = useState<CampaignIdea | undefined>(firstIdea);
  const [isNewIdeaDialogOpen, setIsNewIdeaDialogOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  const [selectedIdeaContent, setSelectedIdeaContent] = useState<string>(
    firstIdea?.content || ''
  );
  const [selectedIdeaContentBlocks, setSelectedIdeaContentBlocks] = useState<any>(
    firstIdea?.content_blocks || []
  );

  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(
    Array.isArray(firstIdea?.languages) ? firstIdea.languages as string[] : []
  );
  const [selectedChannels, setSelectedChannels] = useState<string[]>(
    Array.isArray(firstIdea?.channels) ? firstIdea.channels as string[] : []
  );
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(
    Array.isArray(firstIdea?.content_types) ? firstIdea.content_types as string[] : []
  );
  
  // Update state when selected idea changes
  useEffect(() => {
    if (selectedIdea) {
      setSelectedIdeaContent(selectedIdea.content || '');
      setSelectedLanguages(Array.isArray(selectedIdea.languages) ? selectedIdea.languages as string[] : []);
      setSelectedChannels(Array.isArray(selectedIdea.channels) ? selectedIdea.channels as string[] : []);
      setSelectedContentTypes(Array.isArray(selectedIdea.content_types) ? selectedIdea.content_types as string[] : []);
    }
  }, [selectedIdea]);
  
  // Debounced function to update idea content
  const debouncedUpdateIdea = useCallback(
    debounce(async (id: string, content: string, contentBlocks: Block[]) => {
      try {
        await upsertCampaignIdea({
          id,
          content,
          content_blocks: contentBlocks
        });
      } catch (error) {
        console.error('Error updating idea:', error);
      }
    }, 1000),
    []
  );

  // Create handlers using the shared utility functions
  const handleLanguageSelect = createLanguageHandler(selectedLanguages, setSelectedLanguages, selectedIdea);
  const handleChannelSelect = createChannelHandler(selectedChannels, setSelectedChannels, selectedIdea);
  const handleContentTypeChange = createContentTypeHandler(selectedContentTypes, setSelectedContentTypes, selectedIdea);
  
  // Handle textarea change
  const handleTextareaChange = (content: string, contentBlocks: Block[]) => {
    setSelectedIdeaContent(content);
    setSelectedIdeaContentBlocks(contentBlocks);

    // Update in database if we have a selected idea
    if (selectedIdea?.id) {
      debouncedUpdateIdea(selectedIdea.id, content, contentBlocks);
    }
  };
  
  // Create a new idea
  const createNewIdea = async (content: string = '') => {
    try {
      // Get the first option from each category
      const defaultLanguage = POPULAR_LANGUAGES[0]?.code || '';
      const defaultChannel = POPULAR_CHANNELS[0]?.code || '';
      const defaultContentType = CONTENT_TYPES[0]?.code || '';

      const newIdea = await createCampaignIdea({
        campaign_id: campaign.id,
        company_id: campaign.company_id,
        content: content ? formatCampaignIdeaContent(JSON.stringify(content)) : '',
        content_blocks: content ? await editor.tryParseMarkdownToBlocks(formatCampaignIdeaContent(JSON.stringify(content))) : [],
        metadata: content,
        //@ts-expect-error TODO: fix this, when idea is generated with AI, it will have a title. 
        title: content ? content.idea_title : '',
        is_selected: true,
        languages: [defaultLanguage],
        brief: null,
        content_types: [defaultContentType],
        channels: [defaultChannel],
      }); 

      // Update the selected idea with the new one
      const updatedIdea: CampaignIdea = {
        ...newIdea,
        title: newIdea.title || '',
        content_blocks: newIdea.content_blocks as Block[] || [],
        brief_blocks: newIdea.brief_blocks as Block[] || [],
        languages: Array.isArray(newIdea.languages) ? newIdea.languages as string[] : [defaultLanguage],
        channels: Array.isArray(newIdea.channels) ? newIdea.channels as string[] : [defaultChannel],
        content_types: Array.isArray(newIdea.content_types) ? newIdea.content_types as string[] : [defaultContentType],
      };
      
      setSelectedIdea(updatedIdea);
      setSelectedIdeaContent(updatedIdea.content || '');
      setSelectedIdeaContentBlocks(updatedIdea.content_blocks || []);
      setSelectedLanguages(Array.isArray(updatedIdea.languages) ? updatedIdea.languages as string[] : [defaultLanguage]);
      setSelectedChannels(Array.isArray(updatedIdea.channels) ? updatedIdea.channels as string[] : [defaultChannel]);
      setSelectedContentTypes(Array.isArray(updatedIdea.content_types) ? updatedIdea.content_types as string[] : [defaultContentType]);

      return updatedIdea;
    } catch (error) {
      console.error('Error creating idea:', error);
      toast.error('Failed to create idea');
      throw error;
    }
  };

  // Handle manual idea creation
  const handleManualCreate = async () => {
    try {
      await createNewIdea();
      setIsNewIdeaDialogOpen(false);
    } catch (error) {
      // Error is already handled in createNewIdea
      console.error('Error creating idea:', error);
    }
  };

  // Handle AI idea generation
  const handleAIGenerate = async () => {
    try {
      setIsGenerating(true);
      
      // Get the first option from each category
      const defaultLanguage = POPULAR_LANGUAGES[0]?.code || '';
      // const defaultChannel = POPULAR_CHANNELS[0]?.code || '';
      // const defaultContentType = CONTENT_TYPES[0]?.code || '';

      const response = await fetch('/api/ai/generate-idea', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaign_brief:  extractCampaignBrief(campaign) || 'No objective provided',
          brand_brief: brand.data ? extractBrandBrief(brand.data) : 'No brand info provided',
          brand_name: workspace.account.name || 'No brand name provided',
          product_info: brand.data?.brand?.product_list || 'No product info provided',
          previous_ideas: campaignIdeas.map((idea) => idea.content) || 'No previous ideas provided',
          language: defaultLanguage || 'English',
        }),
      });

      if (!response.ok) {
        console.error('Failed to generate idea:', response);
        throw new Error('Failed to generate idea');
      }

      const data = await response.json();
      console.log('Generated idea:', data);

      await createNewIdea(data);
      setIsNewIdeaDialogOpen(false);
    } catch (error) {
      console.error('Error generating idea:', error);
      toast.error('Failed to generate idea');
    } finally {
      setIsGenerating(false);
    }
  };
 
  const handleRefineIdea = async () => {
    setIsRefining(true);
    console.log(Object.values(campaign).filter(Boolean).join(' '))
    try {
      // Get the first option from each category if not already selected
      const defaultLanguage = POPULAR_LANGUAGES[0]?.code || '';
      const defaultChannel = POPULAR_CHANNELS[0]?.code || '';
      const defaultContentType = CONTENT_TYPES[0]?.code || '';
      
      // Use current selections or defaults
      const currentLanguages = selectedLanguages.length > 0 ? selectedLanguages : [defaultLanguage];
      const currentChannels = selectedChannels.length > 0 ? selectedChannels : [defaultChannel];
      const currentContentTypes = selectedContentTypes.length > 0 ? selectedContentTypes : [defaultContentType];

      const response = await fetch('/api/ai/rewrite-idea', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          initial_idea: selectedIdea?.content || 'No idea provided',
          campaign_brief: extractCampaignBrief(campaign) || 'No objective provided',
          brand_brief: brand.data ? extractBrandBrief(brand.data) : 'No brand info provided',
          brand_name: workspace.account.name || 'No brand name provided',
          product_info: brand.data?.brand?.product_list || 'No product info provided',
          language: currentLanguages[0] || 'English',
        }),
      });
      const data = await response.json();
      console.log('Refined idea:', data);
      //convert the data to markdown
      const markdownContent = formatCampaignIdeaContent(JSON.stringify(data));
      const contentBlocks = await editor.tryParseMarkdownToBlocks(markdownContent);
      
      //update the idea in the database
      const updatedIdea = await upsertCampaignIdea({
        id: selectedIdea?.id,
        content: markdownContent,
        content_blocks: contentBlocks,
        languages: currentLanguages,
        channels: currentChannels,
        content_types: currentContentTypes
      });

      // Update the state with the refined idea
      if (updatedIdea) {
        const refinedIdea: CampaignIdea = {
          ...updatedIdea,
          title: updatedIdea.title || '',
          content_blocks: contentBlocks,
          brief_blocks: Array.isArray(updatedIdea.brief_blocks) ? updatedIdea.brief_blocks as Block[] : [],
          languages: Array.isArray(updatedIdea.languages) ? updatedIdea.languages as string[] : currentLanguages,
          channels: Array.isArray(updatedIdea.channels) ? updatedIdea.channels as string[] : currentChannels,
          content_types: Array.isArray(updatedIdea.content_types) ? updatedIdea.content_types as string[] : currentContentTypes,
        };
        
        setSelectedIdea(refinedIdea);
        setSelectedIdeaContent(markdownContent);
        setSelectedIdeaContentBlocks(contentBlocks);
        setSelectedLanguages(Array.isArray(refinedIdea.languages) ? refinedIdea.languages as string[] : currentLanguages);
        setSelectedChannels(Array.isArray(refinedIdea.channels) ? refinedIdea.channels as string[] : currentChannels);
        setSelectedContentTypes(Array.isArray(refinedIdea.content_types) ? refinedIdea.content_types as string[] : currentContentTypes);
      }
    } catch (error) {
      console.error('Error refining idea:', error);
      toast.error('Failed to refine idea');
    } finally {
      setIsRefining(false);
    }
  }

  if (campaignIdeas.length === 0) {
    return (
      <div className="flex flex-col mt-50 items-center justify-center">
        <EmptyState onAddIdea={() => setIsNewIdeaDialogOpen(true)} campaignId={campaign.id} companyId={campaign.company_id} />
        <NewIdeaDialog
        isOpen={isNewIdeaDialogOpen}
        onClose={() => setIsNewIdeaDialogOpen(false)}
        onManualCreate={handleManualCreate}
        onAIGenerate={handleAIGenerate}
        isGenerating={isGenerating}
      />
      </div>
    );
  }

  return (
    <div className="container space-y-8">
      <NewIdeaDialog
        isOpen={isNewIdeaDialogOpen}
        onClose={() => setIsNewIdeaDialogOpen(false)}
        onManualCreate={handleManualCreate}
        onAIGenerate={handleAIGenerate}
        isGenerating={isGenerating}
      />
      
      {/* Idea Selection Section */}
      <IdeaSection
        campaignIdeas={campaignIdeas}
        selectedIdea={selectedIdea}
        onIdeaSelect={setSelectedIdea}
        onOpenNewIdeaDialog={() => setIsNewIdeaDialogOpen(true)}
      />

      <Separator className="my-8" />

      {/* Idea Details Section */}
      <IdeaDetails 
        isRefining={isRefining}
        onRefineIdea={handleRefineIdea}
        selectedIdea={selectedIdea}
        selectedLanguages={selectedLanguages}
        selectedChannels={selectedChannels}
        selectedContentTypes={selectedContentTypes}
        selectedIdeaContent={selectedIdeaContent}
        selectedIdeaContentBlocks={selectedIdeaContentBlocks}
        onTextareaChange={handleTextareaChange}
        onLanguageSelect={handleLanguageSelect}
        onChannelSelect={handleChannelSelect}
        onContentTypeChange={handleContentTypeChange}
      />
    </div>
  );
}