export type KeyMessaging = {
  headline: string;
  key_messages: string[];
  call_to_actions: {
    primary_cta: string;
    secondary_cta: string;
  };
  primary_message: string;
  supporting_points: string[];
  value_proposition: string;
};

export type TargetAudience = {
  primary_audience: {
    demographics: {
      gender: string;
      location: string;
      age_range: string;
      occupation: string;
    };
    psychographics: {
      goals: string[];
      interests: string[];
      pain_points: string[];
    };
  };
  content_preferences: {
    consumption_habits: string;
    preferred_platforms: string[];
  };
};

export type BrandGuidelines = {
  visual_identity: {
    typography: string;
    color_palette: string;
    imagery_style: string;
  };
  language_guidelines: {
    terminology: string[];
    phrases_to_avoid: string[];
  };
};

export type CompetitiveContext = {
  key_competitors: string[];
  differentiation_points: string[];
};

export type ContentSpecifications = {
  visuals: {
    graphics: string[];
    brand_assets: string[];
    required_images: string[];
  };
  channels: string[];
  key_themes: string[];
  SEO_keywords: string[];
  content_types: string[];
  format_details: {
    length: string;
    structure: string;
  };
  tone_and_style: {
    voice: string;
    style_guidelines: string[];
  };
};

export type ContentOutline = {
  sections: {
    title: string;
    key_points: string[];
    examples: string[];
  }[];
  conclusion: string;
  introduction: string;
};

export type MarketingContentProps = {
  key_messaging: KeyMessaging;
  target_audience: TargetAudience;
  brand_guidelines: BrandGuidelines;
  competitive_context: CompetitiveContext;
  content_specifications: ContentSpecifications;
  content_outline: ContentOutline;
};