import SelectableItems from './selectable-items';

const CONTENT_TYPES = [
  { code: 'Blog Post', name: 'Blog Post' },
  // { code: 'Video', name: 'Video' },
  { code: 'LinkedIn Post', name: 'LinkedIn Post' },
  // { code: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
  { code: 'Tweet', name: 'Tweet' }
] as const;

type ContentTypeSelectProps = {
  selectedContentTypes: string[];
  onContentTypeChange: (contentTypeCode: string) => Promise<void>;
};

export default function ContentTypeSelect({ selectedContentTypes, onContentTypeChange }: ContentTypeSelectProps) {
  return (
    <SelectableItems
      title="Select Content Types"
      description="Choose content types for your campaign."
      items={CONTENT_TYPES}
      selectedItems={selectedContentTypes || []} // Ensure we always pass an array
      onItemSelect={onContentTypeChange}
    />
  );
}

export { CONTENT_TYPES };