'use client';

import { <PERSON><PERSON> } from "@kit/ui/button";
import { IdeaSectionProps } from "../_types/component-types";

// Helper function to get first few words from content
const getFirstWords = (content: string | null | undefined, wordCount: number = 4): string => {
  if (!content) return 'Untitled Idea';
  
  // Remove markdown and special characters, then get first few words
  const cleanContent = content
    .replace(/[#*_`]/g, '') // Remove markdown syntax
    .replace(/\n/g, ' ')    // Replace newlines with spaces
    .trim();
    
  const words = cleanContent.split(/\s+/);
  const firstWords = words.slice(0, wordCount).join(' ');
  
  return firstWords.length < cleanContent.length 
    ? `${firstWords}...` 
    : firstWords;
};

export default function IdeaSection({ 
  campaignIdeas, 
  selectedIdea, 
  onIdeaSelect, 
  onOpenNewIdeaDialog,
}: IdeaSectionProps) {
  // Sort ideas by creation time (assuming there's a created_at field)
  // If no created_at, fallback to ID for stable ordering
  const sortedIdeas = [...campaignIdeas].sort((a, b) => {
    if (a.created_at && b.created_at) {
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    }
    return (a.id || '').localeCompare(b.id || '');
  });

  return (
    <section>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-semibold tracking-tight">Campaign Ideas</h2>
      </div>
      <div className="flex flex-row gap-4">
        {sortedIdeas.map((idea) => (
          <Button 
            variant={selectedIdea?.id === idea.id ? 'default' : 'outline'} 
            key={idea.id} 
            onClick={() => onIdeaSelect(idea)}
            className="max-w-[200px] truncate"
          >
            <span className="truncate">{getFirstWords(idea.content)}</span>
          </Button>
        ))}
        <Button 
          variant="outline" 
          onClick={onOpenNewIdeaDialog}
        >
          <span>New Idea</span>
        </Button>
      </div>
    </section>
  );
}