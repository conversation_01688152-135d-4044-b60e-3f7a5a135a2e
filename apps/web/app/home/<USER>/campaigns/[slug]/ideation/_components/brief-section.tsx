'use client';

import { <PERSON><PERSON> } from "@kit/ui/button";
import { Textarea } from "@kit/ui/textarea";
import { jsonToMarkdown } from "../_lib/jsonToMarkdown";
import { BriefSectionProps } from "../_types/component-types";

export default function BriefSection({
  brief,
  tasksLoading,
  onGenerateContent,
}: BriefSectionProps) {
  const hasBrief = Object.keys(brief).length > 0;
  
  return (
    <section className="space-y-8">
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold tracking-tight">Campaign Brief</h2>
        <Textarea 
          className="min-h-[250px]" 
          value={hasBrief ? jsonToMarkdown({ data: brief }) : ''} 
          readOnly
        />
      </div>
      <Button 
        disabled={tasksLoading || !hasBrief} 
        variant="default" 
        onClick={onGenerateContent}
      >
        {tasksLoading ? 'Generating...' : 'Generate Content'}
      </Button>
    </section>
  );
}