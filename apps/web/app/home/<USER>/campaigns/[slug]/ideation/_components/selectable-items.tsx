import { Button } from "@kit/ui/button";
import { Drawer, DrawerClose, Drawer<PERSON>ontent, <PERSON>erD<PERSON><PERSON>, <PERSON>er<PERSON>ooter, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from "@kit/ui/drawer";
import { PlusIcon } from "lucide-react";
import { SelectableItemsProps } from "../_types/component-types";

export default function SelectableItems({ 
  title, 
  description, 
  items, 
  selectedItems = [], // provide default empty array
  onItemSelect 
}: SelectableItemsProps) {
  // This component displays both selected items and a drawer to select new items
  
  // Ensure we have a valid array, even if empty
  const safeSelectedItems = Array.isArray(selectedItems) ? selectedItems : [];

  // Handle item click in the drawer to add/remove items
  const handleItemSelect = (code: string) => {

    if (code) {
      
      onItemSelect(code);
    }
  };
  
  return (
    <div className="flex flex-row gap-4 flex-wrap">
      {/* Display already selected items as pills/buttons */}
      {safeSelectedItems.map((code) => {
        const item = items.find((i) => i.code === code);
        if (!item) return null;
        return (
          <Button 
            key={code}
            className="rounded-full" 
            variant="default"
            onClick={() => handleItemSelect(code)}
          >
            {item.name}
          </Button>
        );
      })}
      <Drawer>
        <DrawerTrigger asChild>
          <Button className="rounded-full" variant="secondary">
            <PlusIcon className="w-4 h-4" />
          </Button>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>{title}</DrawerTitle>
            <DrawerDescription>{description}</DrawerDescription>
          </DrawerHeader>
          <div className="p-4 grid grid-cols-2 gap-4">
            {items.map((item) => {
              // Make sure we check if the item is selected in a safe way
              const isSelected = Array.isArray(safeSelectedItems) && 
                safeSelectedItems.includes(item.code);
              
              return (
                <Button
                  key={item.code}
                  variant={isSelected ? "default" : "outline"}
                  className="w-full justify-start"
                  onClick={() => handleItemSelect(item.code)}
                >
                  {item.name}
                </Button>
              );
            })}
          </div>
          <DrawerFooter>
            <DrawerClose asChild>
              <Button variant="outline">Close</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  );
}