'use client';

import { CompanyContent } from '~/types/company-content';
import { CampaignIdea } from '~/types/campaign-idea';
import { createContext, useContext, useState, ReactNode } from 'react';

type CampaignContextType = {
  contentTasks: CompanyContent[];
  setContentTasks: (tasks: CompanyContent[]) => void;
  campaignIdeas: CampaignIdea[];
  setCampaignIdeas: (ideas: CampaignIdea[]) => void;
  selectedIdeaIds: string[];
  setSelectedIdeaIds: (ids: string[]) => void;
  isIdeaSelectionDialogOpen: boolean;
  setIsIdeaSelectionDialogOpen: (isOpen: boolean) => void;
  openIdeaSelectionDialog: () => void;
};

const CampaignContext = createContext<CampaignContextType | undefined>(undefined);

export function CampaignProvider({ children }: { children: ReactNode }) {
  const [contentTasks, setContentTasks] = useState<CompanyContent[]>([]);
  const [campaignIdeas, setCampaignIdeas] = useState<CampaignIdea[]>([]);
  const [selectedIdeaIds, setSelectedIdeaIds] = useState<string[]>([]);
  const [isIdeaSelectionDialogOpen, setIsIdeaSelectionDialogOpen] = useState(false);

  const openIdeaSelectionDialog = () => setIsIdeaSelectionDialogOpen(true);

  return (
    <CampaignContext.Provider 
      value={{ 
        contentTasks, 
        setContentTasks,
        campaignIdeas,
        setCampaignIdeas,
        selectedIdeaIds,
        setSelectedIdeaIds,
        isIdeaSelectionDialogOpen,
        setIsIdeaSelectionDialogOpen,
        openIdeaSelectionDialog,
      }}
    >
      {children}
    </CampaignContext.Provider>
  );
}

export function useCampaignContext() {
  const context = useContext(CampaignContext);
  if (context === undefined) {
    throw new Error('useCampaignContext must be used within a CampaignProvider');
  }
  return context;
}