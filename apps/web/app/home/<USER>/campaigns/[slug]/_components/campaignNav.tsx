'use client';

import { usePathname } from 'next/navigation';
import { cn } from "@kit/ui/utils"
import { Check, Circle } from "lucide-react"
import { useCampaignContext } from './campaign-context';

// Define the campaign steps in the same order as they appear in the navigation
const CAMPAIGN_STEPS = [
  { path: 'overview', label: 'Campaign Setup' },
  { path: 'ideation', label: 'Ideas' },
  { path: 'creative-brief', label: 'Creative Brief' },
  { path: 'content-tasks', label: 'Content Task Outline' },
  { path: 'summary', label: 'Summary' },
] as const;

export function CampaignNav({account, campaignSlug}: {account: any, campaignSlug: string}) {
  const pathname = usePathname();
  // const router = useRouter();
  const { contentTasks, openIdeaSelectionDialog } = useCampaignContext();
  
  // Find the current step based on the URL
  const currentStep = CAMPAIGN_STEPS.find(
    (step) => pathname.endsWith(step.path)
  ) || CAMPAIGN_STEPS[0];

  // Get the index of the current step
  const currentStepIndex = CAMPAIGN_STEPS.indexOf(currentStep);

  // Specific handler for summary click
  const handleSummaryClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    // Check if currently on 'Content Task Outline' step and tasks exist
    if (currentStepIndex === 3 && contentTasks.length > 0) {
      event.preventDefault(); // Prevent default navigation
      openIdeaSelectionDialog(); // Open the dialog via context
    } else {
      // Allow default navigation or explicitly navigate if needed
      // Default href behavior should work if preventDefault is not called
    }
  };

  return (
    <div className="flex flex-col gap-4 p-12">
      <div className="flex items-center justify-between w-full">
        <div className="relative flex items-center w-full">
        
          <StepItem
            href={`/home/<USER>/campaigns/${campaignSlug}/overview`}
            label="Campaign Setup"
            isCompleted={currentStepIndex > 0} // Completed if we've moved past this step
            isCurrent={currentStepIndex === 0} // Current if we're on this step
          />

          {/* Ideation Step */}
          <StepItem
            href={`/home/<USER>/campaigns/${campaignSlug}/ideation`}
            label="Ideas"
            isCompleted={currentStepIndex > 1} // Completed if we've moved past this step
            isCurrent={currentStepIndex === 1} // Current if we're on this step
          />

          {/* Creative Brief Step */}
          <StepItem
            href={`/home/<USER>/campaigns/${campaignSlug}/creative-brief`}
            label="Creative Brief"
            isCompleted={currentStepIndex > 2} // Completed if we've moved past this step
            isCurrent={currentStepIndex === 2} // Current if we're on this step
          />

          {/* Content Task Outline Step */}
          <StepItem
            href={`/home/<USER>/campaigns/${campaignSlug}/content-tasks`}
            label="Content Task Outline"
            isCompleted={currentStepIndex > 3} // Completed if we've moved past this step
            isCurrent={currentStepIndex === 3} // Current if we're on this step
          />
          
          {/* Summary Step - Add onClick handler */}
          <StepItem
            href={`/home/<USER>/campaigns/${campaignSlug}/summary`}
            label="Summary"
            isCompleted={currentStepIndex > 4} // Completed if we've moved past this step
            isCurrent={currentStepIndex === 4} // Current if we're on this step
            onClick={handleSummaryClick} // Add the onClick handler
          />

          {/* Progress Bar */}
          <div className="absolute top-[15px] left-0 w-full h-[2px] bg-muted">
            <div 
              className="h-full bg-primary transition-all duration-300" 
              style={{ 
                width: `${((currentStepIndex + 1) / CAMPAIGN_STEPS.length) * 100}%` 
              }} 
            />
          </div>
        </div>
      </div>
    </div>
  )
}

interface StepItemProps {
  href: string
  label: string
  isCompleted: boolean
  isCurrent: boolean
  isFirst?: boolean
  isLast?: boolean
  onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void;
}

function StepItem({ href, label, isCompleted, isCurrent, isFirst, isLast, onClick }: StepItemProps) {
  return (
    <div className={cn(
      "relative z-10 flex flex-col items-center gap-2",
      isFirst ? "flex-1" : isLast ? "flex-1" : "flex-1"
    )}>
      <a
        href={href}
        onClick={onClick}
        className={cn(
          "flex items-center justify-center w-8 h-8 rounded-full border-2",
          isCompleted
            ? "bg-primary border-primary text-primary-foreground" // Completed steps
            : isCurrent
              ? "bg-foreground border-foreground text-background" // Current step
              : "bg-background border-muted text-muted-foreground" // Future steps
        )}
      >
        {isCompleted ? (
          <Check className="w-5 h-5" />
        ) : isCurrent ? (
          <Circle className="w-5 h-5 fill-background stroke-background" />
        ) : null}
      </a>
      <span className={cn(
        "text-sm font-medium",
        isCompleted || isCurrent ? "text-foreground" : "text-muted-foreground"
      )}>
        {label}
      </span>
    </div>
  )
}
  