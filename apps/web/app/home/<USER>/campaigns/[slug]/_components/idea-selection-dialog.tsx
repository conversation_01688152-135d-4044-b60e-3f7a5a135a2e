'use client';

import { Checkbox } from '@kit/ui/checkbox';
import { Label } from '@kit/ui/label';
import { CampaignIdea } from '~/types/campaign-idea';
import { useState, useEffect } from 'react';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Check } from 'lucide-react';

interface IdeaSelectionDialogProps {
  ideas: CampaignIdea[];
  selectedIdeaIds: string[];
  onChange: (selected: string[]) => void;
}

export function IdeaSelectionDialog({ 
  ideas, 
  selectedIdeaIds, 
  onChange 
}: IdeaSelectionDialogProps) {
  const [selected, setSelected] = useState<string[]>(selectedIdeaIds);

  // Update local state when prop changes
  useEffect(() => {
    setSelected(selectedIdeaIds);
  }, [selectedIdeaIds]);

  const handleToggle = (ideaId: string) => {
    const updatedSelection = selected.includes(ideaId)
      ? selected.filter(id => id !== ideaId)
      : [...selected, ideaId];
    
    setSelected(updatedSelection);
    onChange(updatedSelection);
  };

  if (!ideas || ideas.length === 0) {
    return <p className="text-muted-foreground">No ideas available to select.</p>;
  }

  return (
    <div className="space-y-4">
      <p className="text-muted-foreground mb-4">
        Select the ideas you want to move to the content studio:
      </p>

      <ScrollArea className="h-[300px] pr-4">
        <div className="space-y-4">
          {ideas.map((idea) => {
            const isSelected = selected.includes(idea.id || '');
            if(!idea.brief_blocks) return null;
            return (
              <div 
                key={idea.id} 
                className={`p-4 border rounded-md relative ${isSelected ? 'border-primary' : 'border-border'}`}
              >
                {isSelected && (
                  <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1">
                    <Check className="h-4 w-4" />
                  </div>
                )}
                <div className="flex items-start gap-3">
                  <Checkbox 
                    id={`idea-${idea.id}`} 
                    checked={isSelected}
                    onCheckedChange={() => handleToggle(idea.id || '')}
                  />
                  <div className="space-y-1">
                    <Label 
                      htmlFor={`idea-${idea.id}`}
                      className="text-sm font-medium leading-none cursor-pointer"
                    >
                      Idea {ideas.indexOf(idea) + 1}
                    </Label>
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {idea.content || 'No content'}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
}