'use client';
import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@kit/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';
import { updateCompanyContent } from '~/services/company-content';
import { useCampaignContext } from './campaign-context';
import { IdeaSelectionDialog } from './idea-selection-dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';

const CAMPAIGN_STEPS = [
  { path: 'overview', label: 'Strategy' },
  { path: 'ideation', label: 'Ideas' },
  { path: 'creative-brief', label: 'Creative Brief' },
  { path: 'content-tasks', label: 'Content Task Outline' },
  { path: 'summary', label: 'Summary' },
] as const;

type CampaignStep = typeof CAMPAIGN_STEPS[number];

interface BottomNavProps {
  account: string;
  campaignSlug: string;
}

export function BottomNav({ account, campaignSlug }: BottomNavProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isUpdating, setIsUpdating] = useState(false);
  
  const currentStep = CAMPAIGN_STEPS.find(
    (step) => pathname.endsWith(step.path)
  ) || CAMPAIGN_STEPS[0];

  const currentStepIndex = CAMPAIGN_STEPS.indexOf(currentStep);
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === CAMPAIGN_STEPS.length - 1;
  
  const isContentTaskOutlinePage = currentStep.path === 'content-tasks';
  // Get campaign data from context
  const { 
    contentTasks, 
    campaignIdeas, 
    selectedIdeaIds, 
    setSelectedIdeaIds, 
    isIdeaSelectionDialogOpen, 
    setIsIdeaSelectionDialogOpen,
    openIdeaSelectionDialog 
  } = useCampaignContext();

  const handleMoveToStudio = async () => {
    try {
      setIsUpdating(true);
      
      if (contentTasks.length === 0 || selectedIdeaIds.length === 0) {
        toast.error('No content tasks or ideas selected');
        setIsUpdating(false);
        setIsIdeaSelectionDialogOpen(false);
        return;
      }
      
      // Filter tasks for only the selected ideas
      const selectedTasks = contentTasks.filter(task => 
        selectedIdeaIds.includes(task.idea_id)
      );
      
      if (selectedTasks.length === 0) {
        toast.error('No tasks found for the selected ideas');
        setIsUpdating(false);
        setIsIdeaSelectionDialogOpen(false);
        return;
      }
      
      // Update only the tasks for selected ideas
      await Promise.all(
        selectedTasks
          .filter(task => task && task.id) // Filter out any tasks without an id
          .map(task => 
            updateCompanyContent(
              task.id,
              { 
                is_draft: false,
                status: 'In Progress' 
              }
            )
          )
      );
      
      const tasksCount = selectedTasks.length;
      const ideasCount = selectedIdeaIds.length;
      toast.success(`${tasksCount} content tasks from ${ideasCount} idea${ideasCount > 1 ? 's' : ''} moved to studio successfully`);
      
      // Navigate to the summary page
      router.push(`/home/<USER>/campaigns/${campaignSlug}/summary`);
    } catch (error) {
      console.error('Error moving content to studio:', error);
      toast.error('Failed to move content to studio');
    } finally {
      setIsUpdating(false);
      setIsIdeaSelectionDialogOpen(false);
    }
  };

  const handleNavigation = (direction: 'back' | 'next') => {
    // If we're on the content task outline page and clicking next, open the dialog via context if there are content tasks
    if (isContentTaskOutlinePage && direction === 'next' && contentTasks.length > 0) {
      openIdeaSelectionDialog();
      return;
    }

    const targetIndex = direction === 'next' 
      ? currentStepIndex + 1 
      : currentStepIndex - 1;

    if (targetIndex >= 0 && targetIndex < CAMPAIGN_STEPS.length) {
      const targetStep = CAMPAIGN_STEPS[targetIndex] as CampaignStep;
      router.push(`/home/<USER>/campaigns/${campaignSlug}/${targetStep.path}`);
    }
  };

  
  return (
    <>
      <div className="absolute bottom-0 left-0 right-0 border-t bg-background/80 backdrop-blur-sm p-4 flex justify-between items-center shadow-lg">
        <Button
          variant="outline"
          onClick={() => handleNavigation('back')}
          disabled={isFirstStep}
          className="flex items-center gap-2"
        >
          <ChevronLeft className="h-4 w-4" />
          Back
        </Button>

        <div className="flex items-center gap-2">
          {CAMPAIGN_STEPS.map((step, index) => (
            <div
              key={step.path}
              className={`h-2 w-2 rounded-full ${
                index === currentStepIndex
                  ? 'bg-primary'
                  : index < currentStepIndex
                  ? 'bg-primary/50'
                  : 'bg-muted'
              }`}
            />
          ))}
        </div>

        <Button
          variant="default"
          onClick={() => handleNavigation('next')}
          disabled={isLastStep}
          className="flex items-center gap-2"
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Move to Studio Dialog - Use context state and setter */}
      <AlertDialog open={isIdeaSelectionDialogOpen} onOpenChange={setIsIdeaSelectionDialogOpen}>
        <AlertDialogContent className="max-w-xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Select ideas to move to the studio</AlertDialogTitle>
            <AlertDialogDescription>
              Choose which ideas&apos; content tasks you want to move to the content studio where the design begins.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="py-4">
            <IdeaSelectionDialog 
              ideas={campaignIdeas}
              selectedIdeaIds={selectedIdeaIds}
              onChange={setSelectedIdeaIds}
            />
          </div>
          
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleMoveToStudio} 
              disabled={isUpdating || selectedIdeaIds.length === 0}
            >
              {isUpdating ? 'Moving...' : 'Move to Studio'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 