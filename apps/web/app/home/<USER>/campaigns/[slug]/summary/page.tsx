import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { Skeleton } from '@kit/ui/skeleton';
import { getCampaignBySlug, markCampaignReachedSummary } from '~/services/campaign';
import { getCampaignIdeas } from '~/services/campaign-idea';
import { getCompanyContent } from '~/services/company-content';
import { CampaignSummary } from './_components/campaign-summary';

interface SummaryPageProps {
  params: {
    account: string;
    slug: string;
  };
}

export default async function SummaryPage({ params }: SummaryPageProps) {
  const { slug } = await params;
  
  // Fetch campaign data
  const campaign = await getCampaignBySlug(slug);
  
  if (!campaign) {
    return notFound();
  }
  
  // Mark the campaign as having reached the summary
  if (!campaign.has_reached_summary) {
    await markCampaignReachedSummary(campaign.id);
  }
  
  // Fetch campaign ideas
  const campaignIdeas = await getCampaignIdeas(campaign.id);
  
  // Fetch company content (all content tasks for this campaign)
  const campaignTasks = await getCompanyContent(campaign.id);
  
  // Include all tasks, both draft and non-draft
  // Most tasks may be in draft state by default

  return (
    <div className="container py-8 pb-24">
      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <CampaignSummary 
          campaign={campaign}
          campaignIdeas={campaignIdeas}
          campaignTasks={campaignTasks}
        />
      </Suspense>
    </div>
  );
}