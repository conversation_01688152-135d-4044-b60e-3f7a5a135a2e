import { toast } from 'sonner';
import { upsertCampaignIdea } from '~/services/campaign';
import { CampaignIdea } from '~/types/campaign-idea';

type SelectionHandler = (code: string) => Promise<void>;

export function createLanguageHandler(
  selectedLanguages: string[],
  setSelectedLanguages: (languages: string[]) => void,
  selectedIdea?: CampaignIdea
): SelectionHandler {
  return async (languageCode: string) => {
    try {
      // Toggle language selection
      const updatedLanguages = selectedLanguages.includes(languageCode)
        ? selectedLanguages.filter(code => code !== languageCode)
        : [...selectedLanguages, languageCode];
      
      setSelectedLanguages(updatedLanguages);
      
      // Update in database if we have a selected idea
      if (selectedIdea?.id) {
        await upsertCampaignIdea({
          id: selectedIdea.id,
          languages: updatedLanguages,
        });
      }
    } catch (error) {
      console.error('Error updating languages:', error);
      toast.error('Failed to update languages');
    }
  };
}

export function createChannelHandler(
  selectedChannels: string[],
  setSelectedChannels: (channels: string[]) => void,
  selectedIdea?: CampaignIdea
): SelectionHandler {
  return async (channelCode: string) => {
    try {
      // Toggle channel selection
      const updatedChannels = selectedChannels.includes(channelCode)
        ? selectedChannels.filter(code => code !== channelCode)
        : [...selectedChannels, channelCode];
      
      setSelectedChannels(updatedChannels);
      
      // Update in database if we have a selected idea
      if (selectedIdea?.id) {
        await upsertCampaignIdea({
          id: selectedIdea.id,
          channels: updatedChannels,
        });
      }
    } catch (error) {
      console.error('Error updating channels:', error);
      toast.error('Failed to update channels');
    }
  };
}

export function createContentTypeHandler(
  selectedContentTypes: string[],
  setSelectedContentTypes: (contentTypes: string[]) => void,
  selectedIdea?: CampaignIdea
): SelectionHandler {
  return async (contentTypeCode: string) => {
    try {
      // Toggle content type selection
      const updatedContentTypes = selectedContentTypes.includes(contentTypeCode)
        ? selectedContentTypes.filter(code => code !== contentTypeCode)
        : [...selectedContentTypes, contentTypeCode];
      
      setSelectedContentTypes(updatedContentTypes);
      
      // Update in database if we have a selected idea
      if (selectedIdea?.id) {
        await upsertCampaignIdea({
          id: selectedIdea.id,
          content_types: updatedContentTypes,
        });
      }
    } catch (error) {
      console.error('Error updating content types:', error);
      toast.error('Failed to update content types');
    }
  };
} 