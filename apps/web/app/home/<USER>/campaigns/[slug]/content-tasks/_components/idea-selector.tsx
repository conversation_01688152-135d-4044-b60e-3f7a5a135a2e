'use client';

import { Card, CardContent } from '@kit/ui/card';
import { IdeaSelectorProps } from '../_types/component-types';
import { Check } from 'lucide-react';
import { cn } from '@kit/ui/utils';

export default function IdeaSelector({
  campaignIdeas,
  selectedIdea,
  onIdeaSelect,
}: IdeaSelectorProps) {
  if (!campaignIdeas || campaignIdeas.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No ideas found for this campaign.
      </div>
    );
  }

  return (
    <section className="space-y-4">
      <h2 className="text-2xl font-semibold tracking-tight">Selected Idea</h2>
      <p className="text-muted-foreground">
        Choose the idea you want to use for your creative brief.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {campaignIdeas.map((idea) => {
          console.log("idea", typeof idea.content, idea.content);
        return (
          <Card 
            key={idea.id}
            className={cn(
              "cursor-pointer hover:shadow-md transition-all border-2",
              selectedIdea?.id === idea.id 
                ? "border-primary" 
                : "border-transparent"
            )}
            onClick={() => onIdeaSelect(idea)}
          >
            <CardContent className="p-4 relative">
              {selectedIdea?.id === idea.id && (
                <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1">
                  <Check className="h-4 w-4" />
                </div>
              )}
              <p className="line-clamp-4 text-sm">
                {idea.title ? idea.title : idea.content || 'No content'}
              </p>
            </CardContent>
          </Card>
        )}
      )}
      </div>
    </section>
  );
} 