import { MarketingContentProps } from '../_types/brief-types';

/**
 * Generates markdown from structured marketing content
 * @param data Marketing content data
 * @returns Formatted markdown string
 */
const generateMarkdown = (data: MarketingContentProps): string => {
  if (!data) {
    return '## No content available';
  }

  const { 
    key_messaging, 
    target_audience, 
    brand_guidelines, 
    competitive_context, 
    content_specifications, 
    content_outline
  } = data;

  return `
## Key Messaging

### Headline
**${key_messaging?.headline ?? 'No headline provided'}**

### Key Messages
- ${key_messaging?.key_messages?.join('\n- ') ?? 'No key messages provided'}

### Call to Actions
- **Primary CTA:** [${key_messaging?.call_to_actions?.primary_cta ?? 'N/A'}](#link-to-registration-page)
- **Secondary CTA:** [${key_messaging?.call_to_actions?.secondary_cta ?? 'N/A'}](#link-to-website)

### Primary Message
${key_messaging?.primary_message ?? 'No primary message provided'}

### Supporting Points
- ${key_messaging?.supporting_points?.join('\n- ') ?? 'No supporting points provided'}

### Value Proposition
${key_messaging?.value_proposition ?? 'No value proposition provided'}

---

## Target Audience

### Primary Audience

#### Demographics
- **Gender:** ${target_audience?.primary_audience?.demographics?.gender ?? 'N/A'}
- **Location:** ${target_audience?.primary_audience?.demographics?.location ?? 'N/A'}
- **Age Range:** ${target_audience?.primary_audience?.demographics?.age_range ?? 'N/A'}
- **Occupation:** ${target_audience?.primary_audience?.demographics?.occupation ?? 'N/A'}

#### Psychographics
- **Goals:**
  - ${target_audience?.primary_audience?.psychographics?.goals?.join('\n  - ') ?? 'No goals specified'}
- **Interests:**
  - ${target_audience?.primary_audience?.psychographics?.interests?.join('\n  - ') ?? 'No interests specified'}
- **Pain Points:**
  - ${target_audience?.primary_audience?.psychographics?.pain_points?.join('\n  - ') ?? 'No pain points specified'}

### Content Preferences
- **Consumption Habits:** ${target_audience?.content_preferences?.consumption_habits ?? 'N/A'}
- **Preferred Platforms:** ${target_audience?.content_preferences?.preferred_platforms?.join(', ') ?? 'None specified'}

---

## Brand Guidelines

### Visual Identity
- **Typography:** ${brand_guidelines?.visual_identity?.typography ?? 'N/A'}
- **Color Palette:** ${brand_guidelines?.visual_identity?.color_palette ?? 'N/A'}
- **Imagery Style:** ${brand_guidelines?.visual_identity?.imagery_style ?? 'N/A'}

### Language Guidelines
- **Terminology:**
  - ${brand_guidelines?.language_guidelines?.terminology?.join('\n  - ') ?? 'No terminology specified'}
- **Phrases to Avoid:**
  - ${brand_guidelines?.language_guidelines?.phrases_to_avoid?.join('\n  - ') ?? 'No phrases to avoid specified'}

---

## Competitive Context

### Key Competitors
- ${competitive_context?.key_competitors?.join('\n- ') ?? 'No competitors specified'}

### Differentiation Points
- ${competitive_context?.differentiation_points?.join('\n- ') ?? 'No differentiation points specified'}

---

## Content Specifications

### Visuals
- **Graphics:**
  - ${content_specifications?.visuals?.graphics?.join('\n  - ') ?? 'No graphics specified'}
- **Brand Assets:**
  - ${content_specifications?.visuals?.brand_assets?.join('\n  - ') ?? 'No brand assets specified'}
- **Required Images:**
  - ${content_specifications?.visuals?.required_images?.join('\n  - ') ?? 'No required images specified'}

### Channels
- ${content_specifications?.channels?.join(', ') ?? 'No channels specified'}

### Key Themes
- ${content_specifications?.key_themes?.join(', ') ?? 'No themes specified'}

### SEO Keywords
- ${content_specifications?.SEO_keywords?.join(', ') ?? 'No keywords specified'}

### Content Types
- ${content_specifications?.content_types?.join(', ') ?? 'No content types specified'}

### Format Details
- **Length:**
  - ${content_specifications?.format_details?.length ?? 'N/A'}
- **Structure:**
  - ${content_specifications?.format_details?.structure ?? 'N/A'}

### Tone and Style
- **Voice:** ${content_specifications?.tone_and_style?.voice ?? 'N/A'}
- **Style Guidelines:**
  - ${content_specifications?.tone_and_style?.style_guidelines?.join('\n  - ') ?? 'No style guidelines specified'}

---

## Content Outline

### Sections

${content_outline?.sections?.map(section => `
1. **${section.title ?? 'Untitled Section'}**
   - **Key Points:**
     - ${section.key_points?.join('\n     - ') ?? 'No key points specified'}
   - **Examples:**
     - ${section.examples?.join('\n     - ') ?? 'No examples specified'}
`).join('') ?? 'No sections specified'}

### Conclusion
${content_outline?.conclusion ?? 'No conclusion provided'}

### Introduction
${content_outline?.introduction ?? 'No introduction provided'}

  `;
};

/**
 * Converts JSON marketing content to markdown format
 * @param params Object containing the marketing content data
 * @returns Formatted markdown string
 */
export const jsonToMarkdown = ({ data }: { data: MarketingContentProps }): string => {
  if (!data) {
    return '## No content available';
  }
  
  try {
    return generateMarkdown(data);
  } catch (error) {
    // Log with specific error information
    console.error('Error generating markdown:', error instanceof Error ? error.message : 'Unknown error');
    return '## Error generating content\nThere was an error processing the content. Please try again.';
    
  }
}; 