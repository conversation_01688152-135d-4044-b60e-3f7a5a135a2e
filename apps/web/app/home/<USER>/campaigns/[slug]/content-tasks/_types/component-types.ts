import { CampaignIdea } from "~/types/campaign-idea";
import { CompanyContent } from "~/types/company-content";
import { MarketingContentProps } from "./brief-types";
import { Block } from "@blocknote/core";

export interface IdeaSelectorProps {
  campaignIdeas: CampaignIdea[];
  selectedIdea?: CampaignIdea;
  onIdeaSelect: (idea: CampaignIdea) => void;
}

export interface BriefDisplayProps {
  brief: MarketingContentProps;
  isLoading: boolean;
  campaignIdeaId?: string;
  briefBlocks?: Block[];
}

export interface ContentTasksDisplayProps {
  contentTasks: CompanyContent[];
  contentTypes: string[];
  activeContentType: string;
  generateContentTasks: () => Promise<void>;
  onContentTypeChange: (type: string) => Promise<void>;
  isLoading: boolean;
  onDelete?: (id: string) => void;
} 