'use client';

import { useState, useEffect } from 'react';
import { Separator } from '@kit/ui/separator';
import { CampaignIdea } from '~/types/campaign-idea';
import { CompanyContent } from '~/types/company-content';
import { Campaign } from '~/types/Campaign';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';
import { upsertCampaignIdea as _upsertCampaignIdea, upsertCampaignIdea } from '~/services/campaign';
import { saveContentTasks as _saveContentTasks, saveContentTasks, deleteCompanyContent } from '~/services/company-content';
import { MarketingContentProps } from '../_types/brief-types';
import IdeaSelector from './idea-selector';
import ContentTasksDisplay from './content-tasks-display';
import { Button } from '@kit/ui/button';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief, extractCampaignBrief } from '~/utils/brief.util';
import { useCampaignContext } from '../../_components/campaign-context';

interface CreativeBriefSectionProps {
  campaign: Campaign;
  campaignIdeas: CampaignIdea[];
  companyContent: CompanyContent[];
}

export default function ContentTaskOutlineSection({
  campaign,
  campaignIdeas,
  companyContent,
}: CreativeBriefSectionProps) {
  const workspace = useTeamAccountWorkspace();
  const brand = useBrandData(workspace.account.id);
  
  // Safely get the first idea if available
  const firstIdea = campaignIdeas && campaignIdeas.length > 0 ? campaignIdeas[0] : undefined;
  
  // State management
  const [selectedIdea, setSelectedIdea] = useState<CampaignIdea | undefined>(firstIdea);
  const [brief, setBrief] = useState<MarketingContentProps | Record<string, never>>(
    (firstIdea?.brief as MarketingContentProps) || {}
  );

  const [briefLoading, setBriefLoading] = useState(false);
  console.log({briefLoading});
  //filter company content by selectd idea id as the initial content tasks state
  const [contentTasks, _setContentTasks] = useState<CompanyContent[]>(companyContent.filter((content) => content.idea_id === selectedIdea?.id));
  const [tasksLoading, setTasksLoading] = useState(false);
  const [activeContentType, setActiveContentType] = useState<string>('all');
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(
    Array.isArray(firstIdea?.content_types) ? firstIdea.content_types as string[] : []
  );
  
  useEffect(() => {
    if(contentTasks.length > 0) {
        //set the initial content types
        //ensure we only get the unique content types
        setSelectedContentTypes(Array.from(new Set(contentTasks.map((content) => content.content_type))));
    }
  }, [selectedIdea, contentTasks]);

  // Generate brief on page load if no brief exists
  useEffect(() => {
    if (selectedIdea && (!selectedIdea.brief || !brief || Object.keys(selectedIdea.brief || {}).length === 0)) {
      generateBrief();
    } else if (selectedIdea?.brief) {
      setBrief(selectedIdea.brief as MarketingContentProps);
      _setContentTasks(companyContent.filter((content) => content.idea_id === selectedIdea?.id));
    }
  }, [selectedIdea]);

  const generateBrief = async () => {
    if (!selectedIdea) {
      toast.error('No idea selected');
      return;
    }
    
    try {
      setBriefLoading(true);
      
      // Make sure we have at least the idea content available
      if (!selectedIdea.content || selectedIdea.content.trim() === '') {
        toast.error('The selected idea has no content');
        setBriefLoading(false);
        return;
      }
      
      // Explicitly set 'en' as default language if array is empty
      const languages = (Array.isArray(selectedIdea.languages) && selectedIdea.languages.length > 0)
        ? selectedIdea.languages
        : ['en'];
      
      // Set defaults for API fields to prevent validation errors
      const brandName = workspace.account.name || 'Company Name';
      console.log({brand});
      const brandBrief = brand.data ? extractBrandBrief(brand.data) : 'Brief Not Provided';
      const campaignBrief = extractCampaignBrief(campaign) || 'No objective provided';
      
      const response = await fetch('/api/ai/brief', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_name: brandName,
          brand_brief: brandBrief,
          campaign_brief: campaignBrief,
          //@ts-expect-error maps should work TODO: fix this
          product_info: campaign.documents ? campaign.documents?.map((doc: any) => `${doc.title}: ${doc.content}`).join('\n\n') : "No Product Info Provided",
          idea: selectedIdea.content,
          languages, // Always set, defaults to 'en'
          supported_content_types: selectedIdea.content_types,
          supported_channels: selectedIdea.channels,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate brief');
      }
 
      const data = await response.json();
      // console.log("GENERATED BRIEF", {data});
      setBrief(data.creative_brief);
      
      if (selectedIdea?.id) {
        await upsertCampaignIdea({
          id: selectedIdea.id,
          brief: data.creative_brief,
        });
      }
    
    } catch (error) {
      console.error('Error generating brief:', error);
      toast.error('Failed to generate brief');
    } finally {
      setBriefLoading(false);
    }
  };

  const generateContentTasks = async () => {
    if (!selectedIdea?.id) {
      toast.error('No idea selected');
      return;
    }
    
    try {
      setTasksLoading(true);
      
      // Always use a valid language
      const language = (Array.isArray(selectedIdea.languages) && selectedIdea.languages.length > 0)
        ? selectedIdea.languages[0] 
        : 'en';
      
      const response = await fetch('/api/ai/generate-tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          creativeBrief: brief,
          language,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate tasks');
      }
      
      const data = await response.json();
      const tasks = await saveContentTasks(
        data, 
        campaign.id, 
        selectedIdea.id, 
        campaign.company_id
      );
      _setContentTasks(tasks);
    } catch (error) {
      console.error('Error generating tasks:', error);
      toast.error('Failed to generate tasks');
    } finally {
      setTasksLoading(false);
    }
  };
  
  const handleContentTypeChange = async (contentTypeCode: string) => {
    setActiveContentType(contentTypeCode);
  };
  
  const handleIdeaSelect = (idea: CampaignIdea) => {
    setSelectedIdea(idea);
    
    // Reset content types based on selected idea
    setSelectedContentTypes(
      Array.isArray(idea?.content_types) ? idea.content_types as string[] : []
    );
  };

  // Make content tasks and campaign ideas available via context for the bottom nav
  const { 
    setContentTasks: setContextContentTasks,
    setCampaignIdeas: setContextCampaignIdeas,
    setSelectedIdeaIds
  } = useCampaignContext();
  
  useEffect(() => {
    setContextContentTasks(contentTasks);
  }, [contentTasks, setContextContentTasks]);
  
  // Store campaign ideas in context
  useEffect(() => {
    setContextCampaignIdeas(campaignIdeas);
    // Initialize selected ideas with the current selected idea
    if (selectedIdea?.id) {
      setSelectedIdeaIds([selectedIdea.id]);
    }
  }, [campaignIdeas, selectedIdea, setContextCampaignIdeas, setSelectedIdeaIds]);

  const handleDeleteTask = async (id: string) => {
    try {
      await deleteCompanyContent(id);
      _setContentTasks(prevTasks => prevTasks.filter(task => task.id !== id));
      toast.success('Task deleted successfully');
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error('Failed to delete task');
    }
  };
  
  return (
    <div className="space-y-8">
      {/* Idea Selection Section */}
      <IdeaSelector
        campaignIdeas={campaignIdeas}
        selectedIdea={selectedIdea}
        onIdeaSelect={handleIdeaSelect}
      />
     {(campaignIdeas && campaignIdeas.length > 0) && 
     <>
      <div className="flex">
        {(brief && contentTasks.length === 0) && <div className='items-center justify-center flex w-full'><Button size='lg' className="w-full mt-15 my-20" onClick={() => generateContentTasks()}>Generate Content Tasks</Button></div>}
        {!brief && <p className="">Generate Creative Brief in the previous step to generate content tasks.</p>}
      </div>

      {(contentTasks.length > 0 || tasksLoading) && (
        <>
          <Separator className="my-8" />
          {/* Content Tasks Display Section */}
          <ContentTasksDisplay 
            generateContentTasks={generateContentTasks}
            contentTasks={contentTasks}
            contentTypes={selectedContentTypes}
            activeContentType={activeContentType}
            onContentTypeChange={handleContentTypeChange}
            isLoading={tasksLoading}
            onDelete={handleDeleteTask}
          />
        </>
      )}
      </>
      }
    </div>
  );
} 