'use client';
import { useEffect, useCallback } from "react";
import { jsonToMarkdown } from "../_lib/jsonToMarkdown";
import { BriefDisplayProps } from "../_types/component-types";
import { Skeleton } from "@kit/ui/skeleton";
import { MarketingContentProps } from "../_types/brief-types";
// import { Loader2 } from "lucide-react";
import { Spinner } from "@kit/ui/spinner";
import { Block } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import { useCreateBlockNote } from "@blocknote/react";
import debounce from 'lodash/debounce';
import { upsertCampaignIdea } from "~/services/campaign";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@kit/ui/accordion";
import { useTheme } from "next-themes";

export default function BriefDisplay({
  brief,
  isLoading,
  campaignIdeaId,
}: BriefDisplayProps) {
  // const hasBrief = Object.keys(brief).length > 0;
  const editor = useCreateBlockNote({});
  const { theme } = useTheme();
  // Debounced function to save blocks
  const debouncedSaveBlocks = useCallback(
    debounce(async (blocks: Block[]) => {
      try {
        if (!campaignIdeaId) return;
        
        await upsertCampaignIdea({
          id: campaignIdeaId,
          brief_blocks: blocks,
        });
      } catch (error) {
        console.error('Error saving blocks:', error);
      }
    }, 1000),
    [campaignIdeaId]
  );

  useEffect(() => {
    async function updateContent() {
      const blocks = await editor?.tryParseMarkdownToBlocks(jsonToMarkdown({ data: brief as MarketingContentProps }));
      if (!editor) return;
      editor.replaceBlocks(editor.document, blocks);
    }
    updateContent();
  }, [editor, brief]);

  return (
    <section className="space-y-6">
      <Accordion type="single" defaultValue="brief" collapsible>
        <AccordionItem value="brief">
          <div className="flex items-center justify-between">
            <AccordionTrigger className="hover:no-underline">
              <h2 className="text-2xl font-semibold tracking-tight">Campaign Brief</h2>
            </AccordionTrigger>
          </div>
          
          <AccordionContent>
            {isLoading ? (
              <div className="relative">
                <Skeleton className="h-[400px] w-full" />
                <div className="absolute inset-0 flex flex-col items-center justify-center gap-4">
                  <Spinner className="h-8 w-8 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Brief is being generated...</p>
                </div>
              </div>
            ) : (
              <BlockNoteView
                theme={theme as "light" | "dark"}
                editor={editor}
                onChange={() => {
                  if (!editor) return;
                  const blocks = editor.document;
                  debouncedSaveBlocks(blocks);
                }}
              />
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </section>
  );
} 