import { Suspense } from 'react';
import { getCampaignBySlug } from '~/services/campaign';
import { getCampaignIdeas } from '~/services/campaign-idea';
import { getCompanyContent } from '~/services/company-content';
import { notFound } from 'next/navigation';

import { Skeleton } from '@kit/ui/skeleton';
import ContentTaskOutlineSection from './_components/content-task-outline-section';


interface ContentTaskOutlinePageProps {
  params: {
    account: string;
    slug: string;
  };
}

export default async function ContentTaskOutlinePage({ params }: ContentTaskOutlinePageProps) {
  const { slug } = await params;
  
  // Fetch campaign data
  const campaign = await getCampaignBySlug(slug);
  
  if (!campaign) {
    return notFound();
  }
  
  // Fetch campaign ideas - we'll need to check the correct function for this
  const campaignIdeas = await getCampaignIdeas(campaign.id);
  
  // Fetch company content
  const companyContent = await getCompanyContent(campaign.id);

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-8">Content Task Outline</h1>
      
      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <ContentTaskOutlineSection
          campaign={campaign}
          campaignIdeas={campaignIdeas}
          companyContent={companyContent}
        />
      </Suspense>
    </div>
  );
}