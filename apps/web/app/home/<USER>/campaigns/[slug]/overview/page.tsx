import { getCampaignBySlug } from '~/services/campaign';
import { CampaignObjectivesForm } from './_components/campaign-objectives-form';

interface CampaignOverviewPageProps {
  params: {
    slug: string;
    account: string;
  };
}

export default async function CampaignOverviewPage({ params }: CampaignOverviewPageProps) {
  const campaign = await getCampaignBySlug((await params).slug);
  
  if (!campaign) {
    return <div>Campaign not found</div>;
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">{campaign.name}</h1>
        <p className="text-muted-foreground mt-2">
          Edit your campaign details below
        </p>
      </div>
      <CampaignObjectivesForm campaign={campaign} />
    </div>
  );
}