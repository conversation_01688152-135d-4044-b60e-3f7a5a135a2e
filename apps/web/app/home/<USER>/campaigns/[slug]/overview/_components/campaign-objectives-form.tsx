'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { Campaign, CampaignResponse } from '~/types/Campaign';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Textarea } from '@kit/ui/textarea';
import { Card } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { updateCampaign } from '~/services/campaign';
import { toast } from 'sonner';
import { useEffect, useCallback, useState } from 'react';
import debounce from 'lodash/debounce';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief } from '~/utils/brief.util';
import { DocumentSelector, SelectedDocument } from '~/components/document-selector';
import { Calendar } from '@kit/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@kit/ui/utils';
import { useRouter } from 'next/navigation';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@kit/ui/alert-dialog';
import pathsConfig from '~/config/paths.config';

const CampaignFormSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  objective: z.string().min(1, 'Objective is required'),
  messaging: z.string().optional(),
  value_prop: z.string().optional(),
  guidelines: z.string().optional(),
  identity: z.string().optional(),
  kpis: z.string().optional(),
  objectives: z.string().optional(),
  personas: z.string().optional(),
  personality: z.string().optional(),
  targetAudience: z.string().optional(),
  tone: z.string().optional(),
  voice: z.string().optional(),
  visualStyle: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  documents: z.array(
    z.object({
      id: z.string(),
      documentTitle: z.string(),
      content: z.string()
    })
  ).optional(),
});

type CampaignFormValues = z.infer<typeof CampaignFormSchema>;

interface CampaignObjectivesFormProps {
  campaign: Campaign;
}

export function CampaignObjectivesForm({ campaign }: CampaignObjectivesFormProps) {
  const workspace = useTeamAccountWorkspace();
  const { data: brandData } = useBrandData(workspace.account.id);
  const router = useRouter();
  const [isNoBrandDialogOpen, setIsNoBrandDialogOpen] = useState(false);
  
  // Initialize form with existing document selections if any
  let initialDocuments: SelectedDocument[] = [];
  
  if (campaign.documents) {
    try {
      // Handle both string JSON and already parsed object
      initialDocuments = typeof campaign.documents === 'string' 
        ? JSON.parse(campaign.documents) 
        : campaign.documents;
    } catch (error) {
      console.error('Error parsing documents:', error);
      // If parsing fails, default to empty array
      initialDocuments = [];
    }
  }

  const form = useForm<CampaignFormValues>({
    resolver: zodResolver(CampaignFormSchema),
    defaultValues: {
      name: campaign.name,
      objective: campaign.objective || '',
      messaging: campaign.messaging || '',
      value_prop: campaign.value_prop || '',
      guidelines: campaign.guidelines || '',
      identity: campaign.identity || '',
      kpis: campaign.kpis || '',
      objectives: campaign.objectives || '',
      personas: campaign.personas || '',
      personality: campaign.personality || '',
      targetAudience: campaign.targetAudience || '',
      tone: campaign.tone || '',
      voice: campaign.voice || '',
      visualStyle: campaign.visualStyle || '',
      start_date: campaign.start_date ? campaign.start_date : undefined,
      end_date: campaign.end_date ? campaign.end_date : undefined,
      documents: initialDocuments,
    },
  });

  const generateWithAIMutation = useMutation({
    mutationFn: async (formData: CampaignFormValues) : Promise<CampaignResponse> => {
      try {
        const brandBrief = brandData ? extractBrandBrief(brandData) : 'Brief Not Provided';
        
        // Prepare document context for AI
        const documentContext = formData.documents && formData.documents.length > 0
          ? formData.documents.map(doc => `${doc.documentTitle}: ${doc.content}`).join('\n\n')
          : '';

        console.log("DOCUMENT CONTEXT", documentContext);
        const response = await fetch('/api/ai/extract_campaign_info_from_text', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            brand_name: workspace.account.name || 'Name Not Provided',
            brand_brief: brandBrief || 'Brief Not Provided',
            campaign_goal: formData.objective || '',
            product_info: documentContext || "No Product Info Provided",
            language: 'English',
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to generate campaign info');
        }
        const data = await response.json();
        console.log('data', data);
        return data;
      } catch (error) {
        console.error('Error generating campaign info:', error);
        toast.error("Failed to generate campaign info");
        throw error;
      }
    },
    onSuccess: (data) => {
      const currentValues = form.getValues();
      
      // Update form fields with the AI-generated content
      Object.keys(data).forEach((key) => {
        if (key in currentValues) {
          form.setValue(key as keyof CampaignFormValues, data[key]);
        }
      });
      
      // Keep the documents as they were and call the mutation to handle saving
      const finalValues = {
        ...form.getValues(),
        documents: currentValues.documents // Preserve original documents
      };

      // Use the mutation to save, ensuring date conversion happens
      updateCampaignMutation.mutate(finalValues);
    },
  });

  const updateCampaignMutation = useMutation({
    mutationFn: async (data: CampaignFormValues) => {
      // Convert dates to strings before calling the service function
      console.log('data', {data})
      return updateCampaign({
        id: campaign.id,
        ...data
      });
    },
    onError: (error) => {
      toast.error("Failed to save changes");
      console.error('Error updating campaign:', error);
    },
  });

  // Create a debounced version of the save function
  const debouncedSave = useCallback(
    debounce((data: CampaignFormValues) => {
      console.log({data});
      // Pass the form data directly, conversion happens in mutationFn
      updateCampaignMutation.mutate(data);
    }, 100),
    [updateCampaignMutation]
  );

  // Handle document selection changes
  const handleDocumentsChange = (documents: SelectedDocument[]) => {
    form.setValue('documents', documents, { shouldDirty: true });
    const formValues = form.getValues();
    debouncedSave(formValues);
  };

  // Watch for form changes and auto-save
  useEffect(() => {
    const subscription = form.watch((data) => {
      console.log("data", data);
      debouncedSave(data as CampaignFormValues);
    });
    return () => {
      subscription.unsubscribe();
      debouncedSave.cancel();
    };
  }, [form, debouncedSave]);

  // Function to handle the Generate with AI button click
  const handleGenerateClick = () => {
    // Check if brand data exists
    if (!brandData) {
      // Open the dialog if no brand data
      setIsNoBrandDialogOpen(true);
    } else {
      // If we have brand data, trigger the mutation directly
      generateWithAIMutation.mutate(form.getValues());
    }
  };

  // Function to handle generate anyway button click
  const handleGenerateAnyway = () => {
    setIsNoBrandDialogOpen(false);
    generateWithAIMutation.mutate(form.getValues());
  };

  // Function to navigate to brand page
  const handleGoToBrand = () => {
    setIsNoBrandDialogOpen(false);
    const brandPath = pathsConfig.app.accountBrand.replace('[account]', workspace.account.slug);
    router.push(brandPath);
  };

  return (
    <>
      <Form {...form}>
        <form className="space-y-6 max-w-4xl mx-auto">
          <Card className="p-6">
            <div className="flex justify-between items-start mb-6">
              <div className="flex-1" />
              <Button
                type="button"
                variant="outline"
                onClick={handleGenerateClick}
                disabled={generateWithAIMutation.isPending}
                className="ml-auto"
              >
                {generateWithAIMutation.isPending ? 'Generating...' : 'Generate With AI'}
              </Button>
            </div>
            <div className="space-y-6">
              <DocumentSelector
                accountId={workspace.account.id}
                initialDocuments={initialDocuments}
                onDocumentsChange={handleDocumentsChange}
                description="Select documents to provide context for your campaign. This information will be used to generate more relevant content."
              />

                  {/* Date Pickers */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="start_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a start date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={(date) => {
                              // field.onChange(date);
                              // the date should be formatted as a date object for supabase but in the form YYYY-MM-DD
                              console.log("dateITEM", date);
                              const year = date?.getFullYear();
                              //@ts-expect-error TODO: fix this
                              const month = String(date?.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
                              const day = String(date?.getDate()).padStart(2, '0');
                              // Combine into YYYY-MM-DD format
                              const formattedDate = `${year}-${month}-${day}`;
                              form.setValue('start_date', formattedDate);
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="end_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick an end date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={(date) => {
                                // field.onChange(date);
                              // the date should be formatted as a date object for supabase but in the form YYYY-MM-DD
                              console.log("dateITEM", date);
                              const year = date?.getFullYear();
                              //@ts-expect-error TODO: fix this
                              const month = String(date?.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
                              const day = String(date?.getDate()).padStart(2, '0');
                              // Combine into YYYY-MM-DD format
                              const formattedDate = `${year}-${month}-${day}`;
                              form.setValue('end_date', formattedDate);
                            }}
                            disabled={(date) =>
                              date < (form.getValues("start_date") || new Date(0)) // Disable dates before start_date or epoch if no start date
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* End Date Pickers */}


              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Campaign Name</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter campaign name"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="objective"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Objective</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter campaign objective"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="messaging"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Messaging</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter campaign messaging"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="value_prop"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Value Proposition</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter value proposition"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="guidelines"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Guidelines</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter campaign guidelines"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="identity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Identity</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter campaign identity"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="kpis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>KPIs</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter KPIs"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="objectives"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Objectives</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter objectives"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="personas"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Personas</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter personas"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="personality"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Personality</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter personality"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="targetAudience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Audience</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter target audience"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tone</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter tone"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="voice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Voice</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter voice"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="visualStyle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Visual Style</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter visual style"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Card>
        </form>
      </Form>
      
      <AlertDialog open={isNoBrandDialogOpen} onOpenChange={setIsNoBrandDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Brand Not Setup</AlertDialogTitle>
            <AlertDialogDescription>
              You don&apos;t have your brand setup. Without setting up your brand, generated materials may not be useful or on-brand.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsNoBrandDialogOpen(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleGoToBrand}>Go To Brand</AlertDialogAction>
            <AlertDialogAction onClick={handleGenerateAnyway}>Generate Anyway</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
