import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

/**
 * Fetches a user's profile image URL using the Twitter/X API v2
 * This is a fallback method if the OAuth flow doesn't return the image
 */
async function fetchProfileImageByUsername(username: string, bearerToken: string): Promise<string | null> {
  try {
    const endpoint = `https://api.x.com/2/users/by/username/${username}?user.fields=profile_image_url`;
    
    const response = await fetch(endpoint, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`
      }
    });
    
    if (!response.ok) {
      console.error('Error fetching profile image:', await response.text());
      return null;
    }
    
    const data = await response.json();
    
    if (data?.data?.profile_image_url) {
      // Remove _normal suffix to get full-size image
      return data.data.profile_image_url.replace('_normal', '');
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching profile image by username:', error);
    return null;
  }
}

/**
 * Handles the Twitter/X OAuth callback
 * Processes the authorization code from Twitter and exchanges it for an access token
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authorization code and state from the URL
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');
    
    // Handle Twitter error responses
    if (error) {
      console.error(`Twitter authorization error: ${error} - ${errorDescription}`);
      return NextResponse.redirect(new URL(`/home?error=${error}`, request.url));
    }
    
    // Validate required parameters
    if (!code || !state) {
      return NextResponse.redirect(new URL('/home?error=invalid_callback', request.url));
    }
    
    // Get Supabase client for database operations
    const supabase = getSupabaseServerClient();
    
    // Get current user ID from Supabase
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError || !userData.user) {
      console.error('Authentication error:', userError);
      return NextResponse.redirect(new URL('/home?error=auth_error', request.url));
    }
    
    const userId = userData.user.id;
    
    // Retrieve the previously stored code verifier and verify state
    
    const { data: oauthData, error: oauthError } = await supabase
      .from('twitterState')
      .select('state, code_verifier')
      .eq('user_id', userId)
      .single();
      
    if (oauthError || !oauthData) {
      console.error('Error retrieving OAuth state:', oauthError);
      return NextResponse.redirect(new URL('/home?error=state_retrieval_error', request.url));
    }
    
    // Verify that the state parameter matches what we stored in the database
    if (oauthData.state !== state) {
      console.error('State mismatch - possible CSRF attack');
      return NextResponse.redirect(new URL('/home?error=state_mismatch', request.url));
    }
    
    // Retrieve the code verifier
    const codeVerifier = oauthData.code_verifier;
    
    if (!codeVerifier) {
      console.error('No code verifier found');
      return NextResponse.redirect(new URL('/home?error=missing_code_verifier', request.url));
    }
    
    // Exchange the authorization code for an access token
    const clientId = process.env.X_API_CLIENT_ID;
    const clientSecret = process.env.X_API_CLIENT_SECRET;
    const redirectUri = `${new URL(request.url).origin}/api/auth/twitter/callback`;
    
    console.log('Token exchange parameters:', {
      code: code.substring(0, 10) + '...',
      redirectUri,
      clientId: clientId?.substring(0, 5) + '...',
      clientSecret: clientSecret ? 'present' : 'missing',
      codeVerifier: codeVerifier.substring(0, 10) + '...',
    });
    
    try {
      // For Twitter/X OAuth 2.0, we need to authenticate with Basic Auth
      const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
      
      const tokenResponse = await fetch('https://api.x.com/2/oauth2/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${basicAuth}`
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code,
          redirect_uri: redirectUri,
          code_verifier: codeVerifier,
        }),
      });
      
      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          console.error('Error parsing Twitter token exchange error:', e);
          errorData = { raw_response: errorText };
        }
        
        console.error('Twitter token exchange error:', {
          status: tokenResponse.status,
          statusText: tokenResponse.statusText,
          data: errorData
        });
        
        return NextResponse.redirect(new URL(`/home?error=token_exchange&code=${tokenResponse.status}`, request.url));
      }
      
      const tokenData = await tokenResponse.json();
      const { access_token, expires_in, refresh_token, refresh_token_expires_in, scope } = tokenData;
      
      // Calculate actual expiration timestamps as ISO strings for Supabase
      const now = new Date();
      const expiresAt = new Date(now.getTime() + (expires_in || 0) * 1000);
      const refreshTokenExpiresAt = refresh_token_expires_in 
        ? new Date(now.getTime() + refresh_token_expires_in * 1000) 
        : null;
      
      // Format as ISO strings for PostgreSQL timestamp compatibility
      const expiresAtISO = expiresAt.toISOString();
      const refreshTokenExpiresAtISO = refreshTokenExpiresAt?.toISOString();
      
      // Get user profile information
      const profileResponse = await fetch('https://api.x.com/2/users/me', {
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });
      
      if (!profileResponse.ok) {
        console.error('Twitter profile fetch error:', await profileResponse.json());
        return NextResponse.redirect(new URL('/home?error=profile_fetch', request.url));
      }

      const profileData = await profileResponse.json();
      console.log('Twitter profile data:', profileData);
      
      // Extract relevant profile data
      const userProfileData = profileData.data || {};
      const name = userProfileData.name;
      const screenName = userProfileData.username;
      const description = userProfileData.description;
      
      // Get profile image URL - ensure we get the full size image
      let profileImageUrl = userProfileData.profile_image_url || null;
      if (profileImageUrl) {
        // Twitter's API returns smaller "_normal" size images by default
        // Replace "_normal" with "" to get the original size
        profileImageUrl = profileImageUrl.replace('_normal', '');
        
        // If using API v2, the URL may already be the full size
        // Log the image URL for debugging
        console.log('Using profile image URL:', profileImageUrl);
      } else {
        console.log('No profile image URL found in the response');
        
        // Fallback: If we have the username but no image URL, fetch it using API v2
        if (screenName) {
          console.log(`Attempting to fetch profile image for ${screenName} as a fallback`);
          
          // Use Twitter API Client Secret as bearer token for app-only auth (or use a dedicated bearer token)
          const bearerToken = process.env.X_API_BEARER_TOKEN || process.env.X_API_CLIENT_SECRET;
          
          if (bearerToken) {
            const fallbackImageUrl = await fetchProfileImageByUsername(screenName, bearerToken);
            if (fallbackImageUrl) {
              profileImageUrl = fallbackImageUrl;
              console.log('Successfully fetched fallback profile image:', profileImageUrl);
            } else {
              console.log('Failed to fetch fallback profile image');
            }
          } else {
            console.log('No bearer token available for fallback image fetch');
          }
        }
      }
      
      try {
        // Use the standard Supabase .from() syntax with upsert to avoid duplicates

        const { error: insertError } = await supabase
          .from('twitterState')
          .upsert({
            user_id: userId,
            state, 
            access_token, 
            expires_in: expiresAtISO, // Store as ISO timestamp for PostgreSQL
            refresh_token: refresh_token || null, 
            refresh_token_expires_in: refreshTokenExpiresAtISO || null, 
            scope: scope || null,
            screen_name: screenName || null,
            name: name || null,
            description: description || null,
            profile_image_url: profileImageUrl
          }, {
            onConflict: 'user_id'
          });
        
        if (insertError) {
          throw insertError;
        }
      } catch (dbError) {
        console.error('Error storing Twitter data:', dbError);
        return NextResponse.redirect(new URL('/home?error=database_error', request.url));
      }
      
      // Successful connection, redirect to the integrations page
      return NextResponse.redirect(new URL('/home?twitter_connected=true', request.url));
      
    } catch (error) {
      console.error('Twitter callback error:', error);
      return NextResponse.redirect(new URL('/home?error=callback_error', request.url));
    }
  } catch (error) {
    console.error('Twitter callback error:', error);
    return NextResponse.redirect(new URL('/home?error=callback_error', request.url));
  }
} 