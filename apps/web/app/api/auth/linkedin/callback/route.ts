import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

/**
 * Handles the LinkedIn OAuth callback
 * Processes the authorization code from LinkedIn and exchanges it for an access token
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authorization code and state from the URL
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');
    
    // Handle LinkedIn error responses
    if (error) {
      console.error(`LinkedIn authorization error: ${error} - ${errorDescription}`);
      return NextResponse.redirect(new URL(`/home?error=${error}`, request.url));
    }
    
    // Validate required parameters
    if (!code || !state) {
      return NextResponse.redirect(new URL('/home?error=invalid_callback', request.url));
    }
    
    // IMPORTANT: In a production application, you should verify the state parameter
    // against the state stored for this user (in a secure HTTP-only cookie or session)
    // This example doesn't implement the verification since we're using localStorage on client side
    
    // Exchange the authorization code for an access token
    const clientId = process.env.NEXT_PUBLIC_LINKED_IN_CLIENT_ID;
    const clientSecret = process.env.LINKED_IN_PRIMARY_CLIENT_SECRET;
    const redirectUri = `${new URL(request.url).origin}/api/auth/linkedin/callback`;
    console.log(`redirectUri: ${redirectUri}, clientId: ${clientId}, clientSecret: ${clientSecret}`);
    
    const tokenResponse = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: redirectUri,
        client_id: clientId!,
        client_secret: clientSecret!,
      }),
    });
    
    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json();
      console.error('LinkedIn token exchange error:', errorData);
      return NextResponse.redirect(new URL('/home?error=token_exchange', request.url));
    }
    
    const tokenData = await tokenResponse.json();
    const { access_token, expires_in, refresh_token, refresh_token_expires_in, scope } = tokenData;
    
    // Calculate actual expiration timestamps as ISO strings for Supabase
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (expires_in || 0) * 1000);
    const refreshTokenExpiresAt = refresh_token_expires_in 
      ? new Date(now.getTime() + refresh_token_expires_in * 1000) 
      : null;
      
    // Format as ISO strings for PostgreSQL timestamp compatibility
    const expiresAtISO = expiresAt.toISOString();
    const refreshTokenExpiresAtISO = refreshTokenExpiresAt?.toISOString();
    
    console.log('expiresAt', expiresAtISO, 'refreshTokenExpiresAt', refreshTokenExpiresAtISO);
    
    // Get user profile information with enhanced fields including profile picture
    const profileResponse = await fetch(
      'https://api.linkedin.com/v2/me?projection=(id,localizedFirstName,localizedLastName,localizedHeadline,profilePicture(displayImage~digitalmediaAsset:playableStreams))', 
      {
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      }
    );
    
    if (!profileResponse.ok) {
      console.error('LinkedIn profile fetch error:', await profileResponse.json());
      return NextResponse.redirect(new URL('/home?error=profile_fetch', request.url));
    }
    
    const profileData = await profileResponse.json();
    console.log('LinkedIn profile data:', profileData);
    
    // Extract profile picture URL if available
    let profilePictureUrl = null;
    try {
      if (profileData.profilePicture && 
          profileData.profilePicture['displayImage~'] && 
          profileData.profilePicture['displayImage~'].elements && 
          profileData.profilePicture['displayImage~'].elements.length > 0) {
        
        const pictureElement = profileData.profilePicture['displayImage~'].elements[0];
        if (pictureElement.identifiers && pictureElement.identifiers.length > 0) {
          profilePictureUrl = pictureElement.identifiers[0].identifier;
        }
      }
    } catch (e) {
      console.error('Error extracting profile picture:', e);
    }
    
    // Store LinkedIn data using Supabase
    const supabase = getSupabaseServerClient();
    
    try {
      // Get current user ID from Supabase
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError || !userData.user) {
        throw new Error('Could not get authenticated user');
      }
      
      const userId = userData.user.id;
      
      // Use the standard Supabase .from() syntax with upsert to avoid duplicates
      const { error: insertError } = await supabase
        .from('linkedInState')
        .upsert({
          user_id: userId,
          state, 
          access_token, 
          expires_in: expiresAtISO, // Store as ISO timestamp for PostgreSQL
          refresh_token: refresh_token || null, 
          refresh_token_expires_in: refreshTokenExpiresAtISO || null, 
          scope: scope || null,
          first_name: profileData.localizedFirstName || null,
          last_name: profileData.localizedLastName || null,
          headline: profileData.localizedHeadline || null,
          profile_picture_url: profilePictureUrl
        }, {
          onConflict: 'user_id'
        });
      
      if (insertError) {
        throw insertError;
      }
    } catch (dbError) {
      console.error('Error storing LinkedIn data:', dbError);
      return NextResponse.redirect(new URL('/home?error=database_error', request.url));
    }
    
    // Successful connection, redirect to the integrations page
    return NextResponse.redirect(new URL('/home?linkedin_connected=true', request.url));
    
  } catch (error) {
    console.error('LinkedIn callback error:', error);
    return NextResponse.redirect(new URL('/home?error=callback_error', request.url));
  }
} 