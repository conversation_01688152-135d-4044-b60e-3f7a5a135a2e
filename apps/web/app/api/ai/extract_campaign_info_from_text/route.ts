import axios from 'axios';

/**
 * @name POST
 * @description POST handler
 */
export const POST =  
  async ( request: any ) => {
    try {
      const body = await request.json();

  

      const res = await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/sb_extract_campaign_info_from_text_v2`, JSON.stringify({
        "campaign_text": "No Campaign Text Provided",
        "brand_name": body.brand_name,
        "brand_brief": body.brand_brief,
        "campaign_goal": body.campaign_goal,
        "language": "English",
        // "language": body.language,
        "product_info": body.product_info || "No product info provided",
        "model_type": "gemini",
    }), {
      headers: {
          'Content-Type': 'application/json',
      },
  })

    return new Response(JSON.stringify(res.data), { status: 200 });
    } catch (error) {
      // return an error response
      return new Response(null, { status: 500 });
    }
  }