import axios from 'axios';

/**
 * @name POST
 * @description POST handler
 */
export const POST =  
  async ( request: any ) => {
    try {
      const body = await request.json();
      console.log({
        "campaign_info": body.campaign_info,
        "idea": body.initial_idea
      })
      // updateConsumption(body.userId, {
      //   text_generations: 1,
      //   total_words_sent: body.initial_idea.length + body.campaign_info.length
      // });
      const res = await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/sb_rewrite_idea_v2`, JSON.stringify({
        "campaign_brief": body.campaign_brief,
        "initial_idea": body.initial_idea,
        "language": "English",
        "product_info": body.product_info || "NO_INFO_PROVIDED",
        "brand_name": body.brand_name || "NO_INFO_PROVIDED",
        "brand_brief": body.brand_brief || "NO_INFO_PROVIDED",
    }), {
      headers: {
          'Content-Type': 'application/json',
      },
  })
    // updateConsumption(body.userId, {
    //   total_words_generated: res.data.updated_idea.length
    // });
    return new Response(JSON.stringify(res.data), { status: 200 });

    } catch (error) {
      // return an error response
      return new Response(null, { status: 500 });
    }
  }