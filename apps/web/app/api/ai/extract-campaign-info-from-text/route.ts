import { NextRequest } from 'next/server';

import { getApi } from '~/utils/api.util';

interface CampaignInfoRequestBody {
  brand_name: string;
  brand_brief: string;
  campaign_goal: string;
  language: string;
  product_info: string;
  campaign_text: string;
}

/**
 * @name POST
 * @description POST handler
 */
export const POST = async (request: NextRequest) => {
  try {
    const body = (await request.json()) as CampaignInfoRequestBody;
    const response = await getApi().post(
      '/sb_extract_campaign_info_from_text_v2',
      JSON.stringify({
        brand_name: body.brand_name,
        brand_brief: body.brand_brief,
        campaign_goal: body.campaign_goal,
        language: body.language,
        product_info: body.product_info,
        campaign_text: body.campaign_text,
      }),
    );

    return new Response(JSON.stringify(response.data), { status: 200 });
  } catch (error) {
    // return an error response
    return new Response(JSON.stringify(error), { status: 500 });
  }
};
