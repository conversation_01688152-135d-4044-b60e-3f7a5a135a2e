import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Extract the campaign topic and other form data
    const { data, type, timeFilter, topic } = body;
    
    // if (!campaignTopic || !campaignTopic.trim()) {
    //   return NextResponse.json(
    //     { error: 'Campaign topic is required' },
    //     { status: 400 }
    //   );
    // }

    // Prepare the payload for the backend service


    // Call the backend market research service
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      return NextResponse.json(
        { error: 'Backend URL not configured' },
        { status: 500 }
      );
    }

    const response = await fetch(`${backendUrl}/market-research`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: "sonar-deep-research",
        query:  `${topic ? `Using the following topic or research question as the starting point: ${topic} ` : ''} Identify and research the top five to ten ${type} relevant for the following   ICP & persona is facing, by doing deep research and using the following as the starting point. Do not use the ICP/Persona as your sources.: ${JSON.stringify(data)} Use sources from the last ${timeFilter}. `,
        "systemPrompt": "You are a helpful AI research assistant that provides structured, insight-rich responses for marketing teams. Rules: 1. Provide only the final answer. Do not include intermediate steps. 2. Structure your response as key  insights array summary. 3. Do not repeat the input or over-explain your choices. 4. Do not under any circumstances use information that is not coming from a confirmed URL that exists. "  
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend service error:', errorText);
      return NextResponse.json(
        { error: 'Failed to generate market research' },
        { status: response.status }
      );
    }

    const result = await response.json();
    
    // Return the research data from the backend response
    return NextResponse.json({
      success: true,
      research: result.data?.research || [],
      contentSuggestions: result.data?.content_suggestions || [],
      metadata: result.metadata || {}
    });

  } catch (error) {
    console.error('Market research API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
