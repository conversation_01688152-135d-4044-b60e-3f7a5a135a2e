import { NextRequest } from 'next/server';

import { getApi } from '~/utils/api.util';

interface BrandInfoRequestBody {
  brand_text: string;
  brand_name: string;
}

/**
 * @name POST
 * @description POST handler
 */
export const POST = async (request: NextRequest) => {
  try {
    const body = (await request.json()) as BrandInfoRequestBody;

    const response = await getApi().post(
      '/sb_extract_brand_info_from_text_v2',
      JSON.stringify({
        brand_text: JSON.stringify(body.brand_text),
        brand_name: body.brand_name,
        language: 'en',
      }),
    );
    return new Response(JSON.stringify(response.data), { status: 200 });
  } catch (error) {
    // return an error response
    return new Response(JSON.stringify(error), { status: 500 });
  }
};
