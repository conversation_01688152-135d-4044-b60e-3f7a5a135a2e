import OpenAI from 'openai';
import { z } from 'zod';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

const requestSchema = z.object({
  audioUrl: z.string().url(),
  fileName: z.string(),
  folderName: z.string(),
  companyId: z.string().uuid(),
});

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'sk-proj-1234567890',
});

async function downloadFile(url: string): Promise<Buffer> {
  const response = await fetch(url);
  return Buffer.from(await response.arrayBuffer());
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { audioUrl, fileName, folderName, companyId } =
      requestSchema.parse(body);

    // Download the audio file from the URL
    const audioBuffer = await downloadFile(audioUrl);

    // Create a Blob from the buffer
    const audioBlob = new Blob([audioBuffer]);
    const file = new File([audioBlob], fileName, { type: 'audio/mpeg' });

    // Use Whisper API to transcribe the audio
    const transcription = await openai.audio.transcriptions.create({
      file: file,
      model: 'whisper-1',
      response_format: 'text',
    });

    // Store the transcript in the database
    const supabase = await getSupabaseServerClient();

    const { data, error } = await supabase
      .from('product_documents')
      .insert({
        title: `Transcript: ${fileName}`,
        content: transcription,
        file_path: `${companyId}/assets/${folderName}/${fileName}`,
        company_id: companyId,
        file_type: 'transcript',
      })
      .select()
      .single();

    if (error) {
      console.error('Error storing transcript:', error);
      return new Response(
        JSON.stringify({ error: 'Failed to store transcript' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      );
    }

    return new Response(
      JSON.stringify({
        transcript: transcription,
        document: data,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  } catch (error) {
    console.error('Error processing audio:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to process audio file' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}
