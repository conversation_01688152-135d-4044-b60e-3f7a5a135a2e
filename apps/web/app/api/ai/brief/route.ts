/**
 * @name POST
 * @description POST handler
 */
import { NextRequest } from 'next/server';

import { getApi } from '~/utils/api.util';

interface BriefRequestBody {
  brand_name: string;
  brand_brief: string;
  campaign_brief: string;
  product_info: string;
  idea: string;
  languages: string[];
  supported_content_types: string[];
  supported_channels: string[];
}

export const POST = async (request: NextRequest) => {
  try {
    const body = (await request.json()) as BriefRequestBody;

    const res = await getApi().post('/sb_generate_creative_brief_v2', {
      brand_name: body.brand_name || "NOT_PROVIDED",
      brand_brief: body.brand_brief  || "NOT_PROVIDED",
      campaign_brief: body.campaign_brief  ||"NOT_PROVIDED",
      product_info: body.product_info || "NOT_PROVIDED",
      idea: body.idea  || "NOT_PROVIDED",
      content_languages: body.languages || ['English'],
      language: 'English',
      supported_content_types: body.supported_content_types || "NOT_PROVIDED",
      supported_channels: body.supported_channels || "NOT_PROVIDED",
    });


    return new Response(JSON.stringify(res.data), { status: 200 });
  } catch (error) {
    // return an error response
    return new Response(JSON.stringify(error), { status: 500 });
  }
};
