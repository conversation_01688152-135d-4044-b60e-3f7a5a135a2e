import axios from 'axios';

/**
 * @name POST
 * @description POST handler
 */
export const POST = async (request: Request) => {
  try {
    const body = await request.json();
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/sb_generate_idea_visual_description_v5`,
      {
        brand_name: body.brand_name,
        brand_brief: JSON.stringify(body.brand_brief)|| "NOT_PROVIDED",
        campaign_brief: JSON.stringify(body.campaign_info) || "NOT_PROVIDED",
        creative_brief: JSON.stringify(body.creative_brief)|| "NOT_PROVIDED",
        product_info: JSON.stringify(body.product_info)|| "NOT_PROVIDED",
        generated_content: body.generated_content|| "NOT_PROVIDED",
        initial_visual_desc: body.initial_visual_desc,
        language: "English",
        image_gen_styles: JSON.stringify(body.image_gen_styles)
      }
    );

    // Ensure we're returning the visual description from the response
    return new Response(JSON.stringify({
      visual_description: response.data
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    return new Response(JSON.stringify({ error: 'Failed to generate visual description' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};