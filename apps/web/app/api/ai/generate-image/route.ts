import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const supabase = await getSupabaseServerClient();
    // Generate image
    const res = await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/sb_generate_download_image_replicate_v2`, {
      image_prompt: body.image_prompt,
      aspect_ratio: body.aspect_ratio,
      prompt_upsampling: "false",
      language: "English"
    }, {
      responseType: 'arraybuffer',
    });

    const contentType = res.headers['content-type'];
    const imageBuffer = Buffer.from(res.data);
    const extension = contentType.split('/')[1];
    const fileName = `${body.company_id}/generated/${crypto.randomUUID()}.${extension}`;

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('generated')
      .upload(fileName, imageBuffer, {
        contentType,
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      throw uploadError;
    }

    const { data: { publicUrl } } = supabase.storage
      .from('generated')
      .getPublicUrl(fileName);

    return NextResponse.json({ url: publicUrl, path: fileName });

  } catch (error) {
    return NextResponse.json({ error: 'Failed to generate image' }, { status: 500 });
  }
}