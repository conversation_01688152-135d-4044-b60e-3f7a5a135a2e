import { NextResponse } from 'next/server';

const PEXELS_API_KEY = process.env.NEXT_PUBLIC_PEXELS_API_KEY;

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');

  if (!query) {
    return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
  }

  try {
    const response = await fetch(
      `https://api.pexels.com/v1/search?query=${encodeURIComponent(query)}&per_page=30`,
      {
        headers: {
          Authorization: PEXELS_API_KEY!
        }
      }
    );

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Pexels API error:', error);
    return NextResponse.json({ error: 'Failed to fetch stock images' }, { status: 500 });
  }
}
 