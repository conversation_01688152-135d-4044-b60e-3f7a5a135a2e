# Ayrshare Webhook Implementation

This document explains the Ayrshare webhook implementation for handling social profile connection events.

## Overview

The Ayrshare webhook integration allows the application to receive real-time notifications when users connect, disconnect, or need to refresh their social media accounts. This is essential for maintaining accurate connection status and notifying users of any issues.

## Webhook URL

The webhook endpoint is available at:
```
https://your-domain.com/api/integrations/ayrshare/webhook
```

## Environment Variables

The following environment variables are required for the webhook functionality:

### Required

- `AYRSHARE_API_KEY`: Your Ayrshare API key for authenticating with the Ayrshare API
- `NEXT_PUBLIC_SITE_URL`: The base URL of your application (used for webhook registration)

### Optional

- `AYRSHARE_WEBHOOK_SECRET`: Secret key for HMAC verification (recommended for production)
- `VERCEL_URL`: Fallback URL for webhook registration (automatically set in Vercel deployments)

## Setup Instructions

### 1. Environment Variables

Add the required environment variables to your `.env.local` file:

```env
# Required
AYRSHARE_API_KEY=your_ayrshare_api_key_here
NEXT_PUBLIC_SITE_URL=https://your-domain.com

# Optional but recommended for production
AYRSHARE_WEBHOOK_SECRET=your_webhook_secret_here
```

### 2. Webhook Registration

Webhooks are automatically registered when users create new social profiles. The registration happens in the `AddSocialProfileDialog` component and includes:

- **Action**: `social` (monitors social account connections)
- **URL**: `https://your-domain.com/api/integrations/ayrshare/webhook`
- **Secret**: HMAC secret for verification (if configured)

### 3. Security

The webhook implementation includes HMAC-SHA256 verification for security:

- **Headers**: `X-Authorization-Content-SHA256` and `X-Authorization-Timestamp`
- **Verification**: Compares the received signature with a calculated HMAC
- **Fallback**: If no secret is configured, verification is skipped (not recommended for production)

## Webhook Events

The webhook handles the following social action events:

### Link Event
```json
{
  "action": "social",
  "type": "link",
  "platform": "linkedin",
  "displayName": "John Doe",
  "refId": "profile_reference_id",
  "source": "user",
  "created": "2023-01-05T01:18:47Z",
  "hookId": "unique_hook_id"
}
```

### Unlink Event
```json
{
  "action": "social",
  "type": "unlink", 
  "platform": "linkedin",
  "refId": "profile_reference_id",
  "source": "system",
  "details": {
    "status": "error",
    "code": 349,
    "message": "Account locked"
  },
  "created": "2023-01-05T01:18:47Z",
  "hookId": "unique_hook_id"
}
```

### Refresh Event
```json
{
  "action": "social",
  "type": "refresh",
  "platform": "linkedin",
  "refId": "profile_reference_id", 
  "refreshBy": "2022-11-05T12:21:29Z",
  "source": "system",
  "created": "2023-01-05T01:18:47Z",
  "hookId": "unique_hook_id"
}
```

## Logging

The webhook implementation includes comprehensive logging:

- **Request Receipt**: Logs when webhook is received
- **HMAC Verification**: Logs verification success/failure
- **Event Details**: Logs all webhook event details
- **Error Handling**: Logs any processing errors

## Current Implementation

The current webhook implementation:

1. **Receives** webhook POST requests
2. **Verifies** HMAC signature (if secret is configured)
3. **Parses** webhook payload
4. **Logs** all event details for debugging
5. **Updates Database**: Automatically updates `ayrshare_social_profiles` table with current connection status
6. **Fetches Profile Details**: Retrieves updated profile information from Ayrshare API
7. **Responds** with appropriate HTTP status codes

## Database Operations

The webhook uses a **normalized database structure** where each platform connection is stored as a separate row in the `ayrshare_social_profiles` table. This eliminates array management complexity and race conditions.

### Link Events (`type: "link"`)
When a user connects a social account:

1. **Finds the profile** by `refId` in the `ayrshare_user_profile` table
2. **Fetches platform-specific details** from the Ayrshare API using the profile key
3. **Inserts/updates a single row** in `ayrshare_social_profiles` table with:
   - Platform identifier (`platform`)
   - Display name and username
   - Profile image and URL
   - Connection metadata (subscription type, verification status)
   - Connection timestamp and status

### Unlink Events (`type: "unlink"`)
When a user disconnects a social account:

1. **Finds the profile** by `refId` in the `ayrshare_user_profile` table
2. **Deletes the specific platform row** from `ayrshare_social_profiles` table
3. **Alternative**: Marks the connection as `is_connected = false` (if deletion fails)

### Refresh Events (`type: "refresh"`)
When an account needs re-authorization:

1. **Finds the platform connection** by `refId` and `platform`
2. **Updates the `refresh_required` timestamp** for that specific platform
3. **Logs the refresh requirement** for user notifications

## Benefits of Normalized Structure

- **No Race Conditions**: Each platform operation is independent
- **Atomic Operations**: Single row insert/update/delete operations
- **Better Performance**: No array filtering or JSON manipulation
- **Scalable**: Easy to add new platform-specific fields
- **Query Efficiency**: Simple JOINs instead of array operations

This approach provides real-time accuracy with better database performance and eliminates frontend complexity.

## Future Enhancements

Planned improvements include:

- **User Notifications**: Send notifications to users about connection changes
- **Real-time Updates**: Trigger UI updates via websockets
- **Retry Logic**: Handle failed webhook processing with retries

## Testing

To test the webhook implementation:

1. **Create a profile** using the `AddSocialProfileDialog`
2. **Connect social accounts** via the Ayrshare interface
3. **Check logs** to verify webhook events are received
4. **Disconnect accounts** to test unlink events

## Support

For issues related to webhook implementation:

1. Check the application logs for webhook events
2. Verify environment variables are set correctly
3. Test webhook registration manually if needed
4. Contact Ayrshare support for API-related issues 