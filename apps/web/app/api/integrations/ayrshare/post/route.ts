import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

export async function POST(request: NextRequest) {
  const logger = await getLogger();
  
  try {
    // Get user from session
    const supabase = getSupabaseServerClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      logger.warn('Unauthorized request to Ayrshare post endpoint');
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    logger.info('Ayrshare post request started', { userId: user.id });

    // Parse request body
    const body = await request.json();
    const { 
      post, 
      platforms = ['linkedin'], 
      mediaUrls, 
      scheduleDate,
      shortenLinks = false,
      disableComments = false,
      linkedInOptions,
      companyId ,
      profileKey
    } = body;
    console.log("body", body, body.profileKey);
    if (!post) {
      return NextResponse.json({ success: false, error: 'Post content is required' }, { status: 400 });
    }

    if (!companyId) {
      return NextResponse.json({ success: false, error: 'Company ID is required' }, { status: 400 });
    }

    // Get user's Ayrshare profile using existing pattern
    // const { data: ayrshareProfiles, error: profileError } = await (supabase as any)
    //   .from('ayrshare_user_profile')
    //   .select('*')
    //   .eq('user_id', user.id)
    //   .eq('company_id', companyId);

    // if (profileError || !ayrshareProfiles || (ayrshareProfiles as any[]).length === 0) {
    //   logger.error('No Ayrshare profile found for user', { userId: user.id, companyId, error: profileError });
    //   return NextResponse.json({ 
    //     success: false, 
    //     error: 'Ayrshare profile not found. Please connect your Ayrshare account first.' 
    //   }, { status: 400 });
    // }

    // const ayrshareProfile = (ayrshareProfiles as any[])[0];

    if (!profileKey) {
      logger.error('Missing profile key for Ayrshare profile', { userId: user.id, companyId });
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid Ayrshare profile configuration. Missing profile key.' 
      }, { status: 400 });
    }

    // Prepare Ayrshare API request
    const ayrsharePayload: any = {
      post,
      platforms
    };

    // Add optional parameters if provided
    if (mediaUrls && mediaUrls.length > 0) {
      ayrsharePayload.mediaUrls = mediaUrls;
    }

    if (scheduleDate) {
      ayrsharePayload.scheduleDate = scheduleDate;
    }

    if (shortenLinks) {
      ayrsharePayload.shortenLinks = shortenLinks;
    }

    if (disableComments) {
      ayrsharePayload.disableComments = disableComments;
    }

    if (linkedInOptions) {
      ayrsharePayload.linkedInOptions = linkedInOptions;
    }

    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`
    };

    // Add Profile-Key header for user-specific posting
    if (profileKey) {
      headers['Profile-Key'] = profileKey;
    }
    console.log("ayrsharePayload", ayrsharePayload);
    console.log('Making request to Ayrshare API', { 
      platforms, 
      hasMedia: !!(mediaUrls && mediaUrls.length > 0),
      isScheduled: !!scheduleDate 
    });

    // Make request to Ayrshare API
    const ayrshareResponse = await fetch('https://api.ayrshare.com/api/post', {
      method: 'POST',
      headers,
      body: JSON.stringify(ayrsharePayload)
    });

    const responseData = await ayrshareResponse.json();

    if (!ayrshareResponse.ok) {
      // Log detailed error information
      console.log('Ayrshare API request failed', { 
        status: ayrshareResponse.status, 
        response: responseData,
        requestPayload: ayrsharePayload,
        headers: { ...headers, 'Authorization': 'Bearer [REDACTED]' } // Hide API key
      });
      
      // Extract specific error details from posts array if available
      let specificErrors = [];
      if (responseData.posts && Array.isArray(responseData.posts)) {
        specificErrors = responseData.posts.map((post: any) => ({
          platform: post.platform,
          status: post.status,
          errors: post.errors || [],
          message: post.message
        }));
        console.log('Detailed Ayrshare post errors:', specificErrors);
      }
      
      // Log full response structure for debugging
      console.log('Full Ayrshare response:', JSON.stringify(responseData, null, 2));
      
      return NextResponse.json({
        success: false,
        error: 'Failed to publish post via Ayrshare',
        details: responseData,
        specificErrors
      }, { status: ayrshareResponse.status });
    }

    logger.info('Successfully posted via Ayrshare', { 
      ayrsharePostId: responseData.id,
      status: responseData.status 
    });

    // Extract post URLs from successful posts
    const postUrls: Record<string, string> = {};
    let primaryPostUrl = '';
    console.log("responseData", JSON.stringify(responseData, null, 2));
    
    // Handle the nested structure: responseData.posts[].postIds[]
    if (responseData.posts && Array.isArray(responseData.posts)) {
      responseData.posts.forEach((post: any) => {
        if (post.postIds && Array.isArray(post.postIds)) {
          post.postIds.forEach((postId: any) => {
            if (postId.status === 'success' && postId.postUrl) {
              postUrls[postId.platform] = postId.postUrl;
              // Use the first successful post URL as primary
              if (!primaryPostUrl) {
                primaryPostUrl = postId.postUrl;
              }
            }
          });
        }
      });
    }

    console.log('Extracted post URLs:', postUrls);
    console.log('Primary post URL:', primaryPostUrl);

    // Return success response with Ayrshare data and extracted URLs
    return NextResponse.json({
      success: true,
      data: responseData,
      postUrls,
      primaryPostUrl,
      message: 'Post successfully published via Ayrshare'
    });

  } catch (error: any) {
    console.log('Error in Ayrshare post API', { error: error.message, stack: error.stack });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
} 

// PATCH handler for pausing/unpausing scheduled posts
export async function PATCH(request: NextRequest) {
  const logger = await getLogger();
  
  try {
    // Get user from session
    const supabase = getSupabaseServerClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      logger.warn('Unauthorized request to Ayrshare patch endpoint');
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    logger.info('Ayrshare patch request started', { userId: user.id });

    // Parse request body
    const body = await request.json();
    const { 
      ayrsharePostId,
      action, // 'pause' or 'unpause'
      companyId,
      profileKey,
      contentId,
      newScheduleDate // Optional new schedule date for unpause
    } = body;

    if (!ayrsharePostId) {
      return NextResponse.json({ success: false, error: 'Ayrshare post ID is required' }, { status: 400 });
    }

    if (!action || !['pause', 'unpause'].includes(action)) {
      return NextResponse.json({ success: false, error: 'Valid action (pause/unpause) is required' }, { status: 400 });
    }

    if (!companyId) {
      return NextResponse.json({ success: false, error: 'Company ID is required' }, { status: 400 });
    }

    if (!profileKey) {
      logger.error('Missing profile key for Ayrshare profile', { userId: user.id, companyId });
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid Ayrshare profile configuration. Missing profile key.' 
      }, { status: 400 });
    }

    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
      'Profile-Key': profileKey
    };

    console.log('ayrsharePostId', process.env.AYRSHARE_API_KEY, profileKey );
    
    // First, check the post status to ensure it can be paused
    logger.info('Checking post status before pause operation', { ayrsharePostId });
    
    const statusResponse = await fetch(`https://api.ayrshare.com/api/post/${ayrsharePostId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
        'Profile-Key': profileKey
      }
    });
    
    const statusData = await statusResponse.json();
    console.log('Post status response:', JSON.stringify(statusData, null, 2));
    
    if (!statusResponse.ok) {
      logger.error('Failed to get post status', { 
        status: statusResponse.status,
        response: statusData,
        ayrsharePostId
      });
      return NextResponse.json({
        success: false,
        error: 'Failed to get post status',
        details: statusData
      }, { status: statusResponse.status });
    }

    // Check if the post is in a valid state for pausing
    const postStatus = statusData.status;
    const hasScheduleDate = statusData.scheduleDate;
    
    if (postStatus !== 'pending') {
      logger.warn('Post is not in pending status', { 
        ayrsharePostId,
        currentStatus: postStatus,
        hasScheduleDate
      });
      return NextResponse.json({
        success: false,
        error: `Post cannot be ${action}d. Current status: ${postStatus}. Only posts with 'pending' status can be paused.`,
        details: { currentStatus: postStatus, hasScheduleDate }
      }, { status: 400 });
    }

    if (!hasScheduleDate) {
      logger.warn('Post was not originally scheduled', { 
        ayrsharePostId,
        postStatus
      });
      return NextResponse.json({
        success: false,
        error: 'Post cannot be paused. It was not originally scheduled.',
        details: { currentStatus: postStatus, hasScheduleDate }
      }, { status: 400 });
    }

    // Prepare request payload
    const patchPayload = {
      id: ayrsharePostId,
      scheduleDate: newScheduleDate || hasScheduleDate, // Always required even though docs say optional
      scheduledPause: action === 'pause'
    };

    console.log('patchPayload', patchPayload);
    console.log('Full request details:', {
      url: 'https://api.ayrshare.com/api/post',
      method: 'PATCH',
      headers: { ...headers, 'Authorization': 'Bearer [REDACTED]' },
      body: patchPayload
    });
    
    logger.info(`${action}ing scheduled post`, { 
      ayrsharePostId,
      action,
      companyId,
      currentStatus: postStatus
    });

    // Make PATCH request to Ayrshare API
    const ayrshareResponse = await fetch('https://api.ayrshare.com/api/post', {
      method: 'PATCH',
      headers,
      body: JSON.stringify(patchPayload)
    });

    const responseData = await ayrshareResponse.json();

    console.log('Ayrshare PATCH response:', JSON.stringify(responseData, null, 2));

    if (!ayrshareResponse.ok) {
      console.log('Ayrshare PATCH request failed', { 
        status: ayrshareResponse.status, 
        response: responseData,
        requestPayload: patchPayload
      });
      
      return NextResponse.json({
        success: false,
        error: `Failed to ${action} scheduled post`,
        details: responseData
      }, { status: ayrshareResponse.status });
    }

    logger.info(`Successfully ${action}d scheduled post`, { 
      ayrsharePostId,
      status: responseData.status 
    });

    // Verify the pause actually worked by checking the post status after a brief delay
    if (action === 'pause') {
      try {
        // Wait a moment for Ayrshare to process the pause request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const verifyResponse = await fetch(`https://api.ayrshare.com/api/post/${ayrsharePostId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
            'Profile-Key': profileKey
          }
        });
        
        const verifyData = await verifyResponse.json();
        console.log('Post status after pause attempt:', JSON.stringify(verifyData, null, 2));
        
        // Check if the post is actually paused (should have scheduledPause: true)
        if (verifyData.scheduledPause !== true) {
          console.warn('⚠️  Post may not be properly paused. Expected scheduledPause: true, got:', verifyData.scheduledPause);
          
          // Return an error if the pause didn't actually work
          return NextResponse.json({
            success: false,
            error: 'Post pause was not successful. The post may still be scheduled to publish.',
            details: { 
              ayrshareResponse: responseData,
              postStatus: verifyData,
              expectedPauseStatus: true,
              actualPauseStatus: verifyData.scheduledPause
            }
          }, { status: 400 });
        } else {
          console.log('✅ Post successfully paused - scheduledPause: true');
        }
      } catch (verifyError) {
        console.error('Failed to verify post status after pause:', verifyError);
        // Don't fail the entire request if verification fails
      }
    }

    // Update the company_content record if contentId is provided
    if (contentId) {
      const { error: updateError } = await supabase
        .from('company_content')
        .update({ is_paused: action === 'pause' } as any)
        .eq('id', contentId)
        .eq('company_id', companyId);

      if (updateError) {
        logger.error('Failed to update company_content record', { 
          error: updateError, 
          contentId,
          companyId 
        });
        // Don't fail the entire request if database update fails
      }
    }

    // Return success response
    return NextResponse.json({
      success: true,
      data: responseData,
      message: `Post successfully ${action}d`
    });

  } catch (error: any) {
    logger.error('Error in Ayrshare patch API', { error: error.message, stack: error.stack });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
} 