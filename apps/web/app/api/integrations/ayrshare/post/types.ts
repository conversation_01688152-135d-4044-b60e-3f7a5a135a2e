export interface AyrsharePostRequest {
  post: string;
  platforms?: string[];
  mediaUrls?: string[];
  scheduleDate?: string;
  shortenLinks?: boolean;
  disableComments?: boolean;
  linkedInOptions?: {
    title?: string;
    description?: string;
  };
  companyId: string;
}

export interface AyrsharePostResponse {
  success: boolean;
  data?: {
    status: string;
    errors: any[];
    postIds: Array<{
      status: string;
      id: string;
      postUrl?: string;
      platform: string;
      owner?: string;
    }>;
    id: string; // Ayrshare Post ID used for delete, analytics, comments, etc.
  };
  postUrls?: Record<string, string>; // Platform-specific post URLs
  primaryPostUrl?: string; // The first successful post URL
  message?: string;
  error?: string;
  details?: any;
  specificErrors?: Array<{
    platform: string;
    status: string;
    errors: any[];
    message?: string;
  }>;
} 