# Ayrshare Integration - LinkedIn Posting

This directory contains the implementation for posting to LinkedIn via the Ayrshare API.

## Overview

The Ayrshare integration allows users to post content to LinkedIn through their connected Ayrshare profiles. It includes:

- Server-side API endpoint for secure posting
- Client-side utilities for making API calls
- React hooks for easy integration
- TypeScript types for type safety

## Prerequisites

1. **Environment Variables**: Ensure the following are set in your environment:
   - `AYRSHARE_API_KEY`: Your Ayrshare API key
   - `AYRSHARE_PRIVATE_KEY`: Base64 encoded private key for JWT generation
   - `AYRSHARE_DOMAIN`: Your Ayrshare domain

2. **Database Table**: The `ayrshare_user_profile` table must exist with the following structure:
   ```sql
   CREATE TABLE public.ayrshare_user_profile (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
     title TEXT,
     "refId" TEXT,
     "profileKey" TEXT,
     "messagingActive" BOOLEAN,
     company_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE
   );
   ```

3. **User Profile Setup**: Users must have an Ayrshare profile connected with a valid `profileKey`.

## API Endpoint

### POST `/api/integrations/ayrshare/post`

Posts content to social media platforms via Ayrshare.

**Request Body:**
```typescript
{
  post: string;                    // Required: Post content
  platforms?: string[];            // Optional: Default ['linkedin']
  mediaUrls?: string[];           // Optional: Image/video URLs
  scheduleDate?: string;          // Optional: UTC datetime for scheduling
  shortenLinks?: boolean;         // Optional: Default false
  disableComments?: boolean;      // Optional: Default false
  linkedInOptions?: {             // Optional: LinkedIn-specific options
    title?: string;
    description?: string;
  };
  companyId: string;              // Required: Company/account ID
}
```

**Response:**
```typescript
{
  success: boolean;
  data?: {
    status: string;
    errors: any[];
    postIds: Array<{
      status: string;
      id: string;
      postUrl?: string;
      platform: string;
      owner?: string;
    }>;
    id: string; // Ayrshare Post ID
  };
  message?: string;
  error?: string;
  details?: any;
}
```

## Client-Side Usage

### Using the React Hook

```tsx
import { useAyrsharePost } from '@/api/integrations/ayrshare/post';

function MyComponent() {
  const { postToLinkedIn, loading, error } = useAyrsharePost();

  const handlePost = async () => {
    try {
      const result = await postToLinkedIn(
        "Hello LinkedIn!",
        "company-id-here",
        {
          mediaUrls: ["https://example.com/image.jpg"],
          shortenLinks: true
        }
      );
      
      if (result.success) {
        console.log('Posted successfully!');
      }
    } catch (err) {
      console.error('Failed to post:', err);
    }
  };

  return (
    <button onClick={handlePost} disabled={loading}>
      {loading ? 'Posting...' : 'Post to LinkedIn'}
    </button>
  );
}
```

### Using Client Utilities Directly

```typescript
import { postToLinkedIn } from '@/api/integrations/ayrshare/post';

async function publishPost() {
  try {
    const result = await postToLinkedIn(
      "My post content",
      "company-id",
      {
        mediaUrls: ["https://example.com/image.jpg"],
        scheduleDate: "2024-12-31T12:00:00Z"
      }
    );
    
    return result;
  } catch (error) {
    console.error('Post failed:', error);
    throw error;
  }
}
```

## Features Supported

### Basic Posting
- ✅ Text posts to LinkedIn
- ✅ Posts with images
- ✅ Multiple platform support (configured for LinkedIn)

### Advanced Features
- ✅ Scheduled posting
- ✅ Link shortening
- ✅ Comment disabling
- ✅ LinkedIn-specific options
- ✅ Error handling and logging

### Security
- ✅ User authentication required
- ✅ Company-specific profile access
- ✅ RLS policies for data access
- ✅ Server-side API key protection

## Error Handling

The implementation includes comprehensive error handling:

1. **Authentication Errors**: 401 if user not authenticated
2. **Validation Errors**: 400 for missing required fields
3. **Profile Errors**: 400 if Ayrshare profile not found or invalid
4. **API Errors**: Forwards Ayrshare API errors with details
5. **Server Errors**: 500 for unexpected errors with logging

## Logging

All operations are logged using the application's logging system:
- Request initiation
- Profile lookups
- API calls to Ayrshare
- Success/failure states
- Error details

## Rate Limiting

Rate limiting is handled by Ayrshare's API. Check your Ayrshare plan for specific limits.

## Testing

You can test the implementation by:

1. Ensuring you have a connected Ayrshare profile
2. Using the example component in `post/example-usage.tsx`
3. Making direct API calls to test the endpoint

## Troubleshooting

### Common Issues

1. **"Ayrshare profile not found"**
   - Ensure user has connected their Ayrshare account
   - Verify `companyId` matches the profile's company

2. **"Missing profile key"**
   - Profile was created but `profileKey` is null
   - User needs to reconnect their Ayrshare account

3. **Ayrshare API errors**
   - Check your API key and quota
   - Verify the post content meets platform requirements
   - Check media URL accessibility

## Future Enhancements

Potential improvements:
- Support for other platforms (Twitter, Facebook, etc.)
- Bulk posting capabilities
- Post analytics integration
- Draft saving functionality
- Media upload handling 