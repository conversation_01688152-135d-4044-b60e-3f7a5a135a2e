import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ postId: string }> }
) {
  const logger = await getLogger();
  
  try {
    // Get user from session
    const supabase = getSupabaseServerClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      logger.warn('Unauthorized request to Ayrshare status endpoint');
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    const { postId } = await params;
    const { searchParams } = new URL(request.url);
    const profileKey = searchParams.get('profileKey');
    const companyId = searchParams.get('companyId');

    if (!postId) {
      return NextResponse.json({ success: false, error: 'Post ID is required' }, { status: 400 });
    }

    if (!profileKey) {
      return NextResponse.json({ success: false, error: 'Profile key is required' }, { status: 400 });
    }

    if (!companyId) {
      return NextResponse.json({ success: false, error: 'Company ID is required' }, { status: 400 });
    }

    logger.info('Fetching post status from Ayrshare', { 
      postId, 
      userId: user.id, 
      companyId 
    });

    // Prepare headers
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
      'Profile-Key': profileKey
    };

    // Make request to Ayrshare API
    const ayrshareResponse = await fetch(`https://api.ayrshare.com/api/post/${postId}`, {
      method: 'GET',
      headers
    });

    const responseData = await ayrshareResponse.json();

    if (!ayrshareResponse.ok) {
      logger.error('Failed to fetch post status from Ayrshare', { 
        status: ayrshareResponse.status, 
        response: responseData,
        postId
      });
      
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch post status',
        details: responseData
      }, { status: ayrshareResponse.status });
    }

    logger.info('Successfully fetched post status from Ayrshare', { 
      postId,
      status: responseData.status 
    });

    // Return the status data from Ayrshare
    return NextResponse.json({
      success: true,
      data: responseData,
      message: 'Post status fetched successfully'
    });

  } catch (error: any) {
    const resolvedParams = await params;
    logger.error('Error fetching post status from Ayrshare', { 
      error: error.message, 
      stack: error.stack,
      postId: resolvedParams.postId
    });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
} 