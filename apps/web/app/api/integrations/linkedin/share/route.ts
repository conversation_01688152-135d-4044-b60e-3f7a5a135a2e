import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLinkedInProfile } from '~/services/linkedin';

export async function POST(request: NextRequest) {
  try {
    // Get user from session
    const supabase = getSupabaseServerClient();
    const { data: { user } } = await supabase.auth.getUser();
    console.log("User ID:", user?.id);
    
    if (!user) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const { content, image } = await request.json();
    console.log("Content to share:", content?.substring(0, 100) + (content?.length > 100 ? '...' : ''));
    console.log("Image to share:", image ? 'Image provided' : 'No image');
    
    if (!content) {
      return NextResponse.json({ success: false, error: 'Content is required' }, { status: 400 });
    }

    // Get LinkedIn profile with access token
    const profile = await getLinkedInProfile(user.id);
    console.log("LinkedIn profile found:", profile ? 'Yes' : 'No');
    
    if (!profile || !profile.access_token) {
      return NextResponse.json({ 
        success: false, 
        error: 'LinkedIn account not connected or missing access token' 
      }, { status: 400 });
    }

    // Check if we have the person URN stored, if not get it and store it
    let personUrn = profile.person_urn;
    console.log("Person URN from DB:", personUrn);
    
    if (!personUrn || !personUrn.startsWith('urn:li:person:')) {
      console.log("Fetching person URN from LinkedIn API");
      try {
        // Fetch the LinkedIn profile to get the proper URN
        const meResponse = await fetch('https://api.linkedin.com/v2/me', {
          headers: {
            'Authorization': `Bearer ${profile.access_token}`,
            'X-Restli-Protocol-Version': '2.0.0'
          }
        });
        
        if (!meResponse.ok) {
          throw new Error(`Failed to fetch LinkedIn profile: ${meResponse.status}`);
        }
        
        const data = await meResponse.json();
        console.log("LinkedIn /me response:", data);
        
        // Update the URN in our database
        personUrn = `urn:li:person:${data.id}`;
        console.log("New person URN:", personUrn);
        
        await supabase
          .from('linkedInState')
          .update({ person_urn: personUrn })
          .eq('user_id', user.id);
          
        console.log("Updated person URN in database");
      } catch (error) {
        console.error("Error fetching LinkedIn profile:", error);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch LinkedIn profile'
        }, { status: 500 });
      }
    }

    console.log("Preparing post payload");
    
    try {
      let activityId;
      let linkedInHandle = '';
      
      // Extract LinkedIn handle from personUrn
      if (personUrn) {
        const personId = personUrn.split(':').pop() || '';
        // Get LinkedIn profile details to extract the handle
        try {
          const profileResponse = await fetch(`https://api.linkedin.com/v2/people/${personId}?projection=(vanityName)`, {
            headers: {
              'Authorization': `Bearer ${profile.access_token}`,
              'X-Restli-Protocol-Version': '2.0.0'
            }
          });
          
          if (profileResponse.ok) {
            const profileData = await profileResponse.json();
            linkedInHandle = profileData.vanityName || personId;
            console.log("LinkedIn handle:", linkedInHandle);
          } else {
            // If we can't get the vanity name, we'll use the person ID
            linkedInHandle = personId;
            console.log("Using person ID as handle:", linkedInHandle);
          }
        } catch (error) {
          console.error("Error fetching LinkedIn handle:", error);
          linkedInHandle = personId;
        }
      }
      
      // If there's an image, we need to follow a different process
      if (image) {
        console.log("Image sharing process started");
        
        // Step 1: Register the image upload
        const registerResponse = await fetch('https://api.linkedin.com/v2/assets?action=registerUpload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${profile.access_token}`,
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0'
          },
          body: JSON.stringify({
            registerUploadRequest: {
              recipes: ["urn:li:digitalmediaRecipe:feedshare-image"],
              owner: personUrn,
              serviceRelationships: [
                {
                  relationshipType: "OWNER",
                  identifier: "urn:li:userGeneratedContent"
                }
              ]
            }
          })
        });
        
        if (!registerResponse.ok) {
          const errorData = await registerResponse.json().catch(() => ({}));
          throw new Error(`Failed to register image upload: ${registerResponse.status} ${JSON.stringify(errorData)}`);
        }
        
        const registerData = await registerResponse.json();
        console.log("Register response:", registerData);
        
        const uploadUrl = registerData.value.uploadMechanism["com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest"].uploadUrl;
        const asset = registerData.value.asset;
        
        // Step 2: Fetch the image binary from the provided URL
        console.log("Fetching image from URL:", image);
        const imageResponse = await fetch(image);
        
        if (!imageResponse.ok) {
          throw new Error(`Failed to fetch image from URL: ${imageResponse.status}`);
        }
        
        // Get the image binary data
        const imageBlob = await imageResponse.blob();
        console.log("Image fetched, size:", imageBlob.size, "bytes");
        
        // Step 3: Upload the image binary to LinkedIn
        console.log("Uploading image to LinkedIn");
        const uploadResponse = await fetch(uploadUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${profile.access_token}`
          },
          body: imageBlob
        });
        
        if (!uploadResponse.ok) {
          const uploadErrorText = await uploadResponse.text().catch(() => '');
          throw new Error(`Failed to upload image to LinkedIn: ${uploadResponse.status} ${uploadErrorText}`);
        }
        
        console.log("Image uploaded successfully");
        
        // Step 4: Create the share with the uploaded image
        const payload = {
          author: personUrn,
          lifecycleState: "PUBLISHED",
          specificContent: {
            "com.linkedin.ugc.ShareContent": {
              shareCommentary: {
                text: content
              },
              shareMediaCategory: "IMAGE",
              media: [
                {
                  status: "READY",
                  description: {
                    text: "Shared image"
                  },
                  media: asset,
                  title: {
                    text: "Image"
                  }
                }
              ]
            }
          },
          visibility: {
            "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
          }
        };
        
        console.log("Image share payload:", JSON.stringify(payload));
        
        const shareResponse = await fetch('https://api.linkedin.com/v2/ugcPosts', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${profile.access_token}`,
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0'
          },
          body: JSON.stringify(payload)
        });
        
        if (!shareResponse.ok) {
          const errorData = await shareResponse.json().catch(() => ({}));
          throw new Error(`Failed to create image share: ${shareResponse.status} ${JSON.stringify(errorData)}`);
        }
        
        // The share URN is returned in the X-RestLi-Id header
        console.log("Share response headers:", Object.fromEntries([...shareResponse.headers.entries()]));
        activityId = shareResponse.headers.get('x-restli-id');
        console.log("Activity ID from header:", activityId);
      } else {
        // Text-only share
        const payload = {
          author: personUrn,
          lifecycleState: "PUBLISHED",
          specificContent: {
            "com.linkedin.ugc.ShareContent": {
              shareCommentary: {
                text: content
              },
              shareMediaCategory: "NONE"
            }
          },
          visibility: {
            "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
          }
        };
        
        console.log("Text share payload:", JSON.stringify(payload));
        
        const shareResponse = await fetch('https://api.linkedin.com/v2/ugcPosts', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${profile.access_token}`,
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0'
          },
          body: JSON.stringify(payload)
        });
        
        if (!shareResponse.ok) {
          const errorData = await shareResponse.json().catch(() => ({}));
          throw new Error(`Failed to create text share: ${shareResponse.status} ${JSON.stringify(errorData)}`);
        }
        
        // Log all headers to see what's available
        console.log("Share response headers:", Object.fromEntries([...shareResponse.headers.entries()]));
        activityId = shareResponse.headers.get('x-restli-id');
        console.log("Activity ID from header:", activityId);
      }
      
      // Generate the post URL - this is the one working fix, the one in LinkedIn documentation is wrong 
      // and format is different
      let postUrl;
      
      if (activityId) {
        // Extract the UGC post ID if it's a full URN
        const postId = activityId.includes(':') ? activityId.split(':').pop() : activityId;
        
        // Create the post URL with the LinkedIn format
        if (linkedInHandle) {
          // For specified tags, create a hash tag string
          // const hashtags = ['#contentmarketing', '#b2b'];
          // const hashtagString = hashtags.join('');
          
          // Format: linkedin.com/posts/{profile-id}/{activity-term}-{activity-id}
          postUrl = `https://www.linkedin.com/posts/${linkedInHandle}/activity-${postId}`;
        } else {
          // Fallback to feed URL if we couldn't get the handle
          postUrl = `https://www.linkedin.com/feed/update/${postId}`;
        }
        
        return NextResponse.json({
          success: true,
          activityId: activityId,
          postUrl: postUrl,
          message: 'Successfully posted to LinkedIn'
        });
      } else {
        // If we don't have an activity ID for some reason, return a generic success
        return NextResponse.json({
          success: true,
          message: 'Successfully posted to LinkedIn, but could not determine post URL'
        });
      }
    } catch (postError: any) {
      console.error("Error posting to LinkedIn:", postError);
      
      return NextResponse.json({
        success: false,
        error: postError.message || 'Failed to post to LinkedIn',
        details: postError.response || null
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in LinkedIn share API:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to share to LinkedIn'
    }, { status: 500 });
  }
}
