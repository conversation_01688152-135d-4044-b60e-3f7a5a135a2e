import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

/**
 * API route to store LinkedIn OAuth data in the database
 * This route handles storing the LinkedIn OAuth data in the linkedInState table
 */
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    const { 
      state, 
      access_token, 
      expires_in, 
      refresh_token, 
      refresh_token_expires_in, 
      scope 
    } = body;

    // Validate required parameters
    if (!state || !access_token) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Get the Supabase client
    const supabase = getSupabaseServerClient();

    // Verify authentication
    const { data: authData, error: authError } = await supabase.auth.getUser();
    if (authError || !authData.user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    try {
      const { error: queryError } = await supabase.from('linkedInState').insert({
        state, 
        access_token, 
        expires_in: expires_in?.toString(), 
        refresh_token, 
        refresh_token_expires_in: refresh_token_expires_in?.toString(),
        scope
      });
      
      if (queryError) {
        throw queryError;
      }
      
      return NextResponse.json({ success: true });
    } catch (sqlError) {
      console.error('Error storing LinkedIn data:', sqlError);
      return NextResponse.json(
        { error: 'Database error', details: (sqlError as Error).message },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 