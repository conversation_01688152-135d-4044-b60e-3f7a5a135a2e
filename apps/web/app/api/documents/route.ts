import { NextRequest, NextResponse } from 'next/server';

import { getLogger } from '@kit/shared/logger';

import { getDocupanda<PERSON><PERSON> } from '../../utils/api.util';

export const config = {
  api: {
    bodyParser: false, // Needed for formData handling
  },
};

export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const ctx = { name: 'document-upload' };

  try {
    logger.info(ctx, 'Starting document upload process');
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      logger.error(ctx, 'No file provided');
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Convert file to base64
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64File = buffer.toString('base64');

    const docupandaApi = getDocupanda<PERSON>pi();

    // Upload to DocuPanda
    logger.info(ctx, 'Uploading file to DocuPanda');
    const uploadResponse = await docupandaApi.post('/document', {
      document: {
        file: {
          contents: base64File,
          filename: file.name,
        },
      },
    });

    const { documentId } = uploadResponse.data;
    logger.info(ctx, `File uploaded, documentId: ${documentId}`);

    // Wait for processing to complete
    let documentStatus = 'processing';
    let documentResult: any = null;
    while (documentStatus === 'processing') {
      logger.info(ctx, 'Checking document processing status');
      const statusResponse = await docupandaApi.get(`/document/${documentId}`);
      const statusData = statusResponse.data;
      documentStatus = statusData.status;
      if (statusData.status === 'completed') {
        documentResult = statusData.result;
      }
      await new Promise((resolve) => setTimeout(resolve, 3000)); // Wait 3 seconds between checks
    }

    logger.info(ctx, 'Document processing completed');
    return NextResponse.json(JSON.stringify(documentResult?.text || ''));
  } catch (error) {
    logger.error(ctx, 'Error processing document:', JSON.stringify(error));
    return NextResponse.json(
      { error: 'Error processing document' },
      { status: 500 },
    );
  }
}
