import { handleUpload, type <PERSON>leUploadBody } from '@vercel/blob/client';
import { NextResponse } from 'next/server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(request: Request): Promise<NextResponse> {
  const logger = await getLogger();
  const ctx = { name: 'document-blob-upload' };

  try {
    logger.info(ctx, 'Starting document blob upload process');
    
    // Check authentication
    const supabase = getSupabaseServerClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      logger.error(ctx, 'Unauthorized upload attempt');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const body = (await request.json()) as HandleUploadBody;

    const jsonResponse = await handleUpload({
      body,
      request,
      onBeforeGenerateToken: async () => {
        // Generate a client token for the browser to upload the file
        // Only authenticated users can upload
        logger.info(ctx, `Generating upload token for user: ${user.id}`);

        return {
          allowedContentTypes: ['application/pdf'],
          addRandomSuffix: true,
          tokenPayload: JSON.stringify({
            uploadType: 'document',
            timestamp: Date.now(),
            userId: user.id,
          }),
        };
      },
      onUploadCompleted: async ({ blob, tokenPayload }) => {
        // Get notified of client upload completion
        logger.info(ctx, `Document blob upload completed: ${blob.url}`);

        try {
          // Log the successful upload
          if (tokenPayload) {
            const payload = JSON.parse(tokenPayload);
            logger.info(ctx, `Upload completed for user ${payload.userId}: ${JSON.stringify(payload)}`);
          }
        } catch (error) {
          logger.error(ctx, 'Error parsing token payload:', error);
        }
      },
    });

    return NextResponse.json(jsonResponse);
  } catch (error) {
    logger.error(ctx, 'Error in document blob upload:', error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 },
    );
  }
} 