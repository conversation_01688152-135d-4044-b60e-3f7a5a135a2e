import { NextRequest, NextResponse } from 'next/server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getDocupandaApi } from '../../../utils/api.util';

export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const ctx = { name: 'document-process' };

  try {
    logger.info(ctx, 'Starting document processing from blob URL');
    
    // Check authentication
    const supabase = getSupabaseServerClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      logger.error(ctx, 'Unauthorized document processing attempt');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const { blobUrl } = await request.json();
    
    if (!blobUrl) {
      logger.error(ctx, 'No blob URL provided');
      return NextResponse.json({ error: 'No blob URL provided' }, { status: 400 });
    }

    // Download the file from the blob URL
    logger.info(ctx, `Downloading file from blob URL for user ${user.id}: ${blobUrl}`);
    const fileResponse = await fetch(blobUrl);
    
    if (!fileResponse.ok) {
      logger.error(ctx, 'Failed to download file from blob URL');
      return NextResponse.json({ error: 'Failed to download file' }, { status: 400 });
    }

    // Convert to base64 for DocuPanda
    const arrayBuffer = await fileResponse.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const base64File = buffer.toString('base64');

    // Extract filename from blob URL or use default
    const filename = blobUrl.split('/').pop() || 'document.pdf';

    const docupandaApi = getDocupandaApi();

    // Upload to DocuPanda
    logger.info(ctx, 'Uploading file to DocuPanda');
    const uploadResponse = await docupandaApi.post('/document', {
      document: {
        file: {
          contents: base64File,
          filename: filename,
        },
      },
    });

    const { documentId } = uploadResponse.data;
    logger.info(ctx, `File uploaded, documentId: ${documentId}`);

    // Wait for processing to complete
    let documentStatus = 'processing';
    let documentResult: any = null;
    while (documentStatus === 'processing') {
      logger.info(ctx, 'Checking document processing status');
      const statusResponse = await docupandaApi.get(`/document/${documentId}`);
      const statusData = statusResponse.data;
      documentStatus = statusData.status;
      if (statusData.status === 'completed') {
        documentResult = statusData.result;
      }
      await new Promise((resolve) => setTimeout(resolve, 3000)); // Wait 3 seconds between checks
    }

    logger.info(ctx, 'Document processing completed');
    return NextResponse.json(JSON.stringify(documentResult?.text || ''));
  } catch (error) {
    logger.error(ctx, 'Error processing document:', JSON.stringify(error));
    return NextResponse.json(
      { error: 'Error processing document' },
      { status: 500 },
    );
  }
} 