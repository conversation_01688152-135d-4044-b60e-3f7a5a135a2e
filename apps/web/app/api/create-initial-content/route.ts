import { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import axios from 'axios';
import { InitialCampaignResponse } from '~/types/Campaign';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const client = getSupabaseServerClient();
    const { data: authData, error: authError } = await client.auth.getUser();
    const user = authData.user;
    if (!user || authError) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }
    
    // Get request body
    const requestBody = await req.json();
    const { 
      company_id, 
      user_id, 
      campaign_name, 
      slug, 
      objective, 
      websiteUrl, 
      company_name 
    } = requestBody;

    // Validate required fields
    if (!company_id || !user_id || !campaign_name || !slug || !objective || !websiteUrl || !company_name) {
      return NextResponse.json({ 
        error: 'Missing required fields',
        details: 'All fields are required: company_id, user_id, campaign_name, slug, objective, websiteUrl, company_name'
      }, { status: 400 });
    }

    // Forward the request to the backend service
    try {
      const response = await axios.post<InitialCampaignResponse>(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/create-initial-content`,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      // Return the response from the backend
      return NextResponse.json<InitialCampaignResponse>(response.data);
    } catch (apiError: any) {
      return NextResponse.json(
        { 
          error: 'Failed to create initial content', 
          details: apiError?.response?.data || 'Unknown error' 
        }, 
        { status: apiError?.response?.status || 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}