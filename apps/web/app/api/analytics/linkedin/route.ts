import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';
import { fetchLinkedInAnalytics } from '../../../home/<USER>/analytics/_lib/server/analytics.service';

export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();
  
  try {
    // Get the profile key from the query parameters
    const { searchParams } = new URL(request.url);
    const profileKey = searchParams.get('profileKey');
    
    if (!profileKey) {
      logger.error('Profile key not provided in analytics API request');
      return NextResponse.json(
        { error: 'Profile key is required' },
        { status: 400 }
      );
    }

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      logger.error('Unauthorized access to analytics API', { error: authError });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch the analytics data
    const analyticsData = await fetchLinkedInAnalytics(profileKey);
    
    if (!analyticsData) {
      logger.error('Failed to fetch LinkedIn analytics data', { profileKey });
      return NextResponse.json(
        { error: 'Failed to fetch analytics data' },
        { status: 500 }
      );
    }

    logger.info('Successfully fetched LinkedIn analytics via API', {
      profileKey,
      postsCount: analyticsData.postsCount,
      userId: user.id
    });

    return NextResponse.json(analyticsData);
    
  } catch (error) {
    logger.error('Error in LinkedIn analytics API', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 