import { NextRequest } from 'next/server';

export const POST = async (request: NextRequest) => {
  try {
    const body = await request.json();

    // If imageUrl is provided, initiate animation

    if (body.imageUrl) {
      const apiResponse = await fetch('https://api.smartberry.ai/generate_video_runway', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_url: body.imageUrl,
          video_duration: 5,
          video_prompt: body.prompt || 'Animate this image',
        }),
      });

      if (!apiResponse.ok) {
        throw new Error(`API request failed with status ${apiResponse.status}`);
      }

      const responseData = await apiResponse.json();

      return new Response(JSON.stringify({
        taskId: responseData.task_id,
      }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // If taskId is provided, check status
    if (body.taskId) {
      const apiResponse = await fetch(`https://api.smartberry.ai/get_status_and_path_video_runway`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          task_id: body.taskId,
        }),
      });

      if (!apiResponse.ok) {
        throw new Error(`API request failed with status ${apiResponse.status}`);
      }

      const responseData = await apiResponse.json();

      if (responseData.status === 'RUNNING' || responseData.status === 'PENDING' || responseData.status === 'THROTTLED') {
        return new Response(JSON.stringify({ status: 'RUNNING' }), {
          headers: { 'Content-Type': 'application/json' },
        });
      }

      if (responseData.status === 'SUCCEEDED') {
        const videoPath = responseData.video_path;
        const videoApiResponse = await fetch(`https://api.smartberry.ai/sb_download_video`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            video_path: videoPath,
          }),
        });

        if (!videoApiResponse.ok) {
          throw new Error(`Video download request failed with status ${videoApiResponse.status}`);
        }

        const videoData = await videoApiResponse.arrayBuffer();

        // Return the video data directly
        return new Response(videoData, {
          headers: {
            'Content-Type': 'video/mp4',
            'Content-Disposition': `attachment; filename="animated-${body.taskId || 'image'}.mp4"`,
           },
        });
      }

      if (responseData.status === 'FAILED') {
        return new Response(JSON.stringify({ status: 'FAILED' }), {
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    throw new Error('Invalid request - must provide either imageUrl or taskId');

  } catch (error) {
    return new Response(JSON.stringify({ error: 'Animation operation failed' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
