import { NextRequest, NextResponse } from 'next/server';
// import axios from 'axios'; // Remove axios import


export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    // const supabase = await getSupabaseServerClient();
    // console.log('body', body);
    // Generate image using fetch
    const generateRes = await fetch(`https://api.smartberry.ai/sb_generate_download_image_replicate_v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_prompt: body.image_prompt,
        aspect_ratio: body.aspect_ratio,
        prompt_upsampling: "false",
        language: "English"
      }),
    });

    if (!generateRes.ok) {
      throw new Error(`Image generation API request failed with status ${generateRes.status}`);
    }

    const imageArrayBuffer = await generateRes.arrayBuffer();
    const imageBuffer = Buffer.from(imageArrayBuffer);

    // Upload to ImgBB
    const imgbbApiKey = '********************************'; // Consider moving to environment variables
    const formData = new FormData();
    formData.append('image', imageBuffer.toString('base64'));

    const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
      method: 'POST',
      body: formData,
    });

    if (!imgbbRes.ok) {
      const errorText = await imgbbRes.text();
      throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
    }

    const imgbbData = await imgbbRes.json();

    if (!imgbbData.success) {
        throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
    }

    // Return the ImgBB URL
    return NextResponse.json({ url: imgbbData.data.url });

    /* Previous code returning raw image buffer
    return new Response(imageBuffer, {
      headers: {
        'Content-Type': contentType,
      },
    });
    */

  } catch (error) {
    console.error('Image generation error:', error);
    return NextResponse.json({ error: 'Failed to generate image' }, { status: 500 });
  }
}