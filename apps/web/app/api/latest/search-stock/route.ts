import { type NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const source = searchParams.get('source') ?? 'pexels'; // Default to pexels if not specified

  if (!query) {
    return NextResponse.json(
      { error: 'Search query is required' },
      { status: 400 },
    );
  }

  if (source === 'pexels') {
    // Use a server-side environment variable for the API key
    const apiKey = process.env.NEXT_PUBLIC_PEXELS_API_KEY;

    if (!apiKey) {
      console.error('Pexels API key is not configured.');
      return NextResponse.json(
        { error: 'API key not configured for Pexels' },
        { status: 500 },
      );
    }

    const pexelsUrl = `https://api.pexels.com/videos/search?query=${encodeURIComponent(
      query,
    )}&per_page=30&size=medium&orientation=landscape`;

    try {
      const response = await fetch(pexelsUrl, {
        headers: {
          Authorization: apiKey,
        },
      });

      if (!response.ok) {
        console.error(`Pexels API error! status: ${response.status}`);
        const errorBody = await response.text();
        console.error(`Pexels error body: ${errorBody}`);
        return NextResponse.json(
          { error: `Pexels API error: ${response.statusText}` },
          { status: response.status },
        );
      }

      const data = await response.json();
      // We only need the videos array, potentially transforming it if needed
      return NextResponse.json(data.videos ?? []);
    } catch (error) {
      console.error('Error fetching from Pexels API:', error);
      return NextResponse.json(
        { error: 'Internal server error fetching from Pexels' },
        { status: 500 },
      );
    }
  } else {
    // Placeholder for other potential sources (e.g., Pixabay)
    return NextResponse.json({ error: 'Unsupported source' }, { status: 400 });
  }
} 