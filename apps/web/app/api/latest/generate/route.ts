import { NextRequest } from "next/server";
import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || "********************************************************************************************************************************************************************"
});

export async function POST(request: NextRequest) {
  try {
    const { text, promoStyle, toneSelections, videoLength, musicStyle, fontStyle } = await request.json();
    
    const scenesCount = videoLength === "Short (15–30 sec)" ? "3-4" : 
                      videoLength === "Medium (30–60 sec)" ? "5-7" : 
                      videoLength === "Full promo (60–90 sec)" ? "8-12" : "5-7";
    
    const toneString = toneSelections?.length > 0 ? toneSelections.join(" + ") : "Professional";
    
    const prompt = `
      You are a creative director tasked with turning a blog post into a ${promoStyle || "professional"} promotional or educational video. 
      Based on the blog and the selected tone (${toneString}) and style, break the video into ${scenesCount} short scenes.
      
      Each scene should include:
      1. "text": A sensical caption or overlay text (10 to 20 words) that will be used as caption/narration for that scene
      2. "visualType": Either "STOCK" or "AI" to indicate the type of visual needed. 
      3. "visualDescription": 
         - If visualType is "STOCK": A simple search term for finding stock footage (e.g. "business meeting", "sunset beach")
         - If visualType is "AI": A detailed prompt for AI image generation, as well as how it should be animated to become a video (delimited by a dash -)
      
      Make it feel like a high-budget tech trailer — keep scenes short, dramatic, and connected to the blog's core message.
      
      Create a short scene with a strong message and a vivid visual description that fits the selected theme.
      
      Visuals should:
      - Match and enhance the narrative of each scene
      - For stock footage: Use simple, clear search terms that will yield good results
      - For AI generation: Provide detailed, specific prompts
      - Create a cohesive visual story
      - Mix both stock footage and AI-generated images as appropriate
      
      Return ONLY an array of objects with "text", "visualType", and "visualDescription" properties.
      Do not include any other text or explanations. Do not include markdown, it MUST be a pure, parseable JSON object.
      
      Blog Post: ${text}
      Video Style: ${promoStyle || "Professional"}
      Tone: ${toneString}
      Desired Video Length: ${videoLength || "Medium (30-60 seconds)"}
      Font Style: ${fontStyle || "Clean & minimal"}
      Music Style: ${musicStyle || "None"}
    `;

    const completion = await openai.chat.completions.create({
      model: "o3-mini-2025-01-31",
      messages: [{ role: "user", content: prompt }],
    });

    const content = completion.choices[0].message.content;
    const scenes = JSON.parse(content || '[]');
    return Response.json({ scenes });
  } catch (error: any) {
    return Response.json({ error: error.message }, { status: 500 });
  }
}