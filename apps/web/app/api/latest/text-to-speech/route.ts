import { NextRequest } from "next/server";
import { ElevenLabsClient } from "elevenlabs";

const ELEVEN_LABS_API_KEY = "***************************************************";

// Voice IDs for different languages
const VOICE_IDS = {
  English: "RiK8PTtVIeKKoFFTk9fg", // Josh voice
};

export async function POST(request: NextRequest) {
  try {
    const { text, language } = await request.json();

    const client = new ElevenLabsClient({
      apiKey: ELEVEN_LABS_API_KEY,
    });

    // Select the appropriate voice based on language
    const voiceId = VOICE_IDS[language as keyof typeof VOICE_IDS];
    if (!voiceId) {
      throw new Error(`Unsupported language: ${language}`);
    }

    // Arrays to store audio chunks and alignment data
    const audioChunks: Uint8Array[] = [];
    // let alignmentData = null;

    // Get the streaming response with timestamps
    const streamResponse = await client.textToSpeech.streamWithTimestamps(voiceId, {
      text,
      model_id: "eleven_multilingual_v2",
      output_format: "mp3_44100_128",
    });

    // Process each chunk from the stream
    let chunkCount = 0;
    const alignmentData = {
      characters: [],
      character_start_times_seconds: [],
      character_end_times_seconds: []
    };
    for await (const chunk of streamResponse) {
      chunkCount++;
      // Get alignment data from the first chunk
      if (chunk.normalized_alignment) {
        alignmentData.characters.push(...(chunk.normalized_alignment.characters || []));
        alignmentData.character_start_times_seconds.push(...(chunk.normalized_alignment.character_start_times_seconds || []));
        alignmentData.character_end_times_seconds.push(...(chunk.normalized_alignment.character_end_times_seconds || []));
        // alignmentData = chunk.normalized_alignment || chunk.alignment;
      }

      if (chunk.audio_base64) {
        audioChunks.push(Buffer.from(chunk.audio_base64, 'base64'));
      }
    }

    if (!alignmentData) {
      throw new Error("No alignment data received");
    }

    // Combine all audio chunks
    const audioData = Buffer.concat(audioChunks);
    const audioBase64 = audioData.toString('base64');

    // Process the timestamp data to get word-level timestamps
    const words = text.split(/\s+/);
    const { characters, character_start_times_seconds, character_end_times_seconds } = alignmentData;

    if (!characters || !character_start_times_seconds || !character_end_times_seconds) {
      throw new Error("Incomplete alignment data received");
    }

    // Create word-level timestamps by tracking word boundaries
    const timestamps = [];
    let currentWord = '';
    // let wordStart = 0;
    const wordStartIndex = 0;

    for (let i = 0; i < characters.length; i++) {
      const char = characters[i];
      
      // If we're at a word boundary (space or punctuation)
      if (char === ' ' || char === '.' || char === ',' || char === '!' || char === '?' || i === characters.length - 1) {
        // If we have accumulated a word, add it to timestamps
        if (currentWord.trim()) {
          timestamps.push({
            word: currentWord.trim(),
            start: character_start_times_seconds[wordStartIndex],
            end: character_end_times_seconds[i]
          });
        }
        // Reset for next word
        currentWord = '';
        wordStartIndex = i + 1;
      } else {
        currentWord += char;
      }
    }

    // Get the total duration from the last timestamp
    const duration = character_end_times_seconds[character_end_times_seconds.length - 1] || 0;

    return Response.json({
      audioBase64,
      timestamps,
      duration
    });
  } catch (error: any) {
    return Response.json({ error: error.message }, { status: 500 });
  }
}