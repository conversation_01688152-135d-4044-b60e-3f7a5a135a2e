

export type LifecycleStage = "Startup" | "Scale-up" | "Mature Enterprise";

export type NumberRange = {
  min: number | null;
  max: number | null;
};

export type ICP = {
  id: string;
  name: string;
  is_generating: boolean;
  error_generating: boolean;
  withAi: boolean;
  withLinkedIn: boolean;
  linkedInUrls: string[];
  // Metadata
  created_at: string;
  updated_at: string;
  company_id: string;
  reference_material: string[];
  reference_description: string;
  // Data is stored as a JSON object containing all the ICP attributes
  data: {
    // Multi-select attributes
    target_industries?: string[];
    geography_markets?: string[];
    use_cases_problems?: string[];
    buying_triggers?: string[];
    decision_making_departments?: string[];
    red_flags_disqualifiers?: string[];
    technologies_used?: string[];
    content_needs?: string[];
    custom_fields?: any[];
    
    // Range attributes
    company_size_employees?: NumberRange;
    company_revenue_range_usd?: NumberRange;
    typical_contract_value?: NumberRange;
    
    // Enum attribute
    lifecycle_stage?: LifecycleStage;
    
    // Notes
    notes?: string;
  };
  
  // Computed fields (for UI)
  personas_count?: number;
};

export type ICPFormData = {
  name: string;
  
  // Multi-select attributes as string arrays
  target_industries: string[];
  geography_markets: string[];
  use_cases_problems: string[];
  buying_triggers: string[];
  decision_making_departments: string[];
  red_flags_disqualifiers: string[];
  technologies_used: string[];
  content_needs: string[];
  
  // Range attributes
  company_size_employees: NumberRange;
  company_revenue_range_usd: NumberRange;
  typical_contract_value: NumberRange;
  
  // Enum attribute
  lifecycle_stage: LifecycleStage | null;
  
  // Notes
  notes: string;
};

// Default options for multi-select fields
export const TARGET_INDUSTRIES_OPTIONS = [
  "SaaS",
  "Fintech",
  "E-commerce",
  "Healthcare",
  "Education",
  "Manufacturing",
  "Real Estate",
  "Logistics",
  "Marketing",
  "HR Tech",
  "Security",
  "AI/ML",
  "Consulting",
  "Legal",
  "Non-profit"
];

export const GEOGRAPHY_MARKETS_OPTIONS = [
  "North America",
  "United States",
  "Canada",
  "Europe",
  "United Kingdom",
  "Germany",
  "France",
  "Asia Pacific",
  "Australia",
  "Japan",
  "Latin America",
  "Global"
];

export const USE_CASES_PROBLEMS_OPTIONS = [
  "Lead Generation",
  "Customer Acquisition",
  "Sales Automation",
  "Marketing Attribution",
  "Customer Retention",
  "Process Optimization",
  "Cost Reduction",
  "Compliance",
  "Security",
  "Analytics & Reporting",
  "Team Collaboration",
  "Customer Support",
  "Product Development",
  "Data Management"
];

export const BUYING_TRIGGERS_OPTIONS = [
  "Hired first marketer",
  "New product launch",
  "Funding round",
  "Rapid growth",
  "Compliance requirements",
  "Security incident",
  "Tool consolidation",
  "Team expansion",
  "New regulations",
  "Competitive pressure",
  "Technology migration",
  "Leadership change"
];

export const DECISION_MAKING_DEPARTMENTS_OPTIONS = [
  "C-Suite",
  "Marketing",
  "Sales",
  "Product",
  "Engineering",
  "IT",
  "Operations",
  "Finance",
  "HR",
  "Legal",
  "Customer Success",
  "Data & Analytics"
];

export const RED_FLAGS_DISQUALIFIERS_OPTIONS = [
  "Bootstrapped company",
  "No website",
  "Less than 10 employees",
  "No dedicated budget",
  "DIY culture",
  "Recent tool adoption",
  "Price-sensitive",
  "Complex approval process",
  "Limited tech stack",
  "Regulatory restrictions"
];

export const TECHNOLOGIES_USED_OPTIONS = [
  "Salesforce",
  "HubSpot",
  "Marketo",
  "Pardot",
  "Mailchimp",
  "Intercom",
  "Slack",
  "Microsoft Teams",
  "Zoom",
  "AWS",
  "Google Cloud",
  "Azure",
  "Snowflake",
  "Tableau",
  "Power BI",
  "Zapier",
  "Segment",
  "Mixpanel",
  "Google Analytics"
];

export const CONTENT_NEEDS_OPTIONS = [
  "Product education",
  "Competitor comparison",
  "ROI calculator",
  "Implementation guide",
  "Best practices",
  "Case studies",
  "Industry reports",
  "Compliance documentation",
  "Technical specifications",
  "Integration guides",
  "Onboarding materials",
  "Training resources"
]; 