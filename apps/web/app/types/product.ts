import type { ProductDocument } from './product-document';

export interface KeyFeature {
  name: string;
  value_prop: string;
  differentiation: string;
}

export interface Product {
  id: string;
  created_at?: number;
  updated_at?: number;
  name: string;
  description?: string;
  target_audience?: string[]; // Array of ICP IDs
  key_features?: KeyFeature[];
  company_id: string;
  custom_fields?: Record<string, any>;
}

export interface CreateProductData {
  name: string;
  description?: string;
  target_audience?: string[]; // Array of ICP IDs
  key_features?: KeyFeature[];
  company_id: string;
  custom_fields?: Record<string, any>;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string;
}

export interface ProductWithDocuments extends Product {
  documents?: ProductDocument[];
}

// Re-export ProductDocument types for convenience
export type { ProductDocument, ProductDocumentWithLink } from './product-document';