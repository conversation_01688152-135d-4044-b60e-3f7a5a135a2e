'use client';

import { useMemo } from 'react';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useQuery } from '@tanstack/react-query';
import { useZero } from './use-zero';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

export interface TrialStatus {
  hasActiveSubscription: boolean;
  isTrialing: boolean;
  trialExpired: boolean;
  trialExpiringSoon: boolean;
  canAccessApp: boolean;
  daysUntilExpiration: number | null;
  isLoading: boolean;
}

export interface TrialBadgeStatus {
  show: boolean;
  variant: 'success' | 'warning' | 'destructive';
  message: string;
  isLoading: boolean;
}

export function useTrialStatus(): TrialStatus {
  // SUPABASE VERSION - Simplified for debugging
  const workspace = useTeamAccountWorkspace();
  const supabase = useSupabase();

  const { data: subscriptions, isLoading } = useQuery({
    queryKey: ['subscriptions', workspace.account.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('account_id', workspace.account.id);

      if (error) {
        throw new Error(`Failed to fetch subscriptions: ${error.message}`);
      }

      return data;
    },
    enabled: !!workspace.account.id,
  });

  return useMemo(() => {


    if (isLoading) {
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false,
        trialExpiringSoon: false,
        canAccessApp: false, // Block access during loading to prevent content flash
        daysUntilExpiration: null,
        isLoading: true,
      };
    }

    const subscription = subscriptions?.find((sub) => sub.active) || subscriptions?.[0];
    
    if (!subscription) {
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false,
        trialExpiringSoon: false,
        canAccessApp: false,
        daysUntilExpiration: null,
        isLoading: false,
      };
    }

    const isTrialing = subscription.status === 'trialing';
    const hasActiveSubscription = subscription.status === 'active' || isTrialing;
    
    let trialExpired = false;
    let trialExpiringSoon = false;
    let daysUntilExpiration: number | null = null;

    if (subscription.trial_ends_at) {
      // Zero-sync stores timestamps as numbers (milliseconds since epoch)
      const trialEndsAt = typeof subscription.trial_ends_at === 'number'
        ? subscription.trial_ends_at
        : new Date(subscription.trial_ends_at).getTime();
      const now = Date.now();
      const timeDiff = trialEndsAt - now;
      daysUntilExpiration = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

      trialExpired = timeDiff <= 0;
      trialExpiringSoon = timeDiff > 0 && daysUntilExpiration <= 3;
    }

    return {
      hasActiveSubscription,
      isTrialing,
      trialExpired,
      trialExpiringSoon,
      canAccessApp: hasActiveSubscription && (!isTrialing || !trialExpired),
      daysUntilExpiration,
      isLoading: false,
    };
  }, [subscriptions, isLoading]);
}

export function useTrialBadge(): TrialBadgeStatus {
  // Use the existing useTrialStatus hook but handle loading differently for the badge
  const { isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, isLoading } = useTrialStatus();

  return useMemo(() => {
    // For the trial badge, we can show it even during loading if we have cached data
    // Only hide during loading if we have no data at all
    if (isLoading && !isTrialing && !trialExpired && !hasActiveSubscription) {
      return { show: false, variant: 'success', message: '', isLoading: true };
    }

    // If no subscription exists at all (new user), don't show any badge
    // This prevents showing "Trial expired" for users who just signed up
    if (!hasActiveSubscription && !isTrialing) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // If trial has expired, show expired badge
    if (trialExpired) {
      return { show: true, variant: 'destructive', message: 'Trial expired', isLoading: false };
    }

    // If not trialing (has active paid subscription), don't show badge
    if (!isTrialing && hasActiveSubscription) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // If trialing and expiring soon, show warning badge
    if (isTrialing && trialExpiringSoon && daysUntilExpiration !== null) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'warning',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    // If trialing with more than 3 days left, show success badge
    if (isTrialing && daysUntilExpiration !== null && daysUntilExpiration > 3) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'success',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    return { show: false, variant: 'success', message: '', isLoading: false };
  }, [isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, isLoading]);
}
