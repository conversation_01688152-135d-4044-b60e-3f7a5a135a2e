'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { ICP } from '~/types/icp';

const client = getSupabaseServerClient();

/**
 * Create a new ICP
 * @param icp - The ICP data to create
 * @returns The created ICP
 */
export async function createICP(
  icp: { name: string; company_id: string; data: Record<string, any> }
): Promise<ICP> {
  const {
    data: { user },
  } = await client.auth.getUser();


  if (!user) {
    throw new Error('No authenticated user');
  }

  const { data, error } = await (client as any)
    .from('icps')
    .insert({
      name: icp.name,
      company_id: icp.company_id,
      data: icp.data,
    })
    .select()
    .single();

  if (error) throw error;
  return data as ICP;
}

/**
 * Update an existing ICP
 * @param icp - The ICP data to update
 * @returns The updated ICP
 */
export async function updateICP(
  icp: { id: string; name: string; company_id: string; data: Record<string, any> }
): Promise<ICP> {
  const { data, error } = await (client as any)
    .from('icps')
    .update({
      name: icp.name,
      data: icp.data,
    })
    .match({ id: icp.id })
    .select()
    .single();

  if (error) throw error;
  return data as ICP;
}

/**
 * Get all ICPs for a company with persona counts
 * @param companyId - The company ID to get ICPs for
 * @returns Array of ICPs with persona counts
 */
export async function getICPs(companyId: string): Promise<ICP[]> {
  const { data, error } = await (client as any)
    .from('icps')
    .select(`
      *,
      personas_count:personas(count)
    `)
    .eq('company_id', companyId)
    .order('name', { ascending: true });

  if (error) throw error;
  
  // Transform the response to include personas_count as a number
  return ((data || []) as any[]).map(icp => ({
    ...icp,
    personas_count: Array.isArray(icp.personas_count) ? icp.personas_count.length : 0
  })) as ICP[];
}

/**
 * Get an ICP by ID
 * @param id - The ICP ID
 * @returns The ICP or null if not found
 */
export async function getICPById(id: string): Promise<ICP | null> {
  const { data, error } = await (client as any)
    .from('icps')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') { // Not found
      return null;
    }
    throw error;
  }
  
  return data as ICP;
}

/**
 * Delete an ICP
 * @param id - The ICP ID to delete
 */
export async function deleteICP(id: string): Promise<void> {
  const { error } = await (client as any)
    .from('icps')
    .delete()
    .match({ id });

  if (error) throw error;
}

/**
 * Get personas for a specific ICP
 * @param icpId - The ICP ID
 * @returns Array of personas belonging to the ICP
 */
export async function getPersonasByICP(icpId: string) {
  const { data, error } = await client
    .from('personas')
    .select('*')
    .eq('icp_id' as any, icpId)
    .order('name', { ascending: true });

  if (error) throw error;
  return data || [];
}

/**
 * Get personas without an ICP (for migration purposes)
 * @param companyId - The company ID
 * @returns Array of personas without an ICP
 */
export async function getPersonasWithoutICP(companyId: string) {
  const { data, error } = await client
    .from('personas')
    .select('*')
    .eq('company_id', companyId)
    .is('icp_id' as any, null)
    .order('name', { ascending: true });

  if (error) throw error;
  return data || [];
}

/**
 * Assign personas to an ICP
 * @param personaIds - Array of persona IDs
 * @param icpId - The ICP ID to assign them to
 */
export async function assignPersonasToICP(personaIds: string[], icpId: string): Promise<void> {
  const { error } = await client
    .from('personas')
    .update({ icp_id: icpId } as any)
    .in('id', personaIds);

  if (error) throw error;
} 