'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

const client = getSupabaseServerClient();

/**
 * Get all saved research items for a company
 * @param companyId - The company ID to get research items for
 * @returns Array of research items
 */
export async function getSavedResearch(companyId: string) {
  const { data, error } = await (client as any)
    .from('saved_research')
    .select('*')
    .eq('account_id', companyId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}
