export const CONTENT_TYPE_CHOICES = [
  { label: 'X Post', value: 'Tweet', type: ['Text'], channel: 'Twitter' },
  {
    label: 'Blog Post',
    value: 'Blog Post',
    type: ['Text'],
    channel: 'Blogs',
  },
  {
    label: 'LinkedIn Post',
    value: 'LinkedIn Post',
    type: ['Text', 'Image'],
    channel: 'LinkedIn',
  },
  {
    label: 'Email',
    value: 'Content Share Email',
    type: ['Text', 'Image'],
    channel: 'Email',
  },
];

export const onboarding_campaign_statuses = {
  IDLE: 'Idle',
  ERROR: 'Error',
  SCRAPING_SITE: 'Scraping site',
  CREATING_CAMPAIGN: 'Creating campaign',
  GENERATING_IDEA: 'Generating idea',
  GENERATING_CREATIVE_BRIEF: 'Generating creative brief',
  GENERATING_TASKS: 'Generating tasks',
  TASKS_GENERATED: 'Tasks generated',
};
