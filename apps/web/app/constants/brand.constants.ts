import { BrandFont } from '~/types/brand';

export const systemFonts = [
  'Arial',
  'Verdana',
  'Helvetica',
  'Tahoma',
  'Trebuchet MS',
  'Times New Roman',
  'Georgia',
  '<PERSON><PERSON><PERSON>',
  'Courier New',
  'Brush Script MT',
];

export const defaultFonts: BrandFont[] = [
  {
    title: 'Title',
    size: 36,
    fontFamily: 'Arial',
    isBold: true,
    isItalic: false,
    type: 'title',
  },
  {
    title: 'Subtitle',
    size: 24,
    fontFamily: 'Arial',
    isBold: false,
    isItalic: true,
    type: 'subtitle',
  },
  {
    title: 'Heading',
    size: 20,
    fontFamily: 'Arial',
    isBold: true,
    isItalic: false,
    type: 'heading',
  },
  {
    title: 'Subheading',
    size: 18,
    fontFamily: 'Arial',
    isBold: false,
    isItalic: true,
    type: 'subheading',
  },
  {
    title: 'Section Header',
    size: 16,
    fontFamily: 'Arial',
    isBold: true,
    isItalic: false,
    type: 'section_header',
  },
  {
    title: 'Body',
    size: 14,
    fontFamily: 'Arial',
    isBold: false,
    isItalic: false,
    type: 'body',
  },
  {
    title: 'Quote',
    size: 14,
    fontFamily: 'Arial',
    isBold: false,
    isItalic: true,
    type: 'quote',
  },
  {
    title: 'Caption',
    size: 12,
    fontFamily: 'Arial',
    isBold: false,
    isItalic: false,
    type: 'caption',
  },
];

export const fontSizes = [
  '12',
  '14',
  '16',
  '18',
  '20',
  '24',
  '28',
  '32',
  '36',
  '42',
];
