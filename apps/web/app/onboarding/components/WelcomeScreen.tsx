'use client';

import { useFormContext } from 'react-hook-form';
import { Button } from '@kit/ui/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';

import type { OnboardingFormData } from '../schemas/onboarding';

export function WelcomeScreen({
  handleNext,
  disabled = false
}: {
  handleNext: () => void;
  disabled?: boolean;
}) {
  const { control, watch } = useFormContext<OnboardingFormData>();
  const values = watch();

  return (
    <div className="min-w-2xl space-y-12">
      <div className="space-y-2">
        <h1 className="text-3xl font-medium">Welcome! 🎉</h1>
        <p className="text-muted-foreground text-base">
          It is great to have you here!
          Let&apos;s get your website. We&apos;ll scrape it, so you have some content to
          get started with
        </p>
      
      </div>

      <div className="space-y-6">
        <FormField
          control={control}
          name="companyName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company Name</FormLabel>
              <FormControl>
                <Input placeholder="Acme Inc." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="companyWebsite"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company Website</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="example.com"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            onClick={handleNext}
            disabled={disabled || !values.companyName || !values.companyWebsite}
          >
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
}
