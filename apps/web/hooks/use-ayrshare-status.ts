import { useState, useEffect, useCallback } from 'react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

export interface AyrsharePostStatus {
  id: string;
  status: 'pending' | 'success' | 'error' | 'paused';
  post?: string;
  scheduleDate?: string;
  publishDate?: string;
  created?: string;
  platforms?: string[];
  postUrls?: Record<string, string>;
  postIds?: Array<{
    id: string;
    platform: string;
    postUrl: string;
    status: string;
    owner?: string;
  }>;
  mediaUrls?: string[];
  profileTitle?: string;
  refId?: string;
  shortenLinks?: boolean;
  type?: string;
  errors?: any[];
}

interface UseAyrshareStatusOptions {
  enabled?: boolean;
  refetchInterval?: number; // in milliseconds
}

interface UseAyrshareStatusReturn {
  status: AyrsharePostStatus | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useAyrshareStatus(
  ayrsharePostId: string | undefined,
  profileKey: string | undefined,
  options: UseAyrshareStatusOptions = {}
): UseAyrshareStatusReturn {
  const { enabled = true, refetchInterval = 30000 } = options; // Default 30 seconds
  const workspace = useTeamAccountWorkspace();
  
  const [status, setStatus] = useState<AyrsharePostStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    if (!ayrsharePostId || !profileKey || !enabled) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        profileKey,
        companyId: workspace.account.id
      });

      const response = await fetch(`/api/integrations/ayrshare/status/${ayrsharePostId}?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setStatus(result.data);
      } else {
        console.error('Failed to fetch Ayrshare status:', result);
        setError(result.error || 'Failed to fetch post status');
      }
    } catch (err: any) {
      console.error('Error fetching Ayrshare status:', err);
      setError(err.message || 'Network error');
    } finally {
      setIsLoading(false);
    }
  }, [ayrsharePostId, profileKey, enabled, workspace.account.id]);

  // Initial fetch
  useEffect(() => {
    if (enabled && ayrsharePostId && profileKey) {
      fetchStatus();
    }
  }, [fetchStatus, enabled, ayrsharePostId, profileKey]);

  // Set up interval for refetching
  useEffect(() => {
    if (!enabled || !ayrsharePostId || !profileKey || !refetchInterval) {
      return;
    }

    const interval = setInterval(fetchStatus, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchStatus, enabled, ayrsharePostId, profileKey, refetchInterval]);

  return {
    status,
    isLoading,
    error,
    refetch: fetchStatus
  };
} 