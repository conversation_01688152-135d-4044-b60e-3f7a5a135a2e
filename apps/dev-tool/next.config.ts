import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  reactStrictMode: false,
  transpilePackages: ['@kit/ui', '@kit/shared'],
  experimental: {
    reactCompiler: true,
  },
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  webpack(config) {
    config.resolve.alias['prosemirror-model'] = require.resolve('prosemirror-model')
    return config
  },
};

export default nextConfig;
