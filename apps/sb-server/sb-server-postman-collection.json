{"info": {"name": "SB Server API Collection", "description": "Complete API collection for SB Server with test data for all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{api_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "api_token", "value": "your-api-token-here", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Basic health check endpoint to verify server is running"}, "response": []}, {"name": "Environment Test", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/env-test", "host": ["{{base_url}}"], "path": ["env-test"]}, "description": "Test environment variables and configuration"}, "response": []}, {"name": "<PERSON><PERSON> Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mutatorName\": \"createCompany\",\n  \"args\": {\n    \"name\": \"Acme Corp\",\n    \"domain\": \"acme.com\",\n    \"industry\": \"Technology\",\n    \"size\": \"500-1000\",\n    \"location\": \"San Francisco, CA\"\n  }\n}"}, "url": {"raw": "{{base_url}}/push", "host": ["{{base_url}}"], "path": ["push"]}, "description": "Push data mutations to the database"}, "response": []}, {"name": "Website Crawling", "item": [{"name": "Crawl Website", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/crawl?url=https://example.com&limit=50", "host": ["{{base_url}}"], "path": ["crawl"], "query": [{"key": "url", "value": "https://example.com"}, {"key": "limit", "value": "50"}]}, "description": "Crawl a website and extract content using Firecrawl API"}, "response": []}, {"name": "Get Crawl Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/crawl-status?crawlId=crawl_123456789", "host": ["{{base_url}}"], "path": ["crawl-status"], "query": [{"key": "crawlId", "value": "crawl_123456789"}]}, "description": "Check the status of a crawling job"}, "response": []}]}, {"name": "Website Extraction & Scraping", "item": [{"name": "Extract from Website", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"urls\": [\"https://example.com\", \"https://another-site.com\"],\n  \"prompt\": \"Extract company information including name, industry, and contact details\",\n  \"schema\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"company_name\": {\"type\": \"string\"},\n      \"industry\": {\"type\": \"string\"},\n      \"email\": {\"type\": \"string\"},\n      \"phone\": {\"type\": \"string\"},\n      \"address\": {\"type\": \"string\"}\n    }\n  },\n  \"enableWebSearch\": true\n}"}, "url": {"raw": "{{base_url}}/extract-from-website", "host": ["{{base_url}}"], "path": ["extract-from-website"]}, "description": "Extract structured data from websites using AI with custom schema"}, "response": []}, {"name": "Get Extract Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/extract-status?extractId=extract_123456789", "host": ["{{base_url}}"], "path": ["extract-status"], "query": [{"key": "extractId", "value": "extract_123456789"}]}, "description": "Check the status of an extraction job"}, "response": []}, {"name": "Scrape Website", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/scrape?url=https://example.com", "host": ["{{base_url}}"], "path": ["scrape"], "query": [{"key": "url", "value": "https://example.com"}]}, "description": "Basic website scraping using Cheerio for title, description, and content"}, "response": []}, {"name": "Scrape by Element", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"url\": \"https://example.com\",\n  \"element_prompts\": [\n    \"Find the main heading of the page\",\n    \"Extract all pricing information\",\n    \"Get contact information including email and phone\",\n    \"Find product features or service offerings\"\n  ],\n  \"json_output\": {\n    \"title\": \"string\",\n    \"pricing\": \"array\",\n    \"contact\": \"object\",\n    \"features\": \"array\"\n  },\n  \"instructions\": \"Structure the data in a clean, organized format suitable for business analysis\"\n}"}, "url": {"raw": "{{base_url}}/scrape-by-element", "host": ["{{base_url}}"], "path": ["scrape-by-element"]}, "description": "Advanced AI-powered scraping targeting specific elements using JigsawStack"}, "response": []}]}, {"name": "AI Content Generation", "item": [{"name": "Generate Content Schedule", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productDocumentation\": \"Our SaaS platform helps businesses automate their marketing workflows with AI-powered content generation, social media scheduling, and performance analytics. Key features include: automated content creation, multi-platform publishing, audience targeting, and ROI tracking.\",\n  \"campaignGoal\": \"Increase brand awareness and generate 500 new leads for our marketing automation platform targeting small to medium businesses\",\n  \"startDate\": \"2024-09-01\",\n  \"endDate\": \"2024-11-30\",\n  \"externalResearch\": \"Recent studies show that 73% of SMBs struggle with consistent content creation. Our target audience spends most time on LinkedIn, Twitter, and industry blogs. Peak engagement times are Tuesday-Thursday, 9-11 AM and 2-4 PM.\"\n}"}, "url": {"raw": "{{base_url}}/generate-schedule", "host": ["{{base_url}}"], "path": ["generate-schedule"]}, "description": "Generate a comprehensive content marketing schedule with AI"}, "response": []}, {"name": "Generate Persona", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"icp_data\": {\n    \"company_size\": \"50-200 employees\",\n    \"industry\": \"SaaS, Technology, Marketing\",\n    \"roles\": [\"Marketing Manager\", \"CMO\", \"Growth Lead\"],\n    \"challenges\": [\n      \"Lack of time for content creation\",\n      \"Difficulty measuring ROI\",\n      \"Managing multiple marketing channels\",\n      \"Scaling content production\"\n    ],\n    \"goals\": [\n      \"Increase lead generation\",\n      \"Improve brand awareness\",\n      \"Automate repetitive tasks\",\n      \"Better track marketing performance\"\n    ],\n    \"budget_range\": \"$5,000-$25,000/month\",\n    \"tech_stack\": [\"HubSpot\", \"Salesforce\", \"Google Analytics\", \"Slack\"],\n    \"location\": \"North America, Europe\"\n  }\n}"}, "url": {"raw": "{{base_url}}/generate-persona", "host": ["{{base_url}}"], "path": ["generate-persona"]}, "description": "Generate detailed buyer personas based on ICP data"}, "response": []}, {"name": "Generate Visual Description", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"brand_brief\": \"Modern, professional SaaS brand focused on marketing automation. Colors: <PERSON> (#2563EB), <PERSON>, <PERSON> Gray. Style: Clean, minimalist, tech-forward. Target audience: Marketing professionals aged 28-45. Tone: Confident, helpful, innovative.\",\n  \"content\": \"New blog post about '5 Ways AI is Revolutionizing Content Marketing'. The post discusses machine learning algorithms, automated personalization, predictive analytics, content optimization, and performance tracking. Target audience: marketing managers looking to leverage AI in their strategies.\"\n}"}, "url": {"raw": "{{base_url}}/generate-visual-description", "host": ["{{base_url}}"], "path": ["generate-visual-description"]}, "description": "Generate visual descriptions for content based on brand guidelines"}, "response": []}]}, {"name": "Social Media Scraping", "item": [{"name": "Scrape Reddit", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keywords\": \"marketing automation SaaS startup\",\n  \"timeFilter\": \"month\",\n  \"includeComments\": true,\n  \"includeEngagement\": true,\n  \"maxItems\": 100,\n  \"maxPostCount\": 50,\n  \"maxComments\": 10,\n  \"sort\": \"top\"\n}"}, "url": {"raw": "{{base_url}}/scrape/reddit", "host": ["{{base_url}}"], "path": ["scrape", "reddit"]}, "description": "Scrape Reddit posts and comments for market research and trend analysis"}, "response": []}, {"name": "Scrape TikTok", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hashtags\": [\"#marketingautomation\", \"#saasmarketing\", \"#digitalmarketing\"],\n  \"searchQueries\": [\"marketing automation tools\", \"saas marketing tips\"],\n  \"resultsPerPage\": 50,\n  \"maxProfilesPerQuery\": 10,\n  \"profileScrapeSections\": [\"videos\"],\n  \"profileSorting\": \"latest\",\n  \"oldestPostDateUnified\": \"30\",\n  \"newestPostDate\": \"2024-08-01\"\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}, "description": "Scrape TikTok content for trending hashtags and viral marketing content"}, "response": []}, {"name": "Scrape Twitter/X", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"searchTerms\": [\"marketing automation\", \"saas tools\", \"content marketing AI\"],\n  \"twitterHandles\": [\"@hubspot\", \"@mailchimp\", \"@salesforce\"],\n  \"maxItems\": 200,\n  \"tweetLanguage\": \"en\",\n  \"onlyVerifiedUsers\": false,\n  \"onlyTwitterBlue\": false,\n  \"minimumRetweets\": 5,\n  \"minimumFavorites\": 10,\n  \"start\": \"2024-07-01\",\n  \"end\": \"2024-08-01\",\n  \"sort\": \"Top\"\n}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}, "description": "Scrape Twitter/X for trending topics, competitor analysis, and engagement insights"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic timestamp", "pm.environment.set('timestamp', new Date().toISOString());", "", "// Log request details for debugging", "console.log('Making request to:', pm.request.url.toString());", "console.log('Request method:', pm.request.method);", "", "// Validate required environment variables", "if (!pm.environment.get('base_url')) {", "  console.warn('Warning: base_url environment variable not set');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Response status code is successful', function () {", "  pm.expect(pm.response.code).to.be.oneOf([200, 201, 202]);", "});", "", "pm.test('Response time is acceptable', function () {", "  pm.expect(pm.response.responseTime).to.be.below(30000);", "});", "", "pm.test('Response has valid JSON structure', function () {", "  pm.response.to.have.jsonBody();", "});", "", "// Log response for debugging", "console.log('Response status:', pm.response.code);", "console.log('Response time:', pm.response.responseTime + 'ms');", "", "// Store commonly used values for subsequent requests", "if (pm.response.json().crawlId) {", "  pm.environment.set('last_crawl_id', pm.response.json().crawlId);", "}", "", "if (pm.response.json().extractId) {", "  pm.environment.set('last_extract_id', pm.response.json().extractId);", "}"]}}]}