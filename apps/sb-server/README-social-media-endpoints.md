# Social Media Scraping API Endpoints

This document describes the REST API endpoints for scraping social media platforms (Reddit, TikTok, and Twitter/X).

**🆕 Updated TikTok Scraper**: Now uses the advanced `clockworks/tiktok-scraper` with support for hashtags, profiles, search queries, direct URLs, advanced filtering, and media downloads.

## Setup

1. Ensure your server is running on `http://localhost:8080`
2. Set your `APIFY_API_TOKEN` environment variable
3. Import the Postman collection: `social-media-scrapers.postman_collection.json`

## Available Endpoints

### 🟢 Health Check
```
GET /
```

### 🔴 Reddit Scraping
```
POST /scrape/reddit
```

**Request Body Example:**
```json
{
  "keywords": "artificial intelligence",
  "timeFilter": "week",
  "maxItems": 10,
  "maxPostCount": 15,
  "maxComments": 50,
  "sort": "top",
  "includeComments": true,
  "includeEngagement": true,
  "platforms": ["reddit"]
}
```

**Parameters:**
- `keywords` (required): Search keywords
- `timeFilter` (optional): `hour`, `day`, `week`, `month`, `year`, `all`
- `maxItems` (optional): Maximum items to return
- `maxPostCount` (optional): Maximum posts to scrape
- `maxComments` (optional): Maximum comments per post
- `sort` (optional): `relevance`, `hot`, `top`, `new`, `rising`, `comments`
- `includeComments` (optional): Include comments in results
- `includeEngagement` (optional): Include engagement metrics
- `platforms` (optional): Array of platforms (for consistency)

### 🎵 TikTok Scraping
```
POST /scrape/tiktok
```

**Request Body Examples:**

**Hashtag Scraping:**
```json
{
  "hashtags": ["ai", "marketing", "business"],
  "resultsPerPage": 20,
  "proxyCountryCode": "US"
}
```

**Profile Scraping:**
```json
{
  "profiles": ["garyvee", "mrbeast"],
  "resultsPerPage": 10,
  "profileSorting": "latest",
  "profileScrapeSections": ["videos"]
}
```

**Search Queries:**
```json
{
  "searchQueries": ["startup advice", "entrepreneur tips"],
  "resultsPerPage": 15,
  "searchSection": "/video"
}
```

**Advanced Filtering:**
```json
{
  "hashtags": ["productivity"],
  "resultsPerPage": 25,
  "leastDiggs": 1000,
  "mostDiggs": 100000,
  "oldestPostDateUnified": "7",
  "excludePinnedPosts": true,
  "profileSorting": "popular"
}
```

**Parameters:**
- **Content Sources:**
  - `hashtags` (optional): Array of hashtags to scrape
  - `profiles` (optional): Array of usernames to scrape
  - `searchQueries` (optional): Array of search terms
  - `postURLs` (optional): Array of direct TikTok video URLs
  
- **Results Configuration:**
  - `resultsPerPage` (optional): Number of results per content source (default: 20)
  - `maxProfilesPerQuery` (optional): Max profiles when searching users (default: 10)
  
- **Profile Options:**
  - `profileScrapeSections` (optional): ["videos"] or ["videos", "reposts"]
  - `profileSorting` (optional): "latest", "popular", or "oldest"
  - `profileConnectionTypes` (optional): ["followers"] or ["following"] to scrape connections instead of posts
  
- **Filters:**
  - `oldestPostDateUnified` (optional): Date (YYYY-MM-DD) or days as number
  - `newestPostDate` (optional): Date (YYYY-MM-DD)
  - `leastDiggs` (optional): Minimum likes filter
  - `mostDiggs` (optional): Maximum likes filter
  - `excludePinnedPosts` (optional): Exclude pinned posts
  
- **Search Options:**
  - `searchSection` (optional): "" (top), "/video", or "/user"
  - `scrapeRelatedVideos` (optional): Include related videos for direct URLs
  
- **Download Options (increase cost):**
  - `shouldDownloadVideos` (optional): Download actual video files
  - `shouldDownloadCovers` (optional): Download thumbnails
  - `shouldDownloadSubtitles` (optional): Download subtitles/captions
  - `shouldDownloadSlideshowImages` (optional): Download slideshow images
  - `shouldDownloadAvatars` (optional): Download profile pictures
  - `shouldDownloadMusicCovers` (optional): Download music cover images
  
- **Other Options:**
  - `proxyCountryCode` (optional): Country code (US, GB, CA, etc.) or "None"
  - `videoKvStoreIdOrName` (optional): Custom storage name for downloaded media

### 🐦 Twitter Scraping
```
POST /scrape/twitter
```

**Request Body Examples:**

**Search by Terms:**
```json
{
  "searchTerms": ["artificial intelligence", "AI marketing"],
  "maxItems": 10,
  "sort": "Latest",
  "tweetLanguage": "en"
}
```

**Search by Username:**
```json
{
  "usernames": ["elonmusk", "sundarpichai"],
  "maxTweetsPerUser": 5,
  "includeReplies": false,
  "includeUserDetails": true
}
```

**Search by Hashtags:**
```json
{
  "hashtags": ["SaaS", "startup", "marketing"],
  "maxItems": 25,
  "sort": "Top",
  "minLikes": 10,
  "minRetweets": 5
}
```

**Advanced Query:**
```json
{
  "searchTerms": ["machine learning"],
  "excludeWords": ["crypto", "bitcoin"],
  "dateFrom": "2024-01-01",
  "dateTo": "2024-12-31",
  "maxItems": 15,
  "sort": "Top",
  "includeImages": true,
  "minLikes": 20
}
```

**Parameters:**
- `searchTerms` (optional): Array of search terms
- `usernames` (optional): Array of usernames to scrape
- `hashtags` (optional): Array of hashtags to search
- `maxItems` (optional): Maximum items to return
- `maxTweetsPerUser` (optional): Maximum tweets per user
- `sort` (optional): `Latest`, `Top`, `Photos`, `Videos`
- `tweetLanguage` (optional): Language code (en, es, fr, etc.)
- `includeReplies` (optional): Include reply tweets
- `includeUserDetails` (optional): Include detailed user information
- `includeImages` (optional): Include tweets with images
- `includeVideos` (optional): Include tweets with videos
- `minLikes` (optional): Minimum likes filter
- `minRetweets` (optional): Minimum retweets filter
- `dateFrom` (optional): Start date (YYYY-MM-DD)
- `dateTo` (optional): End date (YYYY-MM-DD)
- `excludeWords` (optional): Words to exclude from results

## Response Format

All endpoints return responses in this format:

```json
{
  "success": true,
  "data": {
    // Platform-specific data structure
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### Error Response Format

```json
{
  "success": false,
  "error": "Error description",
  "message": "Detailed error message",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Using the Postman Collection

### Import Collection

1. Open Postman
2. Click **Import** → **Upload Files**
3. Select `social-media-scrapers.postman_collection.json`
4. The collection will be imported with all test requests

### Environment Variables

The collection uses a `{{base_url}}` variable set to `http://localhost:8080`. You can:

1. **Create an Environment:**
   - Go to **Environments** → **Create Environment**
   - Add variable `base_url` with value `http://localhost:8080`
   - Save and select the environment

2. **Or modify the collection variable:**
   - Right-click the collection → **Edit**
   - Go to **Variables** tab
   - Update `base_url` if needed

### Test Requests Included

#### Reddit Scraping (3 requests)
1. **Reddit - Basic Search**: Simple AI search
2. **Reddit - Marketing Research**: Advanced SaaS marketing search
3. **Reddit - Minimal Parameters**: Minimal configuration test

#### TikTok Scraping (8 requests)
1. **TikTok - Hashtag Search**: Scrape by hashtags (AI, marketing, business)
2. **TikTok - Profile Scraping**: Scrape specific user profiles
3. **TikTok - Search Queries**: Search by keywords with video filter
4. **TikTok - Direct Video URLs**: Scrape specific videos + related content
5. **TikTok - Advanced Filtering**: Date/engagement filters with hashtags
6. **TikTok - Profile Followers/Following**: Scrape user connections instead of posts
7. **TikTok - With Downloads**: Enable thumbnails, subtitles, avatars download
8. **TikTok - Default (FYP)**: Empty body defaults to "For You Page" hashtag

#### Twitter Scraping (6 requests)
1. **Twitter - Search Terms**: Basic search functionality
2. **Twitter - Username Search**: Profile-based scraping
3. **Twitter - Hashtag Research**: Hashtag-based search
4. **Twitter - Advanced Query**: Complex filtering options
5. **Twitter - User Profile Details**: Detailed user info
6. **Twitter - Minimal Search**: Simple productivity search

#### Error Testing (3 requests)
1. **Reddit - Invalid Parameters**: Test error handling with invalid filters
2. **TikTok - Invalid Parameters**: Test invalid parameters and data types
3. **Twitter - Empty Request**: Test empty request handling

### Quick Start Guide

1. **Start your server:** `npm start` (or your preferred method)
2. **Test health check:** Run "Health Check" request
3. **Test Reddit:** Run "Reddit - Basic Search"
4. **Test TikTok:** Run "TikTok - Hashtag Search" 
5. **Test Twitter:** Run "Twitter - Search Terms"

### Tips for Testing

- **Start with basic requests** before trying advanced ones
- **Check your APIFY_API_TOKEN** if you get authentication errors
- **Monitor console output** in your server for detailed logs
- **Use the Error Testing folder** to verify error handling
- **Adjust `maxItems` parameters** to control API usage/costs

## Development Notes

- All endpoints log request parameters to console
- Responses include timestamps for debugging
- Error handling includes both generic and specific error messages
- The server runs on port 8080 by default (configurable via PORT env var)

## Next Steps

After testing these endpoints individually, you can integrate them into your frontend application or use them as building blocks for more complex market research workflows. 