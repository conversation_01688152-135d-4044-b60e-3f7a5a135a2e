runtime: nodejs20
instance_class: F1
env: standard
service: staging

env_variables:
  # Use environment variables or Secret Manager instead of hardcoded values
  # Set these in your CI/CD pipeline or local environment
  SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
  OPENAI_API_KEY: ${OPENAI_API_KEY}
  ZENROWS_API_KEY: ${ZENROWS_API_KEY}
  ZENROWS_API_URL: "https://api.zenrows.com/v1/"
  NEXT_PUBLIC_SUPABASE_URL: "https://staging-supabase-project.supabase.co"
  BASE_URL: "https://staging-dot-psychic-valve-439013-d2.lm.r.appspot.com"
  NEXT_PUBLIC_BASE_URL: "https://api-staging.smartberry.ai"
  PERPLEXITY_API_KEY: ${PERPLEXITY_API_KEY}
  ZERO_UPSTREAM_DB: ${ZERO_UPSTREAM_DB}
  CLAUDE_API_KEY: ${CLAUDE_API_KEY}
  FIRECRAWL_API_KEY: ${FIRECRAWL_API_KEY}
  LANGFUSE_SECRET_KEY: ${LANGFUSE_SECRET_KEY}
  LANGFUSE_PUBLIC_KEY: ${LANGFUSE_PUBLIC_KEY}
  LANGFUSE_BASEURL: "https://cloud.langfuse.com"
  OPENROUTER_API_KEY: ${OPENROUTER_API_KEY}
  AYRSHARE_PROFILE_KEY: ${AYRSHARE_PROFILE_KEY}
  AYRSHARE_API_KEY: ${AYRSHARE_API_KEY}
  JIGSAW_API_KEY: ${JIGSAW_API_KEY}

handlers:
  - url: /.*
    script: auto