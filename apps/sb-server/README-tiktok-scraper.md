# TikTok Scraping with Apify

This module provides TikTok trending content scraping functionality using the Apify TikTok Trending Scraper for market research purposes.

## Setup

### 1. Environment Variables

The TikTok scraper uses the same Apify API token as the Reddit scraper. Make sure you have set up the following environment variable in your `.env` file:

```env
# Apify API Token for TikTok scraping
APIFY_API_TOKEN=your_apify_token_here
```

You can get your Apify API token from:
1. Sign up at [Apify Console](https://console.apify.com/)
2. Go to Account → Integrations
3. Copy your API token

### 2. Test the Setup

Run the test script to verify everything is working:

```bash
npm run test:tiktok
```

Or manually run:

```bash
tsx src/scripts/test-tiktok-scraper.ts
```

## Usage

### Basic TikTok Trending Scraping

```typescript
import { scrapeTikTok } from './lib/services/scrape-tiktok.js';

const results = await scrapeTikTok({
  maxVideos: 20,
  region: 'en',
  keywords: 'ai,marketing,business'
});

console.log(`Found ${results.totalVideos} trending videos`);
console.log(`Videos: ${results.videos.length}`);
```

### Trending Content Without Keywords

```typescript
// Get all trending content
const allTrending = await scrapeTikTok({
  maxVideos: 50,
  region: 'en'
});
```

### Different Regions

```typescript
// Get trending content from different regions
const regions = ['en', 'es', 'fr', 'de'];
for (const region of regions) {
  const results = await scrapeTikTok({
    maxVideos: 10,
    region: region
  });
  console.log(`${region}: ${results.totalVideos} videos`);
}
```

## API Reference

### `scrapeTikTok(params: TikTokScrapeParams)`

#### Parameters

- `maxVideos` (optional): Maximum number of videos to scrape
  - Type: `number`
  - Default: `50`
  - Range: 1-100 (capped to avoid long-running jobs)
- `region` (optional): Language/region for trending content
  - Type: `string`
  - Default: `'en'`
  - Examples: `'en'`, `'es'`, `'fr'`, `'de'`, `'ja'`, `'ko'`
- `keywords` (optional): Comma-separated keywords to filter content
  - Type: `string`
  - Example: `'ai,marketing,business'`
  - Note: Filters results after scraping based on title, description, hashtags, and usernames

#### Returns

Returns a `TikTokScrapeResponse` object containing:

- `videos`: Array of TikTok videos
- `totalVideos`: Total number of videos found (after filtering)
- `scrapedAt`: Timestamp of when data was scraped
- `region`: The region/language used for scraping
- `keywords`: The keywords used for filtering (if any)

### `formatTikTokDataForAnalysis(data: TikTokScrapeResponse)`

Formats TikTok data into a readable string format suitable for AI analysis, including:
- Video details with engagement metrics
- Creator information
- Hashtag analysis
- Music trends

## Data Structure

### TikTok Video

```typescript
interface TikTokVideo {
  id: string;
  url: string;
  title?: string;
  description?: string;
  username: string;
  userDisplayName?: string;
  userFollowers?: number;
  userFollowing?: number;
  userLikes?: number;
  userVideos?: number;
  videoLikes?: number;
  videoComments?: number;
  videoShares?: number;
  videoViews?: number;
  videoCreatedTime?: string;
  hashtags?: string[];
  mentions?: string[];
  isAd?: boolean;
  musicTitle?: string;
  musicAuthor?: string;
  videoUrl?: string;
  thumbnailUrl?: string;
  duration?: number;
  scrapedAt: string;
  dataType: 'tiktok_video';
}
```

## Error Handling

The scraper includes comprehensive error handling:

- **Missing API Token**: Clear error message with setup instructions
- **API Quota Exceeded**: Specific error for quota limits
- **No Results Found**: Graceful handling when no trending videos are found
- **Network Errors**: Proper error propagation with context
- **Data Processing Errors**: Individual video errors don't break the entire result

## Cost Considerations

- TikTok Trending Scraper costs $15/month + usage
- Usage costs depend on the number of videos scraped
- Consider setting reasonable `maxVideos` limits to control costs
- Free tier credits may not cover extensive TikTok scraping

## Features

### 🎯 **Trending Content Analysis**
- **Latest Trends**: Get the most popular TikTok videos and hashtags
- **Regional Trending**: Access trending content from different regions/languages
- **Hashtag Insights**: Automatic extraction and analysis of trending hashtags
- **Engagement Metrics**: Views, likes, comments, shares for each video

### 🔍 **Content Filtering**
- **Keyword Filtering**: Filter results by relevant keywords after scraping
- **Creator Analysis**: Get detailed information about content creators
- **Music Trends**: Track trending audio and music used in videos
- **Content Types**: Identify different types of content and formats

### 📊 **Data Processing**
- **Normalized Data**: Consistent data structure regardless of API response variations
- **Safe Parsing**: Robust parsing that handles missing or malformed data
- **Analytics Ready**: Formatted output perfect for AI analysis and reporting

## Best Practices

1. **Reasonable Limits**: Set appropriate `maxVideos` to avoid long-running jobs and high costs
2. **Regional Targeting**: Use specific regions that match your target audience
3. **Keyword Strategy**: Use relevant keywords to filter for business-related content
4. **Regular Monitoring**: TikTok trends change rapidly, scrape regularly for current insights
5. **Cost Management**: Monitor your Apify usage to stay within budget

## Use Cases

### 📈 **Marketing Research**
```typescript
// Research marketing trends
const marketingTrends = await scrapeTikTok({
  maxVideos: 30,
  keywords: 'marketing,business,entrepreneur,startup',
  region: 'en'
});
```

### 🎨 **Content Inspiration**
```typescript
// Find content ideas in your niche
const contentIdeas = await scrapeTikTok({
  maxVideos: 20,
  keywords: 'productivity,tech,ai',
  region: 'en'
});
```

### 📊 **Competitor Analysis**
```typescript
// Monitor trending content in your industry
const industryTrends = await scrapeTikTok({
  maxVideos: 40,
  keywords: 'saas,software,technology',
  region: 'en'
});
```

### 🌍 **Global Trends**
```typescript
// Compare trends across regions
const regions = ['en', 'es', 'fr', 'de'];
for (const region of regions) {
  const trends = await scrapeTikTok({
    maxVideos: 15,
    keywords: 'business',
    region: region
  });
  // Analyze regional differences
}
```

## Troubleshooting

### Common Issues

1. **"Invalid or missing Apify API token"**
   - Check that `APIFY_API_TOKEN` is set in your environment variables
   - Verify the token is correct and active

2. **"API quota exceeded"**
   - Check your Apify account usage limits
   - TikTok scraper has different pricing than Reddit scraper
   - Consider upgrading your plan if needed

3. **No results found**
   - TikTok trending content changes frequently
   - Try different regions or remove keyword filters
   - Check if the region code is valid

4. **Slow performance**
   - Reduce `maxVideos` parameter
   - TikTok scraping may be slower than Reddit due to video content

5. **Filtered results are empty**
   - Your keywords might be too specific
   - Try broader or alternative keywords
   - Check trending content without filters first

### Debug Mode

For more detailed logging, you can modify the TikTok scraper to enable debug mode in the Apify actor settings.

## Data Insights

### Hashtag Analysis
The formatter automatically analyzes hashtags and provides:
- Most frequently used hashtags across videos
- Hashtag popularity rankings
- Trending hashtag patterns

### Engagement Patterns
Track engagement metrics to understand:
- What content performs best
- Creator engagement rates
- Trending content characteristics

### Music Trends
Identify trending audio and music:
- Popular songs and sounds
- Music creators gaining traction
- Audio trends in your niche

## Future Enhancements

Potential improvements for the TikTok scraper:
- Search-based scraping (not just trending)
- User profile analysis
- Comment sentiment analysis
- Video content analysis using AI
- Cross-platform trend correlation with Reddit data

## Integration Notes

The TikTok scraper is designed to work alongside the Reddit scraper for comprehensive social media research. Both use the same Apify API token and follow similar patterns for easy integration into market research workflows. 