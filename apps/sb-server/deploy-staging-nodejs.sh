#!/bin/bash

# Deploy sb-server to Staging Environment using Node.js runtime (no Docker)
set -e

# Cleanup function
cleanup() {
  if [[ -f "package.json.backup" ]]; then
    echo "🔄 Restoring original package.json..."
    mv package.json.backup package.json
    rm -f package.json.bak
  fi
  if [[ -d "deploy-temp" ]]; then
    echo "🧹 Cleaning up temporary deployment directory..."
    rm -rf deploy-temp
  fi
}

# Set trap to ensure cleanup on exit
trap cleanup EXIT

echo "🚀 Deploying sb-server to Staging environment (Node.js runtime)..."

# Ensure we're in the sb-server directory
cd "$(dirname "$0")"

# Build sb-server (prebuild will handle zero-schema setup)
echo "🔨 Building sb-server..."
pnpm run build

# Create a temporary deployment directory
echo "📦 Preparing deployment package..."
TEMP_DIR="deploy-temp"
rm -rf $TEMP_DIR
mkdir -p $TEMP_DIR

# Copy built files and verify they exist
if [ ! -d "dist" ]; then
  echo "❌ Error: dist directory not found! Build may have failed."
  exit 1
fi

if [ ! -f "dist/index.js" ]; then
  echo "❌ Error: dist/index.js not found! Build may have failed."
  exit 1
fi

echo "✅ Build artifacts found, copying to deployment directory..."
cp -r dist $TEMP_DIR/
cp package.json.backup $TEMP_DIR/package.json 2>/dev/null || cp package.json $TEMP_DIR/

# Verify the files were copied correctly
if [ ! -f "$TEMP_DIR/dist/index.js" ]; then
  echo "❌ Error: Failed to copy dist/index.js to deployment directory"
  exit 1
fi

echo "✅ dist/index.js successfully copied to deployment directory"

# No tsconfig.json needed since we're deploying pre-built JavaScript

# Copy the zero-schema files from dist to the correct location for direct imports
echo "📋 Copying zero-schema files to deployment directory..."
mkdir -p $TEMP_DIR/dist/lib/zero-schema

# The zero-schema files should already be in dist/lib/zero-schema since they're copied during build
if [ -d "dist/lib/zero-schema" ]; then
  echo "✅ zero-schema files found in build output"
  ls -la dist/lib/zero-schema/
else
  echo "❌ zero-schema files not found in build output!"
  echo "Files were not copied during build process"
  exit 1
fi

# Verify the zero-schema files are in the deployment dist
echo "📋 Verifying zero-schema files in deployment directory..."
if [ -f "$TEMP_DIR/dist/lib/zero-schema/schema.js" ]; then
  echo "✅ schema.js found in deployment directory"
  head -n 5 $TEMP_DIR/dist/lib/zero-schema/schema.js
else
  echo "❌ schema.js not found in deployment directory!"
  exit 1
fi

if [ -f "$TEMP_DIR/dist/lib/zero-schema/mutator.js" ]; then
  echo "✅ mutator.js found in deployment directory"
  head -n 5 $TEMP_DIR/dist/lib/zero-schema/mutator.js
else
  echo "❌ mutator.js not found in deployment directory!"
  exit 1
fi

# Update package.json to remove dev deps and build scripts (no longer needed for deployment)
cd $TEMP_DIR
# Use Node.js to properly modify JSON
node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
delete pkg.scripts.build;
delete pkg.devDependencies;
fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
"

# Generate fresh package-lock.json that matches our modified package.json
echo "📝 Generating fresh package-lock.json..."
rm -f package-lock.json npm-shrinkwrap.json node_modules/.package-lock.json
# First run npm install --package-lock-only to generate initial lock file
npm install --package-lock-only
# Verify the package-lock was created successfully
if [ ! -f "package-lock.json" ]; then
  echo "❌ Error: Failed to generate package-lock.json"
  exit 1
fi
echo "✅ Fresh package-lock.json generated successfully"
cd ..

# Build is already complete, temp directory has the built files

# Deploy to App Engine using Node.js runtime
echo "☁️ Deploying to Google App Engine..."
PROJECT_ID="psychic-valve-439013-d2"

# Copy app engine config to temp directory
if [[ -f ".env.staging" ]]; then
  echo "📋 Loading environment variables from .env.staging..."
  source .env.staging
  cp app.staging.secure.yaml $TEMP_DIR/
  gcloud app deploy $TEMP_DIR/app.staging.secure.yaml \
    --project=${PROJECT_ID} \
    --quiet
else
  echo "⚠️  Using existing app.staging.yaml (contains hardcoded keys)"
  echo "💡 Create .env.staging file to use secure deployment"
  cp app.staging.yaml $TEMP_DIR/
  gcloud app deploy $TEMP_DIR/app.staging.yaml \
    --project=${PROJECT_ID} \
    --quiet
fi

# Cleanup temp directory
echo "🧹 Cleaning up temporary files..."
rm -rf $TEMP_DIR

echo "✅ Staging deployment complete!"
echo "🌐 URL: https://staging-dot-${PROJECT_ID}.lm.r.appspot.com"