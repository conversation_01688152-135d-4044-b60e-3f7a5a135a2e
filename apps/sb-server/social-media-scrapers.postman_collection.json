{"info": {"name": "Social Media Scrapers API", "description": "Collection for testing Reddit, TikTok, and Twitter scraping endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}}, {"name": "Reddit Scraping", "item": [{"name": "Reddit - Basic Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keywords\": \"artificial intelligence\",\n  \"timeFilter\": \"week\",\n  \"maxItems\": 10,\n  \"includeComments\": true,\n  \"includeEngagement\": true\n}"}, "url": {"raw": "{{base_url}}/scrape/reddit", "host": ["{{base_url}}"], "path": ["scrape", "reddit"]}}}, {"name": "Reddit - Marketing Research", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keywords\": \"SaaS marketing tools\",\n  \"timeFilter\": \"month\",\n  \"maxItems\": 20,\n  \"maxPostCount\": 15,\n  \"maxComments\": 50,\n  \"sort\": \"top\",\n  \"includeComments\": true,\n  \"includeEngagement\": true,\n  \"platforms\": [\"reddit\"]\n}"}, "url": {"raw": "{{base_url}}/scrape/reddit", "host": ["{{base_url}}"], "path": ["scrape", "reddit"]}}}, {"name": "Reddit - Minimal Parameters", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keywords\": \"startup advice\"\n}"}, "url": {"raw": "{{base_url}}/scrape/reddit", "host": ["{{base_url}}"], "path": ["scrape", "reddit"]}}}]}, {"name": "TikTok Scraping", "item": [{"name": "TikTok - Hashtag Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hashtags\": [\"ai\", \"marketing\", \"business\"],\n  \"resultsPerPage\": 20,\n  \"proxyCountryCode\": \"US\"\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "TikTok - Profile Scraping", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profiles\": [\"garyvee\", \"mrbeast\"],\n  \"resultsPerPage\": 10,\n  \"profileSorting\": \"latest\",\n  \"profileScrapeSections\": [\"videos\"]\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "TikTok - Search Queries", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"searchQueries\": [\"startup advice\", \"entrepreneur tips\"],\n  \"resultsPerPage\": 15,\n  \"searchSection\": \"/video\"\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "TikTok - Direct Video URLs", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"postURLs\": [\n    \"https://www.tiktok.com/@username/video/1234567890123456789\"\n  ],\n  \"scrapeRelatedVideos\": true,\n  \"resultsPerPage\": 5\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "TikTok - Advanced Filtering", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hashtags\": [\"productivity\", \"lifehacks\"],\n  \"resultsPerPage\": 25,\n  \"leastDiggs\": 1000,\n  \"mostDiggs\": 100000,\n  \"oldestPostDateUnified\": \"7\",\n  \"excludePinnedPosts\": true,\n  \"profileSorting\": \"popular\"\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "TikTok - Profile Followers/Following", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profiles\": [\"garyvee\"],\n  \"profileConnectionTypes\": [\"followers\"],\n  \"maxProfilesPerQuery\": 50\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "TikTok - With Downloads", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hashtags\": [\"funny\"],\n  \"resultsPerPage\": 5,\n  \"shouldDownloadVideos\": false,\n  \"shouldDownloadCovers\": true,\n  \"shouldDownloadSubtitles\": true,\n  \"shouldDownloadAvatars\": true\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "TikTok - <PERSON><PERSON><PERSON> (FYP)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}]}, {"name": "Twitter Scraping", "item": [{"name": "Twitter - Search Terms", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"searchTerms\": [\"artificial intelligence\", \"AI marketing\"],\n  \"maxItems\": 10,\n  \"sort\": \"Latest\",\n  \"tweetLanguage\": \"en\"\n}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}}}, {"name": "Twitter - Username Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"usernames\": [\"elonmusk\", \"sundarpichai\"],\n  \"maxTweetsPerUser\": 5,\n  \"includeReplies\": false\n}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}}}, {"name": "Twitter - Hashtag Research", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hashtags\": [\"SaaS\", \"startup\", \"marketing\"],\n  \"maxItems\": 25,\n  \"sort\": \"Top\",\n  \"tweetLanguage\": \"en\",\n  \"minLikes\": 10,\n  \"minRetweets\": 5\n}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}}}, {"name": "Twitter - Advanced Query", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"searchTerms\": [\"machine learning\"],\n  \"excludeWords\": [\"crypto\", \"bitcoin\"],\n  \"dateFrom\": \"2024-01-01\",\n  \"dateTo\": \"2024-12-31\",\n  \"maxItems\": 15,\n  \"sort\": \"Top\",\n  \"includeImages\": true,\n  \"includeVideos\": false,\n  \"minLikes\": 20\n}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}}}, {"name": "Twitter - User Profile Details", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"usernames\": [\"naval\", \"paulg\"],\n  \"maxTweetsPerUser\": 10,\n  \"includeUserDetails\": true,\n  \"includeReplies\": true\n}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}}}, {"name": "Twitter - Minimal Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"searchTerms\": [\"productivity\"],\n  \"maxItems\": 5\n}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}}}]}, {"name": "Error Testing", "item": [{"name": "Reddit - Invalid Parameters", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keywords\": \"\",\n  \"timeFilter\": \"invalid_filter\",\n  \"maxItems\": -5\n}"}, "url": {"raw": "{{base_url}}/scrape/reddit", "host": ["{{base_url}}"], "path": ["scrape", "reddit"]}}}, {"name": "TikTok - Invalid Parameters", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hashtags\": [],\n  \"profiles\": [],\n  \"resultsPerPage\": -10,\n  \"proxyCountryCode\": \"INVALID_CODE\",\n  \"leastDiggs\": \"not_a_number\"\n}"}, "url": {"raw": "{{base_url}}/scrape/tiktok", "host": ["{{base_url}}"], "path": ["scrape", "tiktok"]}}}, {"name": "Twitter - Empty Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/scrape/twitter", "host": ["{{base_url}}"], "path": ["scrape", "twitter"]}}}]}]}