# sb-server Deployment Guide

This guide explains the new Node.js runtime deployment approach (no Docker required).

## Overview

The deployment has been updated to use App Engine's standard Node.js runtime instead of Docker. This approach:
- ✅ Eliminates Docker complexity and build time overhead
- ✅ Properly handles monorepo workspace dependencies
- ✅ Uses secure environment variable management
- ✅ Maintains the same functionality with better performance

## Quick Start

### 1. Set up environment variables (Secure Deployment)
```bash
# Copy the template and fill in your values
cp .env.preview.template .env.preview

# Edit .env.preview with your actual API keys
# DO NOT commit this file to git!
```

### 2. Deploy to Preview
```bash
# Run the deployment script
pnpm run deploy:preview

# Or manually:
./deploy-preview-nodejs.sh
```

## How it Works

### Build Process
1. **Build workspace dependencies**: `pnpm --filter @kit/zero-schema build`
   - Compiles TypeScript to JavaScript in `packages/zero-schema/dist/`
   - Generates type declarations for proper IDE support

2. **Build sb-server**: `pnpm run build`
   - Compiles the main application TypeScript code
   - Resolves workspace dependencies through pnpm's workspace protocol

3. **Install production dependencies**: `pnpm install --prod --frozen-lockfile`
   - Installs only production dependencies
   - pnpm automatically resolves workspace packages to their built versions

4. **Deploy**: Uses standard App Engine Node.js runtime
   - No Docker image building required
   - Faster deployment times
   - Standard App Engine scaling and management

### File Structure After Build
```
apps/sb-server/
├── dist/                     # Built JavaScript files
├── node_modules/             # Production dependencies
├── package.json              # Package configuration
├── app.preview.yaml          # Legacy config (with hardcoded keys)
├── app.preview.secure.yaml   # Secure config (uses env vars)
└── .env.preview              # Environment variables (not in git)
```

## Configuration Files

### app.preview.secure.yaml (Recommended)
- Uses environment variables instead of hardcoded API keys
- Automatically used when `.env.preview` exists
- More secure for production deployments

### app.preview.yaml (Legacy)
- Contains hardcoded API keys (security risk)
- Used as fallback when no `.env.preview` file exists
- Should be replaced with secure version

## Environment Variables

Required environment variables (see `.env.preview.template`):
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key
- `OPENAI_API_KEY`: OpenAI API key
- `CLAUDE_API_KEY`: Claude API key
- `PERPLEXITY_API_KEY`: Perplexity API key
- `FIRECRAWL_API_KEY`: Firecrawl API key
- `ZERO_UPSTREAM_DB`: PostgreSQL connection string
- `LANGFUSE_SECRET_KEY` & `LANGFUSE_PUBLIC_KEY`: Langfuse keys
- `OPENROUTER_API_KEY`: OpenRouter API key
- `AYRSHARE_PROFILE_KEY` & `AYRSHARE_API_KEY`: Ayrshare keys
- `ZENROWS_API_KEY`: ZenRows scraping service key

## Commands

### Development
```bash
pnpm run dev                    # Start development server
```

### Building
```bash
pnpm run build:deps            # Build only workspace dependencies
pnpm run build                 # Build only sb-server
pnpm run build:all             # Build dependencies + sb-server
```

### Deployment
```bash
pnpm run deploy:preview        # Deploy to preview environment
./deploy-preview-nodejs.sh     # Direct script execution
```

### Testing
```bash
pnpm test                      # Run tests
pnpm run test:watch            # Run tests in watch mode
```

## Troubleshooting

### Build Issues
- **"Module not found @kit/zero-schema"**: Run `pnpm run build:deps` first
- **"No such file dist/index.js"**: Run `pnpm run build` to compile TypeScript
- **TypeScript errors**: Check that all dependencies are installed with `pnpm install`

### Deployment Issues
- **"Environment variable not set"**: Create and populate `.env.preview` file
- **"gcloud command not found"**: Install Google Cloud SDK
- **Authentication errors**: Run `gcloud auth login` and `gcloud config set project YOUR_PROJECT_ID`

### Runtime Issues
- **"Cannot find module"**: Ensure production dependencies are installed
- **API key errors**: Verify environment variables are set correctly in App Engine

## Migration from Docker

The old Docker-based deployment is still available but deprecated:
- `deploy-preview.sh` (Docker approach)
- `deploy-preview-nodejs.sh` (New Node.js approach)

**Benefits of the new approach:**
- 50-70% faster build times (no Docker layer)
- Simpler CI/CD integration
- Better error messages and debugging
- Native App Engine features (auto-scaling, health checks)
- Reduced storage and bandwidth usage

## Security Best Practices

1. **Never commit API keys**: Use `.env.preview` and add it to `.gitignore`
2. **Use Google Secret Manager**: For production, consider Secret Manager integration
3. **Rotate keys regularly**: Update API keys periodically
4. **Principle of least privilege**: Use service accounts with minimal required permissions
5. **Monitor access**: Enable Google Cloud audit logging for deployments