# Twitter/X Scraping with Apify

This module provides comprehensive Twitter/X content scraping functionality using the Apify Twitter Scraper for market research, sentiment analysis, and social media monitoring.

## Setup

### 1. Environment Variables

The Twitter scraper uses the same Apify API token as the Reddit and TikTok scrapers. Make sure you have set up the following environment variable in your `.env` file:

```env
# Apify API Token for Twitter scraping
APIFY_API_TOKEN=your_apify_token_here
```

You can get your Apify API token from:
1. Sign up at [Apify Console](https://console.apify.com/)
2. Go to Account → Integrations
3. Copy your API token

### 2. Test the Setup

Run the test script to verify everything is working:

```bash
npm run test:twitter
```

Or manually run:

```bash
tsx src/scripts/test-twitter-scraper.ts
```

## Usage

### Basic Twitter Scraping

```typescript
import { scrapeTwitter } from './lib/services/scrape-x.js';

// Search for tweets with keywords
const results = await scrapeTwitter({
  searchTerms: ['artificial intelligence', 'machine learning'],
  maxItems: 50,
  sort: 'Latest',
  tweetLanguage: 'en'
});

console.log(`Found ${results.totalItems} items`);
console.log(`Tweets: ${results.tweets.length}`);
console.log(`Users: ${results.users.length}`);
```

### Search by Twitter Handles

```typescript
// Get tweets from specific users
const handleResults = await scrapeTwitter({
  twitterHandles: ['elonmusk', 'OpenAI', 'microsoft'],
  maxItems: 30,
  sort: 'Top'
});
```

### Advanced Filtering

```typescript
// Search with multiple filters
const filteredResults = await scrapeTwitter({
  searchTerms: ['startup', 'entrepreneur'],
  maxItems: 100,
  onlyVerifiedUsers: true,
  minimumFavorites: 100,
  minimumRetweets: 50,
  tweetLanguage: 'en',
  start: '2024-01-01',
  end: '2024-12-31',
  sort: 'Top'
});
```

### User and Engagement Filters

```typescript
// Target specific user interactions
const mentionResults = await scrapeTwitter({
  mentioning: 'OpenAI',
  minimumReplies: 10,
  onlyTwitterBlue: true,
  maxItems: 25
});

// Search tweets from specific author
const authorResults = await scrapeTwitter({
  author: 'sundarpichai',
  onlyImage: true,
  maxItems: 20
});
```

## API Reference

### `scrapeTwitter(params: TwitterScrapeParams)`

#### Parameters

- `searchTerms` (optional): Array of search terms/keywords
  - Type: `string[]`
  - Example: `['AI', 'machine learning', 'startup']`
- `twitterHandles` (optional): Array of Twitter usernames to scrape
  - Type: `string[]`
  - Example: `['elonmusk', 'OpenAI']`
- `maxItems` (optional): Maximum number of items to scrape
  - Type: `number`
  - Default: `100`
  - Range: 1-1000 (capped to control costs)
- `tweetLanguage` (optional): Language filter for tweets
  - Type: `string`
  - Example: `'en'`, `'es'`, `'fr'`, `'de'`
- `onlyVerifiedUsers` (optional): Only tweets from verified users
  - Type: `boolean`
  - Default: `false`
- `onlyTwitterBlue` (optional): Only tweets from Twitter Blue subscribers
  - Type: `boolean`
  - Default: `false`
- `onlyImage` (optional): Only tweets containing images
  - Type: `boolean`
  - Default: `false`
- `onlyVideo` (optional): Only tweets containing videos
  - Type: `boolean`
  - Default: `false`
- `onlyQuote` (optional): Only quote tweets
  - Type: `boolean`
  - Default: `false`
- `author` (optional): Tweets from specific author
  - Type: `string`
  - Example: `'elonmusk'`
- `mentioning` (optional): Tweets mentioning specific user
  - Type: `string`
  - Example: `'OpenAI'`
- `minimumRetweets` (optional): Minimum number of retweets
  - Type: `number`
- `minimumFavorites` (optional): Minimum number of likes
  - Type: `number`
- `minimumReplies` (optional): Minimum number of replies
  - Type: `number`
- `start` (optional): Start date for search
  - Type: `string`
  - Format: `'YYYY-MM-DD'`
- `end` (optional): End date for search
  - Type: `string`
  - Format: `'YYYY-MM-DD'`
- `sort` (optional): Sort order for results
  - Type: `'Latest' | 'Top'`
  - Default: `'Latest'`

#### Returns

Returns a `TwitterScrapeResponse` object containing:

- `tweets`: Array of Twitter tweets
- `users`: Array of Twitter users
- `totalItems`: Total number of items found
- `searchTerms`: Search terms used (if any)
- `twitterHandles`: Twitter handles searched (if any)
- `scrapedAt`: Timestamp of when data was scraped
- `filters`: Applied filters and settings

### `formatTwitterDataForAnalysis(data: TwitterScrapeResponse)`

Formats Twitter data into a readable string format suitable for AI analysis, including:
- Top tweets ranked by engagement
- Hashtag analysis and trending topics
- Top mentions and user interactions
- User profiles with follower metrics
- Verification status and engagement patterns

### `buildAdvancedTwitterQuery(params)`

Builds advanced Twitter search queries using Twitter's search operators:

```typescript
const query = buildAdvancedTwitterQuery({
  keywords: ['startup', 'entrepreneur'],
  fromUser: 'ycombinator',
  hashtags: ['startup', 'tech'],
  minLikes: 100,
  since: '2024-01-01',
  lang: 'en',
  verified: true
});
// Result: "startup entrepreneur from:ycombinator #startup OR #tech min_faves:100 since:2024-01-01 lang:en filter:verified"
```

## Data Structure

### Twitter Tweet

```typescript
interface TwitterTweet {
  type: 'tweet';
  id: string;
  url: string;
  twitterUrl: string;
  text: string;
  retweetCount: number;
  replyCount: number;
  likeCount: number;
  quoteCount: number;
  createdAt: string;
  lang: string;
  bookmarkCount?: number;
  isReply: boolean;
  isRetweet: boolean;
  isQuote: boolean;
  author: TwitterUser;
  hashtags?: string[];
  mentions?: string[];
  urls?: string[];
  media?: any[];
  quote?: TwitterTweet;
  scrapedAt: string;
  dataType: 'twitter_tweet';
}
```

### Twitter User

```typescript
interface TwitterUser {
  type: 'user';
  userName: string;
  url: string;
  twitterUrl: string;
  id: string;
  name: string;
  isVerified: boolean;
  isBlueVerified?: boolean;
  verifiedType?: string;
  hasNftAvatar: boolean;
  profilePicture: string;
  coverPicture?: string;
  description: string;
  location: string;
  followers: number;
  following: number;
  protected: boolean;
  canDm: boolean;
  createdAt: string;
  favouritesCount: number;
  statusesCount: number;
}
```

## Advanced Features

### 🔍 **Twitter Search Operators**

The scraper supports Twitter's advanced search operators through the `buildAdvancedTwitterQuery` function:

#### User Searches
- `from:username` - Tweets from specific user
- `to:username` - Replies to specific user  
- `@username` - Mentions of specific user

#### Content Filters
- `#hashtag` - Tweets with specific hashtags
- `"exact phrase"` - Exact phrase matching
- `filter:verified` - Only verified users
- `filter:media` - Only tweets with media
- `filter:links` - Only tweets with links

#### Engagement Filters
- `min_faves:100` - Minimum likes
- `min_retweets:50` - Minimum retweets
- `min_replies:10` - Minimum replies

#### Date/Time Filters
- `since:2024-01-01` - Tweets after date
- `until:2024-12-31` - Tweets before date
- `lang:en` - Language filter

#### Example Advanced Queries
```typescript
// Find viral AI tweets from verified users
const viralAI = buildAdvancedTwitterQuery({
  keywords: ['artificial intelligence'],
  verified: true,
  minLikes: 1000,
  minRetweets: 100,
  lang: 'en'
});

// Monitor brand mentions with high engagement
const brandMentions = buildAdvancedTwitterQuery({
  mentioning: 'YourBrand',
  minReplies: 5,
  hasMedia: true,
  since: '2024-01-01'
});
```

### 📊 **Data Analysis Features**

#### Engagement Scoring
- Automatic calculation of total engagement (likes + retweets + replies + quotes)
- Engagement rate analysis by follower count
- Viral content identification

#### Hashtag Trending
- Automatic extraction and ranking of hashtags
- Hashtag frequency analysis
- Trending topic identification

#### User Analysis
- Verification status tracking (verified vs. Twitter Blue)
- Follower-to-following ratios
- User engagement patterns

#### Content Classification
- Media type detection (images, videos)
- Quote tweet vs. original content
- Reply thread analysis

## Use Cases

### 📈 **Brand Monitoring**
```typescript
// Monitor brand mentions and sentiment
const brandMonitoring = await scrapeTwitter({
  searchTerms: ['YourBrand', '@YourBrand'],
  maxItems: 200,
  minimumFavorites: 5,
  sort: 'Latest'
});
```

### 🏢 **Competitor Analysis**
```typescript
// Analyze competitor content strategy
const competitorAnalysis = await scrapeTwitter({
  twitterHandles: ['competitor1', 'competitor2', 'competitor3'],
  maxItems: 100,
  onlyImage: true, // Focus on visual content
  sort: 'Top'
});
```

### 🎯 **Influencer Research**
```typescript
// Find influential voices in your industry
const influencerResearch = await scrapeTwitter({
  searchTerms: ['SaaS', 'startup', 'entrepreneur'],
  onlyVerifiedUsers: true,
  minimumRetweets: 50,
  maxItems: 150
});
```

### 📰 **Trend Monitoring**
```typescript
// Track trending topics and hashtags
const trendMonitoring = await scrapeTwitter({
  searchTerms: ['#AI2024', '#TechTrends', '#Innovation'],
  minimumLikes: 100,
  tweetLanguage: 'en',
  maxItems: 300
});
```

### 💼 **Lead Generation**
```typescript
// Find potential customers mentioning pain points
const leadGeneration = await scrapeTwitter({
  searchTerms: ['looking for', 'need help with', 'recommendations for'],
  mentioning: 'your_industry_keywords',
  maxItems: 100,
  sort: 'Latest'
});
```

## Error Handling

The scraper includes comprehensive error handling:

- **Missing API Token**: Clear error message with setup instructions
- **API Quota Exceeded**: Specific error for quota limits
- **Invalid Parameters**: Validation for required search parameters
- **Network Errors**: Proper error propagation with context
- **Rate Limiting**: Automatic handling through Apify's proxy system

## Cost Considerations

- Twitter scraping costs vary based on usage
- Typical cost: ~$0.50-1.00 per 1000 tweets (varies by Apify plan)
- Use `maxItems` parameter to control costs
- Consider using filters to get more relevant results with fewer items
- Monitor your Apify usage dashboard regularly

## Best Practices

### 🎯 **Efficient Searching**
1. **Use Specific Keywords**: Target relevant terms to reduce noise
2. **Apply Filters Early**: Use engagement and verification filters
3. **Reasonable Limits**: Set appropriate `maxItems` for your needs
4. **Date Ranges**: Use recent date ranges for trending topics

### 📊 **Data Quality**
1. **Engagement Thresholds**: Filter low-engagement content
2. **Language Filtering**: Specify language for consistent results
3. **Verification Status**: Consider verified vs. regular users
4. **Content Types**: Filter by media type when relevant

### 💰 **Cost Management**
1. **Batch Searches**: Group related searches together
2. **Monitor Usage**: Track your Apify account consumption
3. **Optimize Queries**: Use precise search terms
4. **Regular Reviews**: Analyze cost per insight ratio

## Twitter Search Operators Reference

### Basic Operators
| Operator | Description | Example |
|----------|-------------|---------|
| `"phrase"` | Exact phrase | `"artificial intelligence"` |
| `OR` | Either term | `AI OR "machine learning"` |
| `-term` | Exclude term | `AI -crypto` |
| `#hashtag` | Hashtag | `#AI` |
| `@user` | Mention | `@OpenAI` |

### User Operators
| Operator | Description | Example |
|----------|-------------|---------|
| `from:user` | From user | `from:elonmusk` |
| `to:user` | To user | `to:OpenAI` |
| `filter:verified` | Verified users | `AI filter:verified` |

### Content Operators
| Operator | Description | Example |
|----------|-------------|---------|
| `filter:media` | Has media | `startup filter:media` |
| `filter:images` | Has images | `product filter:images` |
| `filter:videos` | Has videos | `demo filter:videos` |
| `filter:links` | Has links | `news filter:links` |

### Engagement Operators
| Operator | Description | Example |
|----------|-------------|---------|
| `min_faves:100` | Min likes | `AI min_faves:100` |
| `min_retweets:50` | Min retweets | `startup min_retweets:50` |
| `min_replies:10` | Min replies | `help min_replies:10` |

### Date Operators
| Operator | Description | Example |
|----------|-------------|---------|
| `since:2024-01-01` | After date | `AI since:2024-01-01` |
| `until:2024-12-31` | Before date | `trends until:2024-12-31` |
| `lang:en` | Language | `startup lang:en` |

## Troubleshooting

### Common Issues

1. **"At least one search term required"**
   - Provide searchTerms, twitterHandles, author, or mentioning
   - Cannot run empty searches

2. **"Invalid or missing Apify API token"**
   - Check that `APIFY_API_TOKEN` is set in environment variables
   - Verify the token is correct and active

3. **"API quota exceeded"**
   - Check your Apify account usage limits
   - Twitter scraping costs vary by plan and usage
   - Consider upgrading your plan if needed

4. **No results found**
   - Try broader search terms
   - Remove restrictive filters
   - Check if search terms are too specific

5. **Rate limiting errors**
   - Apify handles rate limiting automatically
   - Consider spacing out large scraping jobs

### Debug Tips

1. **Start Simple**: Begin with basic search terms
2. **Check Logs**: Review console output for detailed error messages
3. **Test Filters**: Try searches without filters first
4. **Verify Dates**: Ensure date formats are correct (YYYY-MM-DD)
5. **Monitor Costs**: Track usage in Apify dashboard

## Integration Notes

The Twitter scraper is designed to work alongside the Reddit and TikTok scrapers for comprehensive social media research. All three scrapers:
- Use the same Apify API token
- Follow consistent error handling patterns
- Provide formatted data for AI analysis
- Include comprehensive TypeScript types
- Support keyword-based market research

This enables cross-platform social media analysis and trend identification across Twitter, Reddit, and TikTok simultaneously. 