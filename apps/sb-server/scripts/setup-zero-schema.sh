#!/bin/bash

# Copy zero-schema files directly to src/lib for direct file imports
set -e

echo "🔨 Building zero-schema..."
cd ../../packages/zero-schema
pnpm run build

echo "📦 Copying zero-schema files to src/lib..."
cd ../../apps/sb-server

# Create lib directory for zero-schema files
rm -rf src/lib/zero-schema
mkdir -p src/lib/zero-schema

# Copy the built files directly to src/lib/zero-schema
cp ../../packages/zero-schema/dist/schema.js src/lib/zero-schema/schema.js
cp ../../packages/zero-schema/dist/schema.d.ts src/lib/zero-schema/schema.d.ts
cp ../../packages/zero-schema/dist/mutator.js src/lib/zero-schema/mutator.js
cp ../../packages/zero-schema/dist/mutator.d.ts src/lib/zero-schema/mutator.d.ts

echo "✅ Zero-schema files copied to src/lib/zero-schema"