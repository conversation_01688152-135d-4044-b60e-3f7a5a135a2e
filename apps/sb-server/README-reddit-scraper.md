# Reddit Scraping with Apify

This module provides Reddit scraping functionality using the Apify API for market research purposes.

## Setup

### 1. Install Dependencies

First, make sure you have installed the required dependencies:

```bash
npm install apify-client
```

### 2. Environment Variables

You'll need to set up the following environment variable in your `.env` file:

```env
# Apify API Token for Reddit scraping
APIFY_API_TOKEN=your_apify_token_here
```

You can get your Apify API token from:
1. Sign up at [Apify Console](https://console.apify.com/)
2. Go to Account → Integrations
3. Copy your API token

### 3. Test the Setup

Run the test script to verify everything is working:

```bash
npm run test:reddit
```

Or manually run:

```bash
tsx src/scripts/test-reddit-scraper.ts
```

## Usage

### Basic Reddit Scraping

```typescript
import { scrapeReddit } from './lib/services/scrape-reddit.js';

const results = await scrapeReddit({
  keywords: 'artificial intelligence marketing',
  timeFilter: 'month',
  includeComments: true,
  includeEngagement: true,
  maxItems: 50,
  maxPostCount: 20,
  maxComments: 10,
  sort: 'new'
});

console.log(`Found ${results.totalItems} items`);
console.log(`Posts: ${results.posts.length}`);
console.log(`Comments: ${results.comments.length}`);
```

### Integrated Market Research

The Reddit scraper is automatically integrated into the market research system. When you create research with `type: 'social-media-research'`, it will:

1. Extract keywords from the topic
2. Scrape Reddit for relevant posts and comments
3. Use AI to analyze the data
4. Return structured research insights

Example usage in the market research system:

```typescript
const research = await generateMarketResearch({
  icp_data: JSON.stringify(icpData),
  persona_data: JSON.stringify(personaData),
  type: 'social-media-research',
  timeFilter: 'Last 3 months',
  topic: 'Social media research for keywords: AI tools, marketing automation'
});
```

## API Reference

### `scrapeReddit(params: RedditScrapeParams)`

#### Parameters

- `keywords` (string): Keywords to search for on Reddit
- `timeFilter` (optional): Time period to filter results
  - Options: `'hour' | 'day' | 'week' | 'month' | 'year' | 'all'`
  - Default: `'month'`
- `includeComments` (optional): Whether to include comments in results
  - Default: `true`
- `includeEngagement` (optional): Whether to include engagement metrics
  - Default: `true`
- `maxItems` (optional): Maximum total items to return
  - Default: `50`
- `maxPostCount` (optional): Maximum posts per search
  - Default: `20`
- `maxComments` (optional): Maximum comments per post
  - Default: `10`
- `sort` (optional): Sort order for results
  - Options: `'relevance' | 'hot' | 'top' | 'new' | 'rising' | 'comments'`
  - Default: `'new'`

#### Returns

Returns a `RedditScrapeResponse` object containing:

- `posts`: Array of Reddit posts
- `comments`: Array of Reddit comments
- `communities`: Array of Reddit communities
- `users`: Array of Reddit users
- `totalItems`: Total number of items found
- `searchQuery`: The search query used
- `timeFilter`: The time filter applied
- `scrapedAt`: Timestamp of when data was scraped

### `formatRedditDataForAnalysis(data: RedditScrapeResponse)`

Formats Reddit data into a readable string format suitable for AI analysis.

## Data Structure

### Reddit Post

```typescript
interface RedditPost {
  id: string;
  parsedId: string;
  url: string;
  username: string;
  title: string;
  communityName: string;
  parsedCommunityName: string;
  body: string;
  numberOfComments: number;
  upVotes: number;
  isVideo: boolean;
  isAd: boolean;
  over18: boolean;
  createdAt: string;
  scrapedAt: string;
  dataType: 'post';
}
```

### Reddit Comment

```typescript
interface RedditComment {
  id: string;
  parsedId: string;
  url: string;
  parentId: string;
  username: string;
  category: string;
  communityName: string;
  body: string;
  createdAt: string;
  scrapedAt: string;
  upVotes: number;
  numberOfreplies: number;
  html: string;
  dataType: 'comment';
}
```

## Error Handling

The scraper includes comprehensive error handling:

- **Missing API Token**: Clear error message with setup instructions
- **API Quota Exceeded**: Specific error for quota limits
- **No Results Found**: Graceful handling when no data is found
- **Network Errors**: Proper error propagation with context

## Cost Considerations

- Reddit Scraper on Apify costs approximately $4 for 1,000 results
- Free tier includes $5 monthly credits
- For regular usage, consider the $49/month Starter plan

## Rate Limits and Best Practices

1. **Respect Rate Limits**: The scraper uses Apify's proxy system to handle rate limiting
2. **Reasonable Limits**: Set appropriate `maxItems` and `maxPostCount` to avoid long-running jobs
3. **Time Filters**: Use appropriate time filters to get relevant, recent data
4. **Keywords**: Use specific, relevant keywords for better results
5. **NSFW Content**: The scraper excludes NSFW content by default for business research

## Troubleshooting

### Common Issues

1. **"Invalid or missing Apify API token"**
   - Check that `APIFY_API_TOKEN` is set in your environment variables
   - Verify the token is correct and active

2. **"API quota exceeded"**
   - Check your Apify account usage limits
   - Consider upgrading your plan if needed

3. **No results found**
   - Try broader keywords
   - Adjust the time filter
   - Check if the keywords are too specific

4. **Slow performance**
   - Reduce `maxItems` and `maxPostCount`
   - Use shorter time filters
   - Consider running scraping jobs during off-peak hours

### Debug Mode

To enable debug mode for more detailed logging, you can modify the scraper parameters:

```typescript
const input = {
  // ... other params
  debugMode: true
};
```

## Integration with Frontend

The Reddit scraper is automatically integrated with the Social Research component (`socials-research.tsx`). When users:

1. Select Reddit as a platform
2. Enter keywords
3. Choose time filters
4. Click "Generate Social Research"

The system will:
1. Create a research entry in the database
2. Queue the Reddit scraping job
3. Process results with AI analysis
4. Update the research with findings

No additional frontend changes are needed to use the Reddit scraping functionality. 