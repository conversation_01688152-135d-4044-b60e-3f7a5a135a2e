import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

type Persona = {
  name: string;
  role: string;
  department?: string | null;
  management_level?: string | null;
  status: "Active" | "Inactive" | "Archived";
  industries?: string[] | null;
  company_size?: string | null;
  location?: string | null;
  tech_stack?: string[] | null;
  budget_range?: string | null;
  challenges?: string[] | null;
  goals?: string[] | null;
  decision_authority?: string | null;
  buying_stage?: string | null;
  info_preferences?: string[] | null;
  content_formats?: string[] | null;
  communication_style?: string | null;
  content_length?: string | null;
  channels?: string[] | null;
  topics?: string[] | null;

};

export async function generatePersona(icp_data: any, website = '', previous_persona_data = {} ): Promise<Persona> {
  try {

    console.log("params", icp_data);
   
    // Get production prompt 
    const prompt = await langfuse.getPrompt("generate_persona", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
      icp_data: icp_data
    });
    console.log(compiledPrompt);
    const response = await callLLM(compiledPrompt, prompt, "google/gemini-2.5-pro-preview");
    return response;
    
  } catch (error) {
    console.error('Error generating content schedule:', error);
    throw new Error('Failed to generate content schedule.');
  }
}
