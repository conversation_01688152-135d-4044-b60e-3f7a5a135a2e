import dotenv from 'dotenv';
import { ApifyClient } from 'apify-client';

dotenv.config();

// Initialize the ApifyClient with API token from environment
const client = new ApifyClient({
  token: process.env.APIFY_API_TOKEN,
});

export interface RedditScrapeParams {
  keywords: string;
  timeFilter?: 'hour' | 'day' | 'week' | 'month' | 'year' | 'all';
  includeComments?: boolean;
  includeEngagement?: boolean;
  platforms?: string[]; // For future expansion to other platforms
  maxItems?: number;
  maxPostCount?: number;
  maxComments?: number;
  sort?: 'relevance' | 'hot' | 'top' | 'new' | 'rising' | 'comments';
}

export interface RedditPost {
  id: string;
  parsedId: string;
  url: string;
  username: string;
  title: string;
  communityName: string;
  parsedCommunityName: string;
  body: string;
  html?: string;
  numberOfComments: number;
  upVotes: number;
  isVideo: boolean;
  isAd: boolean;
  over18: boolean;
  createdAt: string;
  scrapedAt: string;
  dataType: 'post';
}

export interface RedditComment {
  id: string;
  parsedId: string;
  url: string;
  parentId: string;
  username: string;
  category: string;
  communityName: string;
  body: string;
  createdAt: string;
  scrapedAt: string;
  upVotes: number;
  numberOfreplies: number;
  html: string;
  dataType: 'comment';
}

export interface RedditCommunity {
  id: string;
  name: string;
  title: string;
  headerImage: string;
  description: string;
  over18: boolean;
  createdAt: string;
  scrapedAt: string;
  numberOfMembers: number;
  url: string;
  dataType: 'community';
}

export interface RedditUser {
  id: string;
  url: string;
  username: string;
  userIcon: string;
  postKarma: number;
  commentKarma: number;
  description: string;
  over18: boolean;
  createdAt: string;
  scrapedAt: string;
  dataType: 'user';
}

export type RedditScrapedData = RedditPost | RedditComment | RedditCommunity | RedditUser;

export interface RedditScrapeResponse {
  posts: RedditPost[];
  comments: RedditComment[];
  communities: RedditCommunity[];
  users: RedditUser[];
  totalItems: number;
  searchQuery: string;
  timeFilter: string;
  scrapedAt: string;
}

/**
 * Maps time filter from the UI to Apify format
 */
function mapTimeFilter(timeFilter?: string): 'hour' | 'day' | 'week' | 'month' | 'year' | 'all' {
  const mapping: Record<string, 'hour' | 'day' | 'week' | 'month' | 'year' | 'all'> = {
    'Last 3 months': 'month', // Approximate to closest option
    'Last 6 months': 'year',  // Approximate to closest option
    'Last 12 months': 'year',
  };
  
  return mapping[timeFilter || 'Last 3 months'] || 'month';
}

/**
 * Scrapes Reddit data using Apify Reddit Scraper
 */
export async function scrapeReddit(params: RedditScrapeParams): Promise<RedditScrapeResponse> {
  const {
    keywords,
    timeFilter = 'Last 3 months',
    includeComments = true,
    includeEngagement = true,
    maxItems = 50,
    maxPostCount = 20,
    maxComments = 10,
    sort = 'new'
  } = params;

  try {
    // Validate required parameters
    if (!keywords || keywords.trim() === '') {
      throw new Error('Keywords are required for Reddit scraping');
    }

    // Prepare Actor input for Apify Reddit Scraper
    const input = {
      searches: [keywords.trim()],
      searchPosts: true,
      searchComments: includeComments,
      searchCommunities: false, // Focus on posts and comments for market research
      searchUsers: false,
      sort: sort,
      time: mapTimeFilter(timeFilter),
      includeNSFW: false, // Exclude NSFW content for business research
      maxItems: maxItems,
      maxPostCount: maxPostCount,
      maxComments: includeComments ? maxComments : 0,
      maxCommunitiesCount: 2,
      maxUserCount: 2,
      scrollTimeout: 40,
      skipComments: !includeComments,
      proxy: {
        useApifyProxy: true,
        apifyProxyGroups: ['RESIDENTIAL']
      },
      debugMode: false
    };

    console.log('Starting Reddit scrape with input:', JSON.stringify(input, null, 2));

    // Run the Actor and wait for it to finish
    const run = await client.actor('trudax/reddit-scraper').call(input);

    if (!run || !run.defaultDatasetId) {
      throw new Error('Failed to start Reddit scraper or no dataset returned');
    }

    // Fetch results from the run's dataset
    const { items } = await client.dataset(run.defaultDatasetId).listItems();

    if (!items || items.length === 0) {
      console.log('No items found for search query:', keywords);
      return {
        posts: [],
        comments: [],
        communities: [],
        users: [],
        totalItems: 0,
        searchQuery: keywords,
        timeFilter: timeFilter,
        scrapedAt: new Date().toISOString()
      };
    }

    console.log(`Reddit scraper found ${items.length} items`);

    // Process and categorize the results
    const posts: RedditPost[] = [];
    const comments: RedditComment[] = [];
    const communities: RedditCommunity[] = [];
    const users: RedditUser[] = [];

    items.forEach((item: any) => {
      try {
        switch (item.dataType) {
          case 'post':
            posts.push(item as RedditPost);
            break;
          case 'comment':
            comments.push(item as RedditComment);
            break;
          case 'community':
            communities.push(item as RedditCommunity);
            break;
          case 'user':
            users.push(item as RedditUser);
            break;
          default:
            console.warn('Unknown data type:', item.dataType);
        }
      } catch (error) {
        console.error('Error processing item:', error, item);
      }
    });

    const response: RedditScrapeResponse = {
      posts,
      comments,
      communities,
      users,
      totalItems: items.length,
      searchQuery: keywords,
      timeFilter: timeFilter,
      scrapedAt: new Date().toISOString()
    };

    console.log(`Reddit scrape completed: ${posts.length} posts, ${comments.length} comments`);
    
    return response;

  } catch (error) {
    console.error('Error scraping Reddit:', error);
    
    if (error instanceof Error) {
      // Handle specific Apify errors
      if (error.message.includes('token')) {
        throw new Error('Reddit scraping failed: Invalid or missing Apify API token. Please check your APIFY_API_TOKEN environment variable.');
      }
      if (error.message.includes('quota') || error.message.includes('limit')) {
        throw new Error('Reddit scraping failed: API quota exceeded. Please check your Apify account limits.');
      }
      throw new Error(`Reddit scraping failed: ${error.message}`);
    }
    
    throw new Error('Reddit scraping failed: Unknown error occurred');
  }
}

/**
 * Formats Reddit data for market research analysis
 */
export function formatRedditDataForAnalysis(data: RedditScrapeResponse): string {
  const { posts, comments, searchQuery, timeFilter } = data;
  
  let analysis = `Reddit Market Research Analysis for "${searchQuery}" (${timeFilter})\n`;
  analysis += `Generated on: ${data.scrapedAt}\n`;
  analysis += `Total Items: ${data.totalItems} (${posts.length} posts, ${comments.length} comments)\n\n`;

  if (posts.length > 0) {
    analysis += "=== TOP POSTS ===\n\n";
    posts.slice(0, 10).forEach((post, index) => {
      analysis += `${index + 1}. ${post.title}\n`;
      analysis += `   Community: ${post.communityName}\n`;
      analysis += `   Author: ${post.username}\n`;
      analysis += `   Engagement: ${post.upVotes} upvotes, ${post.numberOfComments} comments\n`;
      analysis += `   Created: ${post.createdAt}\n`;
      if (post.body && post.body.length > 0) {
        analysis += `   Content: ${post.body.substring(0, 200)}${post.body.length > 200 ? '...' : ''}\n`;
      }
      analysis += `   URL: ${post.url}\n\n`;
    });
  }

  if (comments.length > 0) {
    analysis += "=== TOP COMMENTS ===\n\n";
    comments.slice(0, 10).forEach((comment, index) => {
      analysis += `${index + 1}. ${comment.body.substring(0, 200)}${comment.body.length > 200 ? '...' : ''}\n`;
      analysis += `   Author: ${comment.username}\n`;
      analysis += `   Community: ${comment.communityName}\n`;
      analysis += `   Engagement: ${comment.upVotes} upvotes, ${comment.numberOfreplies} replies\n`;
      analysis += `   Created: ${comment.createdAt}\n`;
      analysis += `   URL: ${comment.url}\n\n`;
    });
  }

  return analysis;
}
