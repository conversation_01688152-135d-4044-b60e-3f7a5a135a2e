import dotenv from 'dotenv';
import { ApifyClient } from 'apify-client';

dotenv.config();

// Initialize the ApifyClient with API token from environment
const client = new ApifyClient({
  token: process.env.APIFY_API_TOKEN,
});

export interface TwitterScrapeParams {
  searchTerms?: string[];
  twitterHandles?: string[];
  maxItems?: number;
  tweetLanguage?: string;
  onlyVerifiedUsers?: boolean;
  onlyTwitterBlue?: boolean;
  onlyImage?: boolean;
  onlyVideo?: boolean;
  onlyQuote?: boolean;
  author?: string;
  mentioning?: string;
  minimumRetweets?: number;
  minimumFavorites?: number;
  minimumReplies?: number;
  start?: string; // Date in YYYY-MM-DD format
  end?: string; // Date in YYYY-MM-DD format
  sort?: 'Latest' | 'Top';
}

export interface TwitterUser {
  type: 'user';
  userName: string;
  url: string;
  twitterUrl: string;
  id: string;
  name: string;
  isVerified: boolean;
  isBlueVerified?: boolean;
  verifiedType?: string;
  hasNftAvatar: boolean;
  profilePicture: string;
  coverPicture?: string;
  description: string;
  location: string;
  followers: number;
  following: number;
  protected: boolean;
  canDm: boolean;
  createdAt: string;
  favouritesCount: number;
  statusesCount: number;
}

export interface TwitterTweet {
  type: 'tweet';
  id: string;
  url: string;
  twitterUrl: string;
  text: string;
  retweetCount: number;
  replyCount: number;
  likeCount: number;
  quoteCount: number;
  createdAt: string;
  lang: string;
  bookmarkCount?: number;
  isReply: boolean;
  isRetweet: boolean;
  isQuote: boolean;
  author: TwitterUser;
  hashtags?: string[];
  mentions?: string[];
  urls?: string[];
  media?: any[];
  quote?: TwitterTweet;
  scrapedAt: string;
  dataType: 'twitter_tweet';
}

export interface TwitterScrapeResponse {
  tweets: TwitterTweet[];
  users: TwitterUser[];
  totalItems: number;
  searchTerms?: string[];
  twitterHandles?: string[];
  scrapedAt: string;
  filters: {
    language?: string;
    verifiedOnly?: boolean;
    twitterBlueOnly?: boolean;
    mediaOnly?: boolean;
    dateRange?: { start?: string; end?: string };
    engagementFilters?: {
      minRetweets?: number;
      minFavorites?: number;
      minReplies?: number;
    };
  };
}

/**
 * Scrapes Twitter/X content using Apify Twitter Scraper
 */
export async function scrapeTwitter(params: TwitterScrapeParams = {}): Promise<TwitterScrapeResponse> {
  const {
    searchTerms = [],
    twitterHandles = [],
    maxItems = 100,
    tweetLanguage,
    onlyVerifiedUsers = false,
    onlyTwitterBlue = false,
    onlyImage = false,
    onlyVideo = false,
    onlyQuote = false,
    author,
    mentioning,
    minimumRetweets,
    minimumFavorites,
    minimumReplies,
    start,
    end,
    sort = 'Latest'
  } = params;

  try {
    // Validate input
    if (searchTerms.length === 0 && twitterHandles.length === 0 && !author && !mentioning) {
      throw new Error('At least one search term, Twitter handle, author, or mentioning parameter is required');
    }

    console.log('Starting Twitter scrape...');

    // Prepare Actor input for Apify Twitter Scraper
    const input: any = {
      maxItems: Math.min(maxItems, 1000), // Cap at 1000 to avoid excessive costs
      sort: sort
    };

    // Add search terms if provided
    if (searchTerms.length > 0) {
      input.searchTerms = searchTerms;
    }

    // Add Twitter handles if provided
    if (twitterHandles.length > 0) {
      input.twitterHandles = twitterHandles;
    }

    // Add optional filters
    if (tweetLanguage) input.tweetLanguage = tweetLanguage;
    if (onlyVerifiedUsers) input.onlyVerifiedUsers = true;
    if (onlyTwitterBlue) input.onlyTwitterBlue = true;
    if (onlyImage) input.onlyImage = true;
    if (onlyVideo) input.onlyVideo = true;
    if (onlyQuote) input.onlyQuote = true;
    if (author) input.author = author;
    if (mentioning) input.mentioning = mentioning;
    if (minimumRetweets !== undefined) input.minimumRetweets = minimumRetweets;
    if (minimumFavorites !== undefined) input.minimumFavorites = minimumFavorites;
    if (minimumReplies !== undefined) input.minimumReplies = minimumReplies;
    if (start) input.start = start;
    if (end) input.end = end;

    console.log('Twitter scrape input:', JSON.stringify(input, null, 2));

    // Run the Actor and wait for it to finish
    const run = await client.actor('nfp1fpt5gUlBwPcor').call(input);

    if (!run || !run.defaultDatasetId) {
      throw new Error('Failed to start Twitter scraper or no dataset returned');
    }

    // Fetch results from the run's dataset
    const { items } = await client.dataset(run.defaultDatasetId).listItems();

    if (!items || items.length === 0) {
      console.log('No Twitter content found');
      return {
        tweets: [],
        users: [],
        totalItems: 0,
        searchTerms: searchTerms,
        twitterHandles: twitterHandles,
        scrapedAt: new Date().toISOString(),
        filters: {
          language: tweetLanguage,
          verifiedOnly: onlyVerifiedUsers,
          twitterBlueOnly: onlyTwitterBlue,
          mediaOnly: onlyImage || onlyVideo,
          dateRange: { start, end },
          engagementFilters: {
            minRetweets: minimumRetweets,
            minFavorites: minimumFavorites,
            minReplies: minimumReplies
          }
        }
      };
    }

    console.log(`Twitter scraper found ${items.length} items`);

    // Process and normalize the results
    const tweets: TwitterTweet[] = [];
    const users: TwitterUser[] = [];

    // Add debug logging if environment variable is set
    if (process.env.DEBUG_TWITTER_STRUCTURE === 'true') {
      console.log('Sample item structure:', JSON.stringify(items[0], null, 2));
    }

    items.forEach((item: any, index: number) => {
      try {
        // Check the actual type field and also infer type from data structure
        const itemType = item.type || inferItemType(item);
        
        if (itemType === 'tweet') {
          const tweet = processTweet(item);
          tweets.push(tweet);
        } else if (itemType === 'user') {
          const user = processUser(item);
          users.push(user);
        } else {
          console.log(`Unknown item type: ${itemType}, item:`, JSON.stringify(item, null, 2));
        }
      } catch (error) {
        console.error('Error processing Twitter item:', error, item);
      }
    });

    const response: TwitterScrapeResponse = {
      tweets,
      users,
      totalItems: tweets.length + users.length,
      searchTerms: searchTerms.length > 0 ? searchTerms : undefined,
      twitterHandles: twitterHandles.length > 0 ? twitterHandles : undefined,
      scrapedAt: new Date().toISOString(),
      filters: {
        language: tweetLanguage,
        verifiedOnly: onlyVerifiedUsers,
        twitterBlueOnly: onlyTwitterBlue,
        mediaOnly: onlyImage || onlyVideo,
        dateRange: { start, end },
        engagementFilters: {
          minRetweets: minimumRetweets,
          minFavorites: minimumFavorites,
          minReplies: minimumReplies
        }
      }
    };

    console.log(`Twitter scrape completed: ${tweets.length} tweets, ${users.length} users`);
    return response;

  } catch (error) {
    console.error('Error scraping Twitter:', error);
    
    if (error instanceof Error) {
      // Handle specific Apify errors
      if (error.message.includes('token')) {
        throw new Error('Twitter scraping failed: Invalid or missing Apify API token. Please check your APIFY_API_TOKEN environment variable.');
      }
      if (error.message.includes('quota') || error.message.includes('limit')) {
        throw new Error('Twitter scraping failed: API quota exceeded. Please check your Apify account limits.');
      }
      throw new Error(`Twitter scraping failed: ${error.message}`);
    }
    
    throw new Error('Twitter scraping failed: Unknown error occurred');
  }
}

/**
 * Infer the type of item based on its structure
 */
function inferItemType(item: any): 'tweet' | 'user' | 'unknown' {
  // If it has text/tweet content fields, it's likely a tweet
  if (item.text || item.fullText || item.content || item.retweetCount !== undefined || item.likeCount !== undefined) {
    return 'tweet';
  }
  
  // If it has user profile fields, it's likely a user
  if (item.userName || item.screenName || item.followers !== undefined || item.description !== undefined) {
    return 'user';
  }
  
  // Default fallback - assume tweet if we can't determine
  return 'tweet';
}

/**
 * Process and normalize a tweet from the API response
 */
function processTweet(item: any): TwitterTweet {
  // Handle various text field names
  const text = item.text || item.fullText || item.content || '';
  
  // Handle various URL field names
  const url = item.url || item.twitterUrl || `https://twitter.com/i/status/${item.id}`;
  
  // Create a safe author object
  const author = item.author || {
    type: 'user',
    userName: item.userName || item.screenName || 'unknown',
    url: item.authorUrl || `https://twitter.com/${item.userName || item.screenName || 'unknown'}`,
    twitterUrl: item.authorUrl || `https://twitter.com/${item.userName || item.screenName || 'unknown'}`,
    id: item.authorId || item.userId || 'unknown',
    name: item.authorName || item.displayName || item.userName || 'Unknown User',
    isVerified: item.isVerified || item.verified || false,
    isBlueVerified: item.isBlueVerified || false,
    hasNftAvatar: item.hasNftAvatar || false,
    profilePicture: item.profilePicture || item.profileImageUrl || '',
    description: item.authorDescription || item.bio || '',
    location: item.location || '',
    followers: item.followersCount || item.followers || 0,
    following: item.followingCount || item.following || 0,
    protected: item.protected || false,
    canDm: item.canDm || false,
    createdAt: item.authorCreatedAt || item.createdAt || '',
    favouritesCount: item.favouritesCount || 0,
    statusesCount: item.statusesCount || 0
  };

  const tweet: TwitterTweet = {
    type: 'tweet',
    id: item.id || item.tweetId || 'unknown',
    url: url,
    twitterUrl: url,
    text: text,
    retweetCount: item.retweetCount || item.retweets || 0,
    replyCount: item.replyCount || item.replies || 0,
    likeCount: item.likeCount || item.likes || item.favoriteCount || 0,
    quoteCount: item.quoteCount || item.quotes || 0,
    createdAt: item.createdAt || new Date().toISOString(),
    lang: item.lang || item.language || 'en',
    bookmarkCount: item.bookmarkCount || 0,
    isReply: item.isReply || false,
    isRetweet: item.isRetweet || false,
    isQuote: item.isQuote || false,
    author: author,
    hashtags: extractHashtagsFromText(text),
    mentions: extractMentionsFromText(text),
    urls: extractUrlsFromText(text),
    media: item.media || item.attachments || [],
    quote: item.quote,
    scrapedAt: new Date().toISOString(),
    dataType: 'twitter_tweet'
  };

  return tweet;
}

/**
 * Process and normalize a user from the API response
 */
function processUser(item: any): TwitterUser {
  const user: TwitterUser = {
    type: 'user',
    userName: item.userName,
    url: item.url || item.twitterUrl,
    twitterUrl: item.twitterUrl || item.url,
    id: item.id,
    name: item.name,
    isVerified: item.isVerified || false,
    isBlueVerified: item.isBlueVerified,
    verifiedType: item.verifiedType,
    hasNftAvatar: item.hasNftAvatar || false,
    profilePicture: item.profilePicture || '',
    coverPicture: item.coverPicture,
    description: item.description || '',
    location: item.location || '',
    followers: item.followers || 0,
    following: item.following || 0,
    protected: item.protected || false,
    canDm: item.canDm || false,
    createdAt: item.createdAt,
    favouritesCount: item.favouritesCount || 0,
    statusesCount: item.statusesCount || 0
  };

  return user;
}

/**
 * Extract hashtags from tweet text
 */
function extractHashtagsFromText(text: string): string[] {
  const hashtags = text.match(/#[\w]+/g);
  return hashtags ? hashtags.map(tag => tag.substring(1)) : [];
}

/**
 * Extract mentions from tweet text
 */
function extractMentionsFromText(text: string): string[] {
  const mentions = text.match(/@[\w]+/g);
  return mentions ? mentions.map(mention => mention.substring(1)) : [];
}

/**
 * Extract URLs from tweet text
 */
function extractUrlsFromText(text: string): string[] {
  const urlPattern = /https?:\/\/[^\s]+/g;
  const urls = text.match(urlPattern);
  return urls || [];
}

/**
 * Formats Twitter data for market research analysis
 */
export function formatTwitterDataForAnalysis(data: TwitterScrapeResponse): string {
  const { tweets, users, searchTerms, twitterHandles, filters } = data;
  
  let analysis = `Twitter/X Market Research Analysis\n`;
  
  if (searchTerms && searchTerms.length > 0) {
    analysis += `Search Terms: ${searchTerms.join(', ')}\n`;
  }
  if (twitterHandles && twitterHandles.length > 0) {
    analysis += `Twitter Handles: ${twitterHandles.join(', ')}\n`;
  }
  
  analysis += `Generated on: ${data.scrapedAt}\n`;
  analysis += `Total Items: ${data.totalItems} (${tweets.length} tweets, ${users.length} users)\n`;
  
  // Add filter information
  if (filters.language) analysis += `Language Filter: ${filters.language}\n`;
  if (filters.verifiedOnly) analysis += `Verified Users Only: Yes\n`;
  if (filters.twitterBlueOnly) analysis += `Twitter Blue Only: Yes\n`;
  if (filters.mediaOnly) analysis += `Media Content Only: Yes\n`;
  if (filters.dateRange?.start || filters.dateRange?.end) {
    analysis += `Date Range: ${filters.dateRange.start || 'earliest'} to ${filters.dateRange.end || 'latest'}\n`;
  }
  
  analysis += '\n';

  if (tweets.length > 0) {
    analysis += "=== TOP TWEETS ===\n\n";
    
    // Sort tweets by engagement (likes + retweets + replies)
    const sortedTweets = tweets
      .map(tweet => ({
        ...tweet,
        totalEngagement: tweet.likeCount + tweet.retweetCount + tweet.replyCount
      }))
      .sort((a, b) => b.totalEngagement - a.totalEngagement);

    sortedTweets.slice(0, 15).forEach((tweet, index) => {
      analysis += `${index + 1}. ${tweet.text.substring(0, 200)}${tweet.text.length > 200 ? '...' : ''}\n`;
      analysis += `   Author: @${tweet.author.userName} (${tweet.author.name})\n`;
      
      // Verification status
      const verificationStatus = [];
      if (tweet.author.isVerified) verificationStatus.push('verified');
      if (tweet.author.isBlueVerified) verificationStatus.push('blue verified');
      if (verificationStatus.length > 0) {
        analysis += `   Status: ${verificationStatus.join(', ')}\n`;
      }
      
      // Engagement metrics
      const engagement = [];
      if (tweet.likeCount > 0) engagement.push(`${formatEngagementNumber(tweet.likeCount)} likes`);
      if (tweet.retweetCount > 0) engagement.push(`${formatEngagementNumber(tweet.retweetCount)} retweets`);
      if (tweet.replyCount > 0) engagement.push(`${formatEngagementNumber(tweet.replyCount)} replies`);
      if (tweet.quoteCount > 0) engagement.push(`${formatEngagementNumber(tweet.quoteCount)} quotes`);
      
      if (engagement.length > 0) {
        analysis += `   Engagement: ${engagement.join(', ')}\n`;
      }
      
      if (tweet.hashtags && tweet.hashtags.length > 0) {
        analysis += `   Hashtags: ${tweet.hashtags.slice(0, 5).map(tag => '#' + tag).join(', ')}\n`;
      }
      
      if (tweet.mentions && tweet.mentions.length > 0) {
        analysis += `   Mentions: ${tweet.mentions.slice(0, 3).map(mention => '@' + mention).join(', ')}\n`;
      }
      
      analysis += `   Posted: ${tweet.createdAt}\n`;
      analysis += `   URL: ${tweet.url}\n\n`;
    });

    // Hashtag analysis
    const allHashtags = tweets.flatMap(tweet => tweet.hashtags || []);
    const hashtagCounts = allHashtags.reduce((acc, hashtag) => {
      acc[hashtag] = (acc[hashtag] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const topHashtags = Object.entries(hashtagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 15);
      
    if (topHashtags.length > 0) {
      analysis += "=== TRENDING HASHTAGS ===\n\n";
      topHashtags.forEach(([hashtag, count], index) => {
        analysis += `${index + 1}. #${hashtag} (${count} tweets)\n`;
      });
      analysis += '\n';
    }

    // Top mentions analysis
    const allMentions = tweets.flatMap(tweet => tweet.mentions || []);
    const mentionCounts = allMentions.reduce((acc, mention) => {
      acc[mention] = (acc[mention] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const topMentions = Object.entries(mentionCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
      
    if (topMentions.length > 0) {
      analysis += "=== TOP MENTIONS ===\n\n";
      topMentions.forEach(([mention, count], index) => {
        analysis += `${index + 1}. @${mention} (${count} mentions)\n`;
      });
      analysis += '\n';
    }
  }

  if (users.length > 0) {
    analysis += "=== TOP USERS ===\n\n";
    
    // Sort users by follower count
    const sortedUsers = users.sort((a, b) => b.followers - a.followers);
    
    sortedUsers.slice(0, 10).forEach((user, index) => {
      analysis += `${index + 1}. @${user.userName} (${user.name})\n`;
      analysis += `   Followers: ${formatEngagementNumber(user.followers)}\n`;
      analysis += `   Following: ${formatEngagementNumber(user.following)}\n`;
      
      if (user.isVerified || user.isBlueVerified) {
        const verificationStatus = [];
        if (user.isVerified) verificationStatus.push('verified');
        if (user.isBlueVerified) verificationStatus.push('blue verified');
        analysis += `   Status: ${verificationStatus.join(', ')}\n`;
      }
      
      if (user.description) {
        analysis += `   Bio: ${user.description.substring(0, 100)}${user.description.length > 100 ? '...' : ''}\n`;
      }
      
      if (user.location) {
        analysis += `   Location: ${user.location}\n`;
      }
      
      analysis += `   Joined: ${user.createdAt}\n`;
      analysis += `   URL: ${user.url}\n\n`;
    });
  }

  return analysis;
}

/**
 * Format engagement numbers for display (e.g., 1000 -> 1K)
 */
function formatEngagementNumber(num: number): string {
  if (num >= 1000000) {
    return Math.round(num / 1000000 * 10) / 10 + 'M';
  }
  if (num >= 1000) {
    return Math.round(num / 1000 * 10) / 10 + 'K';
  }
  return num.toString();
}

/**
 * Build advanced Twitter search query using provided parameters
 */
export function buildAdvancedTwitterQuery(params: {
  keywords?: string[];
  fromUser?: string;
  toUser?: string;
  mentioning?: string;
  hashtags?: string[];
  minLikes?: number;
  minRetweets?: number;
  minReplies?: number;
  since?: string;
  until?: string;
  lang?: string;
  hasMedia?: boolean;
  hasLinks?: boolean;
  verified?: boolean;
}): string {
  const queryParts: string[] = [];

  // Add keywords
  if (params.keywords && params.keywords.length > 0) {
    queryParts.push(params.keywords.join(' '));
  }

  // Add user filters
  if (params.fromUser) queryParts.push(`from:${params.fromUser}`);
  if (params.toUser) queryParts.push(`to:${params.toUser}`);
  if (params.mentioning) queryParts.push(`@${params.mentioning}`);

  // Add hashtags
  if (params.hashtags && params.hashtags.length > 0) {
    queryParts.push(params.hashtags.map(tag => `#${tag}`).join(' OR '));
  }

  // Add engagement filters
  if (params.minLikes) queryParts.push(`min_faves:${params.minLikes}`);
  if (params.minRetweets) queryParts.push(`min_retweets:${params.minRetweets}`);
  if (params.minReplies) queryParts.push(`min_replies:${params.minReplies}`);

  // Add date filters
  if (params.since) queryParts.push(`since:${params.since}`);
  if (params.until) queryParts.push(`until:${params.until}`);

  // Add content filters
  if (params.lang) queryParts.push(`lang:${params.lang}`);
  if (params.hasMedia) queryParts.push('filter:media');
  if (params.hasLinks) queryParts.push('filter:links');
  if (params.verified) queryParts.push('filter:verified');

  return queryParts.join(' ');
}
