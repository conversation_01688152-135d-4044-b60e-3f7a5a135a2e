
// curl -X POST https://api.firecrawl.dev/v1/crawl \
//     -H 'Content-Type: application/json' \
//     -H 'Authorization: Bearer YOUR_API_KEY' \
//     -d '{
//       "url": "https://docs.firecrawl.dev",
//       "limit": 100,
//       "scrapeOptions": {
//         "formats": ["markdown", "html"]
//       }
//     }'

export async function crawlWebsite(url: string, limit: number = 100, formats: string[] = ['markdown']) {
  const response = await fetch('https://api.firecrawl.dev/v1/crawl', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
    },
    body: JSON.stringify({ url, limit, scrapeOptions: { formats } })
  });

  const data = await response.json();
  console.log(data);

  if (!response.ok) {
    throw new Error(`Failed to crawl website: ${response.statusText}`);
  }

  return data;
}

export async function getCrawlStatus(crawlId: string) {
    const response = await fetch(`https://api.firecrawl.dev/v1/crawl/${crawlId}`, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
        }
    });

    const data = await response.json();
    console.log(data);

    return data;
}