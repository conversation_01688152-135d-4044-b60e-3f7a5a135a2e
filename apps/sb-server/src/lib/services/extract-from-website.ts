
// curl -X POST https://api.firecrawl.dev/v1/crawl \
//     -H 'Content-Type: application/json' \
//     -H 'Authorization: Bearer YOUR_API_KEY' \
//     -d '{
//       "url": "https://docs.firecrawl.dev",
//       "limit": 100,
//       "scrapeOptions": {
//         "formats": ["markdown", "html"]
//       }
//     }'

export interface SchemaItem {
  type: string;
  properties: {
    [key: string]: { type: string };
  };
}

export async function extractFromWebsite(urls: string[], prompt: string, schema: SchemaItem[], enableWebSearch: boolean = false) {
  const properties = {
    ...schema
  };
  console.log('properties', JSON.stringify(properties, null, 2));
  const response = await fetch('https://api.firecrawl.dev/v1/extract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
    },
    body: JSON.stringify({ 
      urls, 
      prompt, 
      schema : {
        ...properties
      },
      enableWebSearch
    })
  });

  const data = await response.json();
  console.log(data);

  if (!response.ok) {
    throw new Error(`Failed to crawl website: ${response.statusText}`);
  }

  return data;
}

export async function getExtractStatus(extractId: string) {
    const response = await fetch(`https://api.firecrawl.dev/v1/extract/${extractId}`, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
        }
    });

    const data = await response.json();
    console.log(data);

    return data;
}