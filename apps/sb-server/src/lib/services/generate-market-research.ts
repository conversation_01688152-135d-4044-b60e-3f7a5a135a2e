import dotenv from "dotenv";
import axios from 'axios';
import { callLLM } from '../utils/callLLM.js';
import { Langfuse } from "langfuse";
import { scrapeReddit, formatRedditDataForAnalysis, type RedditScrapeParams } from './scrape-reddit.js';
import { scrapeTikTok, formatTikTokDataForAnalysis, type TikTokScrapeParams } from './scrape-tiktok.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

type ResearchItem = {
  title: string;
  description: string;
  source?: string;
  source_url?: string;
  relevance_score?: number;
};

type ContentSuggestion = {
  topic: string;
  description: string;
  content_type?: string;
  target_audience?: string;
};

type MarketResearchResponse = {
  research: ResearchItem[];
  content_suggestions: ContentSuggestion[];
};

type MarketResearchParams = {
  icp_data: string; // Combined ICP and persona data
  persona_data: string; // Combined ICP and persona data
  type: string; // research type (pain-points, trending-topics, recent-news)
  timeFilter: string; // time period
  topic?: string; // optional topic
};

export async function generateMarketResearch(params: MarketResearchParams): Promise<MarketResearchResponse> {
  const { icp_data, persona_data, type, timeFilter, topic } = params;

  try {
    // Handle social media research differently
    if (type === 'social-media-research') {
      return await generateSocialMediaResearch(params);
    }

    // Original research generation for other types
    const prompt = await langfuse.getPrompt("generate_market_research", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
      icp_data,
      persona_information: persona_data,
      timeFilter,
      topic,
      type,
      additionalParams : JSON.stringify({
        web_search_options: {
          search_context_size: "high"
        }
      })
    });
    
    console.log("compiledPrompt", compiledPrompt);
    const response = await callLLM(compiledPrompt, prompt, "perplexity/sonar-deep-research");
    return response;

  } catch (error) {
    console.error('Error generating market research:', error);
    
    // Handle axios errors specifically
    if (axios.isAxiosError(error)) {
      throw new Error(`Perplexity API request failed: ${error.response?.data || error.message}`);
    } else {
      throw new Error(`Market research generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Generates social media research using Reddit scraping
 */
async function generateSocialMediaResearch(params: MarketResearchParams): Promise<MarketResearchResponse> {
  const { icp_data, persona_data, type, timeFilter, topic } = params;

  try {
    // Extract keywords from the topic - topic should contain keywords to search for
    if (!topic || topic.trim() === '') {
      throw new Error('Topic/keywords are required for social media research');
    }

    // Extract keywords from topic string (e.g., "Social media research on Twitter/X, LinkedIn for keywords: AI tools, marketing automation")
    const keywordsMatch = topic.match(/keywords?:\s*(.+)$/i);
    const keywords = keywordsMatch ? keywordsMatch[1].trim() : topic;

    console.log('Starting Reddit scraping for keywords:', keywords);

    // Map timeFilter to Reddit scraper format
    const mapTimeFilterForReddit = (filter: string): 'hour' | 'day' | 'week' | 'month' | 'year' | 'all' => {
      const mapping: Record<string, 'hour' | 'day' | 'week' | 'month' | 'year' | 'all'> = {
        'Last 3 months': 'month',
        'Last 6 months': 'year',
        'Last 12 months': 'year',
      };
      return mapping[filter] || 'month';
    };

    // Prepare Reddit scraping parameters
    const redditParams: RedditScrapeParams = {
      keywords: keywords,
      timeFilter: mapTimeFilterForReddit(timeFilter),
      includeComments: true,
      includeEngagement: true,
      maxItems: 100,
      maxPostCount: 50,
      maxComments: 20,
      sort: 'new'
    };

    // Scrape Reddit data
    const redditData = await scrapeReddit(redditParams);
    
    if (redditData.totalItems === 0) {
      console.log('No Reddit data found for keywords:', keywords);
      return {
        research: [{
          title: 'No Social Media Data Found',
          description: `No relevant discussions found on Reddit for keywords "${keywords}" in the specified time period.`,
          source: 'Reddit',
          relevance_score: 0
        }],
        content_suggestions: [{
          topic: 'Start the Conversation',
          description: `Consider creating content to initiate discussions around "${keywords}" as there appears to be limited existing conversation on this topic.`,
          content_type: 'Discussion Post',
          target_audience: 'Reddit Communities'
        }]
      };
    }

    // Format Reddit data for LLM analysis
    const formattedData = formatRedditDataForAnalysis(redditData);
    
    console.log('Reddit data scraped successfully, analyzing with LLM...');

    // Use LLM to analyze Reddit data and generate insights
    const prompt = await langfuse.getPrompt("analyze_social_media_research", undefined, { label: "latest" });
    const compiledPrompt = prompt.compile({
      icp_data,
      persona_information: persona_data,
      social_media_data: formattedData,
      keywords: keywords,
      timeFilter: timeFilter,
      platform: 'Reddit'
    });

    console.log("Social media analysis prompt compiled");
    const analysis = await callLLM(compiledPrompt, prompt, "google/gemini-2.5-pro-preview");

    // Transform raw Reddit data into research items
    const researchItems: ResearchItem[] = [];
    
    // Add top posts as research items
    redditData.posts.slice(0, 10).forEach((post, index) => {
      researchItems.push({
        title: post.title,
        description: post.body || 'No description available',
        source: `Reddit - ${post.communityName}`,
        source_url: post.url,
        relevance_score: Math.max(0.1, Math.min(1.0, (post.upVotes + post.numberOfComments) / 100))
      });
    });

    // Add top comments as research items
    redditData.comments.slice(0, 5).forEach((comment, index) => {
      researchItems.push({
        title: `Comment: ${comment.body.substring(0, 100)}...`,
        description: comment.body,
        source: `Reddit - ${comment.communityName}`,
        source_url: comment.url,
        relevance_score: Math.max(0.1, Math.min(1.0, comment.upVotes / 50))
      });
    });

    // Combine LLM analysis with raw data
    const result: MarketResearchResponse = {
      research: [
        {
          title: 'Reddit Social Media Analysis',
          description: typeof analysis === 'string' ? analysis : JSON.stringify(analysis),
          source: 'Reddit Analysis',
          relevance_score: 1.0
        },
        ...researchItems
      ],
      content_suggestions: analysis?.content_suggestions || [
        {
          topic: 'Engage with Reddit Communities',
          description: `Join discussions in relevant subreddits where your target audience is active. Based on the analysis, focus on ${keywords}-related topics.`,
          content_type: 'Community Engagement',
          target_audience: 'Reddit Users'
        }
      ]
    };

    console.log('Social media research completed successfully');
    return result;

  } catch (error) {
    console.error('Error in social media research:', error);
    
    if (error instanceof Error) {
      throw new Error(`Social media research failed: ${error.message}`);
    }
    throw new Error('Social media research failed: Unknown error occurred');
  }
}
