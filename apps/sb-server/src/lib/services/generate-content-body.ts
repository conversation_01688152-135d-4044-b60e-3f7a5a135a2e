import dotenv from "dotenv";
import { Langfuse } from "langfuse";
import { callLLM } from "../utils/callLLM.js";

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL,
});

export const generateContentBody = async (
  channel: string,
  content_type: string,
  task_description: string,
  personas_block: string | object,
  icps_block: string | object,
  research_block: string | object,
  documents_block: string | object,
  seo_keywords: string,
  trend_keywords: string,
  brand_guidelines: string | object
): Promise<string> => {
  console.log(
    "channel",
    channel,
    "content_type",
    content_type,
    "task_description",
    task_description,
    "personas_block",
    personas_block,
    "icps_block",
    icps_block,
    "research_block",
    research_block,
    "documents_block",
    documents_block,
    "seo_keywords",
    seo_keywords,
    "trend_keywords",
    trend_keywords,
    "brand_guidelines",
    brand_guidelines
  );
  let response1: string | undefined = undefined;
  try {
    // Get production prompt
    const prompt: any = await langfuse.getPrompt(
      "generate_content_body_v2",
      undefined,
      { label: "production" }
    );
    console.log("Full prompt object:", JSON.stringify(prompt, null, 2));
    console.log("prompt.config exists:", !!prompt.config);
    console.log("prompt.config:", prompt.config);

    // Helper function to stringify objects
    const stringify = (value: any): string => {
      if (typeof value === "object" && value !== null) {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    };

    const compiledMessages = prompt.compile({
      channel: channel,
      content_type: content_type,
      task_description: task_description,
      personas_block: stringify(personas_block),
      icps_block: stringify(icps_block),
      research_block: stringify(research_block),
      documents_block: stringify(documents_block),
      seo_keywords: seo_keywords,
      trend_keywords: trend_keywords,
      brand_guidelines: stringify(brand_guidelines),
    });
    console.log("prompt object:", prompt);
    console.log("compiledMessages:", compiledMessages);

    // For chat prompts, pass empty string as prompt and use additionalMessages
    const response = await callLLM(
      "",
      prompt.config, // Use the config portion of the prompt
      "google/gemini-2.5-pro-preview",
      { parse: false },
      compiledMessages
    );
    return response;
  } catch (error) {
    console.error("Error generating visual description:", error);
    throw error;
  }
};
