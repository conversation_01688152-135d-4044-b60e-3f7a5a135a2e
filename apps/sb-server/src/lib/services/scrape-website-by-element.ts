import { JigsawStack } from "jigsawstack";
import { structureScrapeResults } from "./structure-scrape-results.js";

const jigsawstack = JigsawStack({
  apiKey: process.env.JIGSAW_API_KEY
});

export async function scrapeWebsiteByElement(url: string, element_prompts: string[], json_output: Object = {}, instructions: string = '') {

  try {
    const result = await jigsawstack.web.ai_scrape({
      url: url,
      element_prompts
    });
    console.log("result", result);
    const structure_result = await structureScrapeResults(result, json_output, instructions);
    return structure_result;
  } catch (error) {
    console.error('Error in scrapeWebsiteByElement:', error);
    throw new Error('Failed to scrape website by element.');
  }
}