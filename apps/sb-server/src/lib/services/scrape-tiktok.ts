import dotenv from 'dotenv';
import { ApifyClient } from 'apify-client';

dotenv.config();

// Initialize the ApifyClient with API token from environment
const client = new ApifyClient({
  token: process.env.APIFY_API_TOKEN,
});

export interface TikTokScrapeParams {
  // Content to scrape
  hashtags?: string[];
  profiles?: string[];
  searchQueries?: string[];
  postURLs?: string[];
  
  // Results configuration  
  resultsPerPage?: number;
  maxProfilesPerQuery?: number;
  
  // Profile options
  profileScrapeSections?: ('videos' | 'reposts')[];
  profileSorting?: 'latest' | 'popular' | 'oldest';
  profileConnectionTypes?: ('followers' | 'following')[];
  
  // Date filters
  oldestPostDateUnified?: string; // YYYY-MM-DD or days as number
  newestPostDate?: string; // YYYY-MM-DD
  
  // Engagement filters
  leastDiggs?: number; // Minimum likes
  mostDiggs?: number; // Maximum likes
  excludePinnedPosts?: boolean;
  
  // Search options
  searchSection?: '' | '/video' | '/user'; // Top, Videos, or Users
  scrapeRelatedVideos?: boolean;
  
  // Download options
  shouldDownloadVideos?: boolean;
  shouldDownloadCovers?: boolean;
  shouldDownloadSubtitles?: boolean;
  shouldDownloadSlideshowImages?: boolean;
  shouldDownloadAvatars?: boolean;
  shouldDownloadMusicCovers?: boolean;
  
  // Storage and proxy
  videoKvStoreIdOrName?: string;
  proxyCountryCode?: string; // Country code like 'US', 'GB', etc.
}

// Raw TikTok data as returned by the API - no transformation
export type TikTokRawItem = any;

export interface TikTokScrapeResponse {
  items: TikTokRawItem[];
  totalItems: number;
  scrapedAt: string;
  scrapeType: string;
  params: TikTokScrapeParams;
}

/**
 * Scrapes TikTok content using Apify's advanced TikTok Scraper
 */
export async function scrapeTikTok(params: TikTokScrapeParams = {}): Promise<TikTokScrapeResponse> {
  try {
    console.log('Starting TikTok scrape with params:', JSON.stringify(params, null, 2));

    // Build the actor input based on the new scraper's API
    const input: any = {
      resultsPerPage: params.resultsPerPage || 20,
      proxyCountryCode: params.proxyCountryCode || 'None',
    };

    // Add content sources
    if (params.hashtags && params.hashtags.length > 0) {
      input.hashtags = params.hashtags;
    }
    
    if (params.profiles && params.profiles.length > 0) {
      input.profiles = params.profiles;
    }
    
    if (params.searchQueries && params.searchQueries.length > 0) {
      input.searchQueries = params.searchQueries;
    }
    
    if (params.postURLs && params.postURLs.length > 0) {
      input.postURLs = params.postURLs;
    }

    // Profile options
    if (params.profileScrapeSections) {
      input.profileScrapeSections = params.profileScrapeSections;
    }
    if (params.profileSorting) {
      input.profileSorting = params.profileSorting;
    }
    if (params.profileConnectionTypes) {
      input.profileConnectionTypes = params.profileConnectionTypes;
    }

    // Date and engagement filters
    if (params.oldestPostDateUnified) {
      input.oldestPostDateUnified = params.oldestPostDateUnified;
    }
    if (params.newestPostDate) {
      input.newestPostDate = params.newestPostDate;
    }
    if (params.leastDiggs) {
      input.leastDiggs = params.leastDiggs;
    }
    if (params.mostDiggs) {
      input.mostDiggs = params.mostDiggs;
    }
    if (params.excludePinnedPosts !== undefined) {
      input.excludePinnedPosts = params.excludePinnedPosts;
    }

    // Search options
    if (params.searchSection !== undefined) {
      input.searchSection = params.searchSection;
    }
    if (params.maxProfilesPerQuery) {
      input.maxProfilesPerQuery = params.maxProfilesPerQuery;
    }
    if (params.scrapeRelatedVideos !== undefined) {
      input.scrapeRelatedVideos = params.scrapeRelatedVideos;
    }

    // Download options - default to false to save costs
    input.shouldDownloadVideos = params.shouldDownloadVideos || false;
    input.shouldDownloadCovers = params.shouldDownloadCovers || false;
    input.shouldDownloadSubtitles = params.shouldDownloadSubtitles || false;
    input.shouldDownloadSlideshowImages = params.shouldDownloadSlideshowImages || false;
    input.shouldDownloadAvatars = params.shouldDownloadAvatars || false;
    input.shouldDownloadMusicCovers = params.shouldDownloadMusicCovers || false;

    // Storage options
    if (params.videoKvStoreIdOrName) {
      input.videoKvStoreIdOrName = params.videoKvStoreIdOrName;
    }

    // Set defaults if no content source is specified - scrape trending hashtag
    if (!input.hashtags && !input.profiles && !input.searchQueries && !input.postURLs) {
      input.hashtags = ['fyp']; // Default to "For You Page" hashtag
      console.log('No content source specified, defaulting to #fyp hashtag');
    }

    console.log('TikTok scrape input:', JSON.stringify(input, null, 2));

    // Run the Actor and wait for it to finish
    const run = await client.actor('clockworks/tiktok-scraper').call(input);

    if (!run || !run.defaultDatasetId) {
      throw new Error('Failed to start TikTok scraper or no dataset returned');
    }

    // Fetch results from the run's dataset
    const { items } = await client.dataset(run.defaultDatasetId).listItems();

    if (!items || items.length === 0) {
      console.log('No TikTok content found');
      return {
        items: [],
        totalItems: 0,
        scrapedAt: new Date().toISOString(),
        scrapeType: getScrapeType(params),
        params: params
      };
    }

    console.log(`TikTok scraper found ${items.length} items`);
    console.log(items);
    
    // Return raw items without transformation
    const response: TikTokScrapeResponse = {
      items: items,
      totalItems: items.length,
      scrapedAt: new Date().toISOString(),
      scrapeType: getScrapeType(params),
      params: params
    };

    console.log(`TikTok scrape completed: ${items.length} items found`);
    return response;

  } catch (error) {
    console.error('Error scraping TikTok:', error);
    
    if (error instanceof Error) {
      // Handle specific Apify errors
      if (error.message.includes('token')) {
        throw new Error('TikTok scraping failed: Invalid or missing Apify API token. Please check your APIFY_API_TOKEN environment variable.');
      }
      if (error.message.includes('quota') || error.message.includes('limit')) {
        throw new Error('TikTok scraping failed: API quota exceeded. Please check your Apify account limits.');
      }
      throw new Error(`TikTok scraping failed: ${error.message}`);
    }
    
    throw new Error('TikTok scraping failed: Unknown error occurred');
  }
}

/**
 * Helper function to determine scrape type for logging
 */
function getScrapeType(params: TikTokScrapeParams): string {
  if (params.hashtags?.length) return 'hashtags';
  if (params.profiles?.length) return 'profiles';
  if (params.searchQueries?.length) return 'search';
  if (params.postURLs?.length) return 'direct_urls';
  return 'default';
}

/**
 * Basic formatter for TikTok raw data - you can implement your own formatting logic
 */
export function formatTikTokDataForAnalysis(data: TikTokScrapeResponse): string {
  const { items, totalItems, scrapeType, params } = data;
  
  let analysis = `TikTok Content Analysis (${scrapeType})\n`;
  analysis += `Generated on: ${data.scrapedAt}\n`;
  analysis += `Total Items: ${totalItems}\n`;
  
  // Add scrape context
  if (params.hashtags?.length) {
    analysis += `Hashtags: ${params.hashtags.join(', ')}\n`;
  }
  if (params.profiles?.length) {
    analysis += `Profiles: ${params.profiles.join(', ')}\n`;
  }
  if (params.searchQueries?.length) {
    analysis += `Search Queries: ${params.searchQueries.join(', ')}\n`;
  }
  analysis += '\n';

  if (items.length > 0) {
    analysis += "=== RAW ITEMS (First 3) ===\n\n";
    items.slice(0, 3).forEach((item, index) => {
      analysis += `${index + 1}. Raw Item Keys: ${Object.keys(item).join(', ')}\n`;
      if (item.text) {
        analysis += `   Text: ${item.text.substring(0, 100)}${item.text.length > 100 ? '...' : ''}\n`;
      }
      if (item.webVideoUrl) {
        analysis += `   URL: ${item.webVideoUrl}\n`;
      }
      analysis += '\n';
    });
  }

  analysis += `\nNote: This is raw data from the TikTok API. You can implement your own formatting logic based on the actual structure returned.\n`;

  return analysis;
}

// Helper functions removed - returning raw data without transformation
