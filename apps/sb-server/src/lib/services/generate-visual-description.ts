import dotenv from "dotenv";
import { Langfuse } from "langfuse";
import { callLLM } from "../utils/callLLM.js";

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL,
});

export const generateVisualDescription = async (
  brandBrief: string,
  content: string
): Promise<string> => {
  console.log("brandBrief", brandBrief);
  console.log("content", content);
  let response1: string | undefined = undefined;
  try {
    // Get production prompt
    const prompt: any = await langfuse.getPrompt(
      "generate_visual_description_step1",
      undefined,
      { label: "production" }
    );
    console.log("Full prompt object:", JSON.stringify(prompt, null, 2));
    console.log("prompt.config exists:", !!prompt.config);
    console.log("prompt.config:", prompt.config);
    const compiledMessages = prompt.compile({
      brand_brief: brandBrief,
      content: content,
    });
    console.log("prompt object:", prompt);
    console.log("compiledMessages:", compiledMessages);

    // For chat prompts, pass empty string as prompt and use additionalMessages
    const response = await callLLM(
      "",
      prompt.config, // Use the config portion of the prompt
      "google/gemini-2.5-pro-preview",
      { parse: false },
      compiledMessages
    );
    response1 = response.result;
    console.log("response1", response1);
  } catch (error) {
    console.error("Error generating visual description:", error);
    throw error;
  }

  try {
    // Get production prompt
    const prompt: any = await langfuse.getPrompt(
      "generate_visual_description_step2",
      undefined,
      { label: "production" }
    );
    console.log("Full prompt object:", JSON.stringify(prompt, null, 2));
    console.log("prompt.config exists:", !!prompt.config);
    console.log("prompt.config:", prompt.config);
    const compiledMessages = prompt.compile({
      brand_brief: brandBrief,
      image_gen_prompt: response1,
    });
    console.log("prompt object:", prompt);
    console.log("compiledMessages:", compiledMessages);

    // Use the config from langfuse (which should include the schema)
    const response = await callLLM(
      "",
      prompt.config, // Use the config directly from langfuse with schema
      "google/gemini-2.5-pro-preview",
      { parse: true }, // Set parse to true for JSON responses
      compiledMessages
    );

    console.log("response type:", typeof response);
    console.log("response:", response);

    // Check if response exists
    if (!response) {
      throw new Error("LLM response is null or undefined");
    }

    // Check for the expected property based on the schema
    if (!response.enhanced_prompts) {
      throw new Error("Response does not contain enhanced_prompts property");
    }

    return response.enhanced_prompts;
  } catch (error) {
    console.error("Error generating visual description:", error);
    throw error;
  }
};
