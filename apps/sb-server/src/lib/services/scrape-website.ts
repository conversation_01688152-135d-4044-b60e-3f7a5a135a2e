import axios from 'axios';
import * as cheerio from 'cheerio';

export interface ScrapedContent {
  title: string;
  description: string;
  content: string;
  url: string;
}

export const scrapeWebsite = async (url: string): Promise<ScrapedContent> => {
  try {
    // Make HTTP request to get the website content
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
      },
      timeout: 10000,
    });

    // Load the HTML content into cheerio
    const $ = cheerio.load(response.data);

    // Extract title
    const title = $('title').text().trim() || 
                  $('h1').first().text().trim() || 
                  'No title found';

    // Extract meta description
    const description = $('meta[name="description"]').attr('content') || 
                       $('meta[property="og:description"]').attr('content') || 
                       '';

    // Extract main content
    // Remove script, style, and other non-content elements
    $('script, style, nav, header, footer, aside, .cookie-banner, .popup, .modal').remove();
    
    // Try to get main content from common selectors
    let content = '';
    const contentSelectors = [
      'main',
      '[role="main"]',
      '.main-content',
      '.content',
      '.post-content',
      '.entry-content',
      'article',
      '.article-body',
      'body'
    ];

    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        // Get text content and clean it up
        content = element.text();
        break;
      }
    }

    // Clean up the content
    content = content
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n+/g, '\n') // Replace multiple newlines with single newline
      .trim();

    // If content is still empty, try to get all paragraph text
    if (!content || content.length < 100) {
      content = $('p').map((_, el) => $(el).text()).get().join('\n');
    }

    // Limit content length to avoid API limits
    if (content.length > 8000) {
      content = content.substring(0, 8000) + '...';
    }

    return {
      title,
      description,
      content,
      url
    };

  } catch (error) {
    console.error('Error scraping website:', error);
    throw new Error(`Failed to scrape website: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};