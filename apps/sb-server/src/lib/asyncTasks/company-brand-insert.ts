import postgres from 'postgres';
import { generateCompanyBrand } from '../services/generate_company_brand.js';


export async function companyBrandInsert(sql: any, {
  id,
  values,
  isHTML,
  html,
  brand_document,
}: {
  id: string;
  values;
  isHTML: boolean;
  html: string;
  brand_document: {
    url: string;
    pathname: string;
    downloadUrl: string;
  };
}) {

try {
  const companyBrand = await generateCompanyBrand(isHTML, html, brand_document);
  console.log('companyBrand', companyBrand);
  console.log('id', id);
  // fix sql syntax below
    await sql`
      UPDATE company_brand SET 
        is_generating = false,
        error_generating = false,
        updated_at = ${Date.now()},
        brand_profile = ${companyBrand.brand_profile},
        messaging_strategy = ${companyBrand.messaging_strategy},
        visual_identity = ${companyBrand.visual_identity},
        product_catalog = ${companyBrand.product_catalog},
        prompt_library = ${companyBrand.prompt_library}
      WHERE id = ${id}
    `;

} catch (error) {
  console.error('Failed to generate company brand:', error);
  
  // Update the company brand to indicate failure
  await sql`
    UPDATE company_brand 
    SET 
      is_generating = false,
      error_generating = true
    WHERE id = ${id}
  `;
}
}