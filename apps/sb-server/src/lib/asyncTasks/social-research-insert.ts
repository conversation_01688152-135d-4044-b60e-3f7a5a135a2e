import postgres from 'postgres';
import { scrapeTikTok } from '../services/scrape-tiktok.js';
import { scrapeTwitter } from '../services/scrape-x.js';

export async function socialResearchInsert(sql: any, {
  id,
  company_id,
  keywords,
  platform,
  // Twitter/X specific options
  onlyTwitterBlue,
  onlyVerifiedUsers,
  minimumFavorites,
  // TikTok specific options
  maxProfilesPerQuery,
}: {
  id: string;
  company_id: string;
  keywords: string[];
  platform: string;
  // Twitter/X specific options
  onlyTwitterBlue?: boolean;
  onlyVerifiedUsers?: boolean;
  minimumFavorites?: number;
  // TikTok specific options
  maxProfilesPerQuery?: number;
}) {
  try {
    console.log('Starting social research generation for ID:', id);
    console.log('Parameters:', { 
      keywords, 
      platform, 
      onlyTwitterBlue, 
      onlyVerifiedUsers, 
      minimumFavorites, 
      maxProfilesPerQuery 
    });

    let results: any[] = [];

    // Handle TikTok scraping if platform is tiktok
    if (platform === 'tiktok') {
      console.log('Scraping TikTok with keywords:', keywords);
      console.log('TikTok options:', { maxProfilesPerQuery });
      
      const tiktokResponse = await scrapeTikTok({
        hashtags: keywords,
        resultsPerPage: 10, // Default count since we removed the field
        proxyCountryCode: 'US',
        // Add maxProfilesPerQuery parameter if provided
        ...(maxProfilesPerQuery && { maxProfilesPerQuery: maxProfilesPerQuery })
      });

      // Add the TikTok results to our results array
      results = [
        ...results,
        ...tiktokResponse.items.map((item: any) => ({
          ...item,
          platform: 'tiktok',
          scraped_at: new Date().toISOString()
        }))
      ];

      console.log(`TikTok scraping completed: ${tiktokResponse.items.length} items found`);
    }
    
    // Handle X (Twitter) scraping if platform is x
    if (platform === 'x') {
      console.log('Scraping X (Twitter) with keywords:', keywords);
      console.log('X options:', { onlyTwitterBlue, onlyVerifiedUsers, minimumFavorites });
      
      const twitterResponse = await scrapeTwitter({
        searchTerms: keywords,
        maxItems: 10, // Default count
        sort: 'Latest',
        tweetLanguage: 'en',
        // Add Twitter-specific filters if provided
        ...(onlyTwitterBlue && { onlyTwitterBlue: true }),
        ...(onlyVerifiedUsers && { onlyVerifiedUsers: true }),
        ...(minimumFavorites && minimumFavorites > 0 && { minimumFavorites: minimumFavorites })
      });

      // Add the Twitter results to our results array  
      results = [
        ...results,
        ...twitterResponse.tweets.map((tweet: any) => ({
          ...tweet,
          platform: 'x',
          scraped_at: new Date().toISOString()
        })),
        ...twitterResponse.users.map((user: any) => ({
          ...user,
          platform: 'x',
          scraped_at: new Date().toISOString(),
          content_type: 'user_profile'
        }))
      ];

      console.log(`X scraping completed: ${twitterResponse.tweets.length} tweets, ${twitterResponse.users.length} users found`);
    }

    // TODO: Add other platforms when they're ready
    // if (platform === 'reddit') { ... }
    // if (platform === 'instagram') { ... }

    // Update the socials_research table with results
    await sql`
      UPDATE socials_research
      SET 
        results = ${sql.json(results as any)},
        is_generating = false
      WHERE id = ${id}
    `;

    console.log('✅ Social research generated and updated for ID:', id);
    console.log(`Total results: ${results.length}`);
    
  } catch (error) {
    console.error('Failed to generate social research:', error);
    
    // Update the socials_research table to indicate failure
    await sql`
      UPDATE socials_research 
      SET 
        is_generating = false,
        error_generating = true
      WHERE id = ${id}
    `;
  }
}
