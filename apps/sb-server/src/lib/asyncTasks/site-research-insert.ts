import postgres from 'postgres';
import { generateContentSchedule } from '../services/generate-schedule.js';
import { extractFromWebsite, getExtractStatus, SchemaItem } from '../services/extract-from-website.js';


export async function siteResearchInsert(sql: any, {
  id,
  company_id,
  icps,
  personal,
  urls,
  instruction,
  schema,
  enable_web_search,
  agent_mode,
}: {
  id: string;
  company_id: string;
  icps: string[];
  personal: string[];
  urls: string[];
  instruction: string;
  schema: SchemaItem[];
  enable_web_search: boolean;
  agent_mode: boolean;
}) {
  try {
    let results : any;
    const extractResult = await extractFromWebsite(urls, instruction, schema, enable_web_search);
    const extractId = extractResult.id;
    // try the id every 5 seconds until 'status' is 'completed'
    if (!extractResult.success) {
      throw new Error(`Failed to extract from website ${extractResult.error}`);
    }
    while (true) {
      await new Promise(resolve => setTimeout(resolve, 5000));
      const data = await getExtractStatus(extractId);
      console.log('status', data);
      
      if (data.status === 'completed') {
        const results = data.data.results;
        console.log('results!', results);
        await sql`
          UPDATE site_research
            SET 
            results = ${sql.json(results as any)},
            is_generating = false
          WHERE id = ${id}
        `;
        break;
      }
    }
    // return research;
} catch (error) {
  console.error('Failed to generate site research:', error);
  
  // Update the site research to indicate failure
  await sql`
    UPDATE site_research 
    SET 
      is_generating = false,
      error_generating = true
    WHERE id = ${id}
  `;
}
}