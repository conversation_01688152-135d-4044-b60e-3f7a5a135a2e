import axios from 'axios';
import { generateICP } from '../../services/generate-icp.js';
import dotenv from 'dotenv';
dotenv.config();

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.MockedFunction<typeof axios>;

// Mock environment variables
const originalEnv = process.env;

describe('generateICP', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('Successful ICP Generation', () => {
    beforeEach(() => {
      process.env.PERPLEXITY_API_KEY = '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';
    });

    it('should generate a valid ICP with structured response', async () => {
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: JSON.stringify({
                name: 'Mid-Market SaaS Companies',
                target_industries: ['Software', 'Technology', 'SaaS'],
                geography_markets: ['North America', 'Europe'],
                use_cases_problems: ['Process automation', 'Efficiency improvement'],
                buying_triggers: ['Growth phase', 'Scaling challenges'],
                decision_making_departments: ['IT', 'Operations'],
                technologies_used: ['Cloud platforms', 'APIs'],
                content_needs: ['Case studies', 'ROI calculators'],
                red_flags_disqualifiers: ['Budget constraints', 'Legacy systems'],
                company_size_employees: { min: 100, max: 1000 },
                company_revenue_range_usd: { min: 5000000, max: 50000000 },
                typical_contract_value: { min: 25000, max: 150000 },
                lifecycle_stage: 'Scale-up',
                notes: 'Technology-focused companies looking for automation solutions'
              })
            }
          }],
          citations: [],
          usage: {},
          model: 'sonar-pro'
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      const result = await generateICP('Sample website content about automation tools', 'TestCorp');

      // Validate structure and data types, not exact content
      expect(result).toBeDefined();
      expect(typeof result.name).toBe('string');
      expect(result.name.length).toBeGreaterThan(0);

      // Validate arrays
      expect(Array.isArray(result.target_industries)).toBe(true);
      expect(result.target_industries.length).toBeGreaterThan(0);
      expect(result.target_industries.every(item => typeof item === 'string')).toBe(true);

      expect(Array.isArray(result.geography_markets)).toBe(true);
      expect(result.geography_markets.length).toBeGreaterThan(0);
      expect(result.geography_markets.every(item => typeof item === 'string')).toBe(true);

      expect(Array.isArray(result.use_cases_problems)).toBe(true);
      expect(result.use_cases_problems.length).toBeGreaterThan(0);
      expect(result.use_cases_problems.every(item => typeof item === 'string')).toBe(true);

      expect(Array.isArray(result.buying_triggers)).toBe(true);
      expect(result.buying_triggers.length).toBeGreaterThan(0);
      expect(result.buying_triggers.every(item => typeof item === 'string')).toBe(true);

      expect(Array.isArray(result.decision_making_departments)).toBe(true);
      expect(result.decision_making_departments.length).toBeGreaterThan(0);
      expect(result.decision_making_departments.every(item => typeof item === 'string')).toBe(true);

      expect(Array.isArray(result.technologies_used)).toBe(true);
      expect(result.technologies_used.length).toBeGreaterThan(0);
      expect(result.technologies_used.every(item => typeof item === 'string')).toBe(true);

      expect(Array.isArray(result.content_needs)).toBe(true);
      expect(result.content_needs.length).toBeGreaterThan(0);
      expect(result.content_needs.every(item => typeof item === 'string')).toBe(true);

      expect(Array.isArray(result.red_flags_disqualifiers)).toBe(true);
      expect(result.red_flags_disqualifiers.length).toBeGreaterThan(0);
      expect(result.red_flags_disqualifiers.every(item => typeof item === 'string')).toBe(true);

      // Validate numeric range objects
      expect(typeof result.company_size_employees).toBe('object');
      expect(result.company_size_employees).toHaveProperty('min');
      expect(result.company_size_employees).toHaveProperty('max');
      expect(result.company_size_employees.min === null || typeof result.company_size_employees.min === 'number').toBe(true);
      expect(result.company_size_employees.max === null || typeof result.company_size_employees.max === 'number').toBe(true);

      expect(typeof result.company_revenue_range_usd).toBe('object');
      expect(result.company_revenue_range_usd).toHaveProperty('min');
      expect(result.company_revenue_range_usd).toHaveProperty('max');
      expect(result.company_revenue_range_usd.min === null || typeof result.company_revenue_range_usd.min === 'number').toBe(true);
      expect(result.company_revenue_range_usd.max === null || typeof result.company_revenue_range_usd.max === 'number').toBe(true);

      expect(typeof result.typical_contract_value).toBe('object');
      expect(result.typical_contract_value).toHaveProperty('min');
      expect(result.typical_contract_value).toHaveProperty('max');
      expect(result.typical_contract_value.min === null || typeof result.typical_contract_value.min === 'number').toBe(true);
      expect(result.typical_contract_value.max === null || typeof result.typical_contract_value.max === 'number').toBe(true);

      // Validate lifecycle stage enum
      expect(result.lifecycle_stage === null || ['Startup', 'Scale-up', 'Mature Enterprise'].includes(result.lifecycle_stage)).toBe(true);

      // Validate notes
      expect(typeof result.notes).toBe('string');
      expect(result.notes.length).toBeGreaterThan(0);

      // Verify API call was made correctly
      expect(mockedAxios).toHaveBeenCalledWith('https://api.perplexity.ai/chat/completions', {
        model: "sonar-pro",
        messages: [
          {
            role: "system",
            content: expect.stringContaining('expert business analyst')
          },
          {
            role: "user",
            content: expect.stringContaining('TestCorp')
          }
        ],
        response_format: {
          type: "json_schema",
          json_schema: { schema: expect.any(Object) }
        }
      }, {
        headers: {
          'accept': 'application/json',
          'content-type': 'application/json',
          'Authorization': 'Bearer 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'
        }
      });
    });

    it('should handle response with <think> tags', async () => {
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: `<think>
                Let me analyze this website content...
                The company seems to focus on automation...
              </think>
              ${JSON.stringify({
                name: 'Enterprise Automation Clients',
                target_industries: ['Manufacturing', 'Healthcare'],
                geography_markets: ['Global'],
                use_cases_problems: ['Manual processes'],
                buying_triggers: ['Compliance requirements'],
                decision_making_departments: ['Operations'],
                technologies_used: ['ERP systems'],
                content_needs: ['White papers'],
                red_flags_disqualifiers: ['Small budget'],
                company_size_employees: { min: 500, max: null },
                company_revenue_range_usd: { min: 10000000, max: null },
                typical_contract_value: { min: 50000, max: 500000 },
                lifecycle_stage: 'Mature Enterprise',
                notes: 'Large enterprises with complex operations'
              })}`
            }
          }]
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      const result = await generateICP('Enterprise automation platform', 'AutomateCorp');

      // Validate structure without checking exact content
      expect(typeof result.name).toBe('string');
      expect(result.name.length).toBeGreaterThan(0);
      expect(result.lifecycle_stage).toBe('Mature Enterprise');
      expect(result.company_size_employees.max).toBeNull();
      expect(Array.isArray(result.target_industries)).toBe(true);
      expect(result.target_industries.length).toBeGreaterThan(0);
    });

    it('should handle null values in numeric ranges', async () => {
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: JSON.stringify({
                name: 'Test Profile',
                target_industries: ['Technology'],
                geography_markets: ['Global'],
                use_cases_problems: ['General needs'],
                buying_triggers: ['Growth'],
                decision_making_departments: ['IT'],
                technologies_used: ['Software'],
                content_needs: ['Documentation'],
                red_flags_disqualifiers: ['Budget issues'],
                company_size_employees: { min: null, max: null },
                company_revenue_range_usd: { min: 1000000, max: null },
                typical_contract_value: { min: null, max: 100000 },
                lifecycle_stage: null,
                notes: 'Test profile with null values'
              })
            }
          }]
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      const result = await generateICP('Limited content', 'TestCorp');

      // Validate that null values are handled correctly
      expect(result.company_size_employees.min).toBeNull();
      expect(result.company_size_employees.max).toBeNull();
      expect(result.company_revenue_range_usd.max).toBeNull();
      expect(result.typical_contract_value.min).toBeNull();
      expect(result.lifecycle_stage).toBeNull();
      
      // Ensure structure is still valid
      expect(typeof result.name).toBe('string');
      expect(Array.isArray(result.target_industries)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should throw error when PERPLEXITY_API_KEY is not set', async () => {
      delete process.env.PERPLEXITY_API_KEY;

      await expect(generateICP('content', 'TestCorp')).rejects.toThrow(
        'PERPLEXITY_API_KEY environment variable is not set'
      );

      expect(mockedAxios).not.toHaveBeenCalled();
    });

    it('should return fallback ICP when API request fails', async () => {
      process.env.PERPLEXITY_API_KEY = 'test-api-key';
      
      mockedAxios.mockRejectedValueOnce(new Error('API request failed'));

      const result = await generateICP('content', 'TestCorp');

      // Validate fallback structure
      expect(result.name).toBe('TestCorp Target Profile');
      expect(Array.isArray(result.target_industries)).toBe(true);
      expect(result.target_industries.length).toBeGreaterThan(0);
      expect(Array.isArray(result.geography_markets)).toBe(true);
      expect(result.geography_markets.length).toBeGreaterThan(0);
      expect(typeof result.company_size_employees).toBe('object');
      expect(typeof result.company_revenue_range_usd).toBe('object');
      expect(typeof result.typical_contract_value).toBe('object');
      expect(result.lifecycle_stage).toBe('Scale-up');
      expect(result.notes).toContain('fallback data due to processing error');
    });

    it('should return fallback ICP when response parsing fails', async () => {
      process.env.PERPLEXITY_API_KEY = 'test-api-key';
      
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: 'Invalid JSON response that cannot be parsed'
            }
          }]
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      const result = await generateICP('content', 'TestCorp');

      // Validate fallback structure
      expect(result.name).toBe('TestCorp Target Profile');
      expect(result.notes).toContain('fallback data due to processing error');
      expect(Array.isArray(result.target_industries)).toBe(true);
    });

    it('should return fallback ICP when response structure is invalid', async () => {
      process.env.PERPLEXITY_API_KEY = 'test-api-key';
      
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: JSON.stringify({
                name: 'Valid Name',
                // Missing required fields like target_industries
              })
            }
          }]
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      const result = await generateICP('content', 'TestCorp');

      // Validate fallback structure
      expect(result.name).toBe('TestCorp Target Profile');
      expect(result.notes).toContain('fallback data due to processing error');
      expect(Array.isArray(result.target_industries)).toBe(true);
    });
  });

  describe('Input Validation', () => {
    beforeEach(() => {
      process.env.PERPLEXITY_API_KEY = 'test-api-key';
    });

    it('should handle empty website content', async () => {
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: JSON.stringify({
                name: 'General Business Profile',
                target_industries: ['Various'],
                geography_markets: ['Global'],
                use_cases_problems: ['General business needs'],
                buying_triggers: ['Business growth'],
                decision_making_departments: ['Management'],
                technologies_used: ['Standard business tools'],
                content_needs: ['General information'],
                red_flags_disqualifiers: ['Limited information'],
                company_size_employees: { min: null, max: null },
                company_revenue_range_usd: { min: null, max: null },
                typical_contract_value: { min: null, max: null },
                lifecycle_stage: null,
                notes: 'Limited website content available for analysis'
              })
            }
          }]
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      const result = await generateICP('', 'TestCorp');

      // Validate structure handles null values properly
      expect(typeof result.name).toBe('string');
      expect(result.company_size_employees.min).toBeNull();
      expect(result.lifecycle_stage).toBeNull();
      expect(Array.isArray(result.target_industries)).toBe(true);
    });

    it('should handle special characters in company name', async () => {
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: JSON.stringify({
                name: 'Tech Company Profile',
                target_industries: ['Technology'],
                geography_markets: ['North America'],
                use_cases_problems: ['Tech solutions'],
                buying_triggers: ['Innovation needs'],
                decision_making_departments: ['CTO'],
                technologies_used: ['Modern tech stack'],
                content_needs: ['Technical documentation'],
                red_flags_disqualifiers: ['Outdated systems'],
                company_size_employees: { min: 10, max: 100 },
                company_revenue_range_usd: { min: 500000, max: 5000000 },
                typical_contract_value: { min: 5000, max: 50000 },
                lifecycle_stage: 'Startup',
                notes: 'Analysis based on tech company profile'
              })
            }
          }]
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      const result = await generateICP('content', 'Tech & Co. (Special-Chars)');

      // Validate structure
      expect(typeof result.name).toBe('string');
      expect(result.lifecycle_stage).toBe('Startup');
      expect(Array.isArray(result.target_industries)).toBe(true);
      
      // Verify company name was passed correctly in API call
      expect(mockedAxios).toHaveBeenCalledWith('https://api.perplexity.ai/chat/completions', 
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              content: expect.stringContaining('Tech & Co. (Special-Chars)')
            })
          ])
        }),
        expect.any(Object)
      );
    });
  });

  describe('API Request Configuration', () => {
    beforeEach(() => {
      process.env.PERPLEXITY_API_KEY = 'test-api-key';
    });

    it('should configure the API request with correct parameters', async () => {
      const mockResponse = {
        data: {
          choices: [{
            message: {
              content: JSON.stringify({
                name: 'Test Profile',
                target_industries: ['Test'],
                geography_markets: ['Test'],
                use_cases_problems: ['Test'],
                buying_triggers: ['Test'],
                decision_making_departments: ['Test'],
                technologies_used: ['Test'],
                content_needs: ['Test'],
                red_flags_disqualifiers: ['Test'],
                company_size_employees: { min: 1, max: 10 },
                company_revenue_range_usd: { min: 1000, max: 10000 },
                typical_contract_value: { min: 100, max: 1000 },
                lifecycle_stage: 'Startup',
                notes: 'Test notes'
              })
            }
          }]
        }
      };

      mockedAxios.mockResolvedValueOnce(mockResponse as any);

      await generateICP('test content', 'TestCorp');

      expect(mockedAxios).toHaveBeenCalledWith('https://api.perplexity.ai/chat/completions', {
        model: "sonar-pro",
        messages: [
          {
            role: "system",
            content: expect.stringContaining('expert business analyst')
          },
          {
            role: "user",  
            content: expect.stringContaining('TestCorp')
          }
        ],
        response_format: {
          type: "json_schema",
          json_schema: { 
            schema: expect.objectContaining({
              type: "object",
              properties: expect.objectContaining({
                name: { type: "string" },
                target_industries: expect.objectContaining({
                  type: "array",
                  items: { type: "string" }
                }),
                lifecycle_stage: {
                  type: ["string", "null"],
                  enum: ["Startup", "Scale-up", "Mature Enterprise", null]
                }
              }),
              required: expect.arrayContaining([
                "name", "target_industries", "geography_markets", "use_cases_problems",
                "buying_triggers", "decision_making_departments", "technologies_used",
                "content_needs", "red_flags_disqualifiers", "company_size_employees",
                "company_revenue_range_usd", "typical_contract_value", "lifecycle_stage", "notes"
              ])
            })
          }
        }
      }, {
        headers: {
          'accept': 'application/json',
          'content-type': 'application/json',
          'Authorization': 'Bearer test-api-key'
        }
      });
    });
  });
}); 