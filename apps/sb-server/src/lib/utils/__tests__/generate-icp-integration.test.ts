import { generateICP } from '../../services/generate-icp.js';
import { scrapeWebsite } from '../../services/scrape-website.js';
import dotenv from 'dotenv';
dotenv.config();

// Integration tests that use real API calls
// These tests are skipped by default to avoid hitting API limits during regular testing
// Remove .skip to run these tests with actual API calls

describe('generateICP Integration Tests', () => {
  // Set a longer timeout for integration tests
  const INTEGRATION_TIMEOUT = 30000;

  beforeAll(() => {
    // Ensure we have the required API key for integration tests
    if (!process.env.PERPLEXITY_API_KEY) {
      console.warn('PERPLEXITY_API_KEY not set - skipping integration tests');
    }
  });

  describe.skip('Real Website Scraping and ICP Generation', () => {
    it('should scrape gora.io and generate a valid ICP', async () => {
      // Skip if no API key
      if (!process.env.PERPLEXITY_API_KEY) {
        console.log('Skipping test - PERPLEXITY_API_KEY not set');
        return;
      }

      console.log('🌐 Scraping gora.io website...');
      
      // Scrape the Gora.io website
      const scrapedContent = await scrapeWebsite('https://www.gora.io');
      
      console.log('📄 Scraped content preview:');
      console.log('Title:', scrapedContent.title);
      console.log('Description:', scrapedContent.description);
      console.log('Content length:', scrapedContent.content.length);
      console.log('Content preview:', scrapedContent.content.substring(0, 200) + '...');
      
      // Verify we got meaningful content
      expect(scrapedContent.title).toBeTruthy();
      expect(scrapedContent.content.length).toBeGreaterThan(100);
      
      console.log('🤖 Generating ICP using Perplexity API...');
      
      // Generate ICP using the scraped content
      const icp = await generateICP(scrapedContent.content, 'Gora');
      
      console.log('📊 Generated ICP:');
      console.log(JSON.stringify(icp, null, 2));
      
      // Verify the ICP structure
      expect(icp).toBeDefined();
      expect(icp.name).toBeTruthy();
      expect(Array.isArray(icp.target_industries)).toBe(true);
      expect(Array.isArray(icp.geography_markets)).toBe(true);
      expect(Array.isArray(icp.use_cases_problems)).toBe(true);
      expect(Array.isArray(icp.buying_triggers)).toBe(true);
      expect(Array.isArray(icp.decision_making_departments)).toBe(true);
      expect(Array.isArray(icp.technologies_used)).toBe(true);
      expect(Array.isArray(icp.content_needs)).toBe(true);
      expect(Array.isArray(icp.red_flags_disqualifiers)).toBe(true);
      
      // Verify numeric ranges
      expect(typeof icp.company_size_employees).toBe('object');
      expect(typeof icp.company_revenue_range_usd).toBe('object');
      expect(typeof icp.typical_contract_value).toBe('object');
      
      // Verify lifecycle stage is valid or null
      if (icp.lifecycle_stage !== null) {
        expect(['Startup', 'Scale-up', 'Mature Enterprise']).toContain(icp.lifecycle_stage);
      }
      
      expect(icp.notes).toBeTruthy();
      
      // Log some interesting findings
      console.log('🎯 Key ICP insights:');
      console.log('- Target Industries:', icp.target_industries.join(', '));
      console.log('- Key Use Cases:', icp.use_cases_problems.slice(0, 2).join(', '));
      console.log('- Lifecycle Stage:', icp.lifecycle_stage);
      console.log('- Geographic Markets:', icp.geography_markets.join(', '));
      
    }, INTEGRATION_TIMEOUT);

    it('should handle various website types and generate appropriate ICPs', async () => {
      // Skip if no API key
      if (!process.env.PERPLEXITY_API_KEY) {
        console.log('Skipping test - PERPLEXITY_API_KEY not set');
        return;
      }

      const testWebsites = [
        { url: 'https://www.gora.io', companyName: 'Gora' },
        // Add more websites here for comprehensive testing
        // { url: 'https://www.stripe.com', companyName: 'Stripe' },
        // { url: 'https://www.notion.so', companyName: 'Notion' },
      ];

      for (const site of testWebsites) {
        console.log(`\n🌐 Testing ${site.companyName} (${site.url})`);
        
        try {
          // Scrape website
          const content = await scrapeWebsite(site.url);
          expect(content.content.length).toBeGreaterThan(50);
          
          // Generate ICP
          const icp = await generateICP(content.content, site.companyName);
          
          // Basic validation
          expect(icp.name).toBeTruthy();
          expect(icp.target_industries.length).toBeGreaterThan(0);
          expect(icp.geography_markets.length).toBeGreaterThan(0);
          
          console.log(`✅ Successfully generated ICP for ${site.companyName}`);
          console.log(`   Name: ${icp.name}`);
          console.log(`   Industries: ${icp.target_industries.slice(0, 3).join(', ')}`);
          
        } catch (error) {
          console.error(`❌ Failed to process ${site.companyName}:`, error);
          throw error;
        }
        
        // Add delay between requests to be respectful
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }, INTEGRATION_TIMEOUT * 2);
  });

  describe('Website Scraping Only', () => {
    it('should successfully scrape gora.io website content', async () => {
      console.log('🌐 Testing website scraping for gora.io...');
      
      const result = await scrapeWebsite('https://www.gora.io');
      
      console.log('📄 Scraped website data:');
      console.log('Title:', result.title);
      console.log('Description:', result.description);
      console.log('URL:', result.url);
      console.log('Content length:', result.content.length);
      console.log('Content preview (first 300 chars):');
      console.log(result.content.substring(0, 300) + '...');
      
      // Assertions
      expect(result.title).toBeTruthy();
      expect(result.url).toBe('https://www.gora.io');
      expect(result.content.length).toBeGreaterThan(100);
      
      // The content should contain some meaningful text
      expect(result.content.toLowerCase()).toMatch(/gora|oracle|blockchain|data|smart/);
      
    }, INTEGRATION_TIMEOUT);

    it('should handle errors gracefully for invalid URLs', async () => {
      await expect(scrapeWebsite('https://invalid-website-that-does-not-exist.com'))
        .rejects.toThrow('Failed to scrape website');
    });
  });

  describe('Manual Test Helper', () => {
    it('should provide a way to test generateICP with custom content', async () => {
      // This test serves as a helper for manual testing
      // You can modify the content below to test different scenarios
      
      const customContent = `
        Gora is a decentralized oracle network that brings real-world data to blockchain applications. 
        We provide secure, reliable, and tamper-proof data feeds for smart contracts across multiple blockchains.
        Our solutions enable DeFi protocols, prediction markets, and other blockchain applications to access
        off-chain data with cryptographic guarantees. Founded by experts in blockchain technology and data science,
        Gora serves enterprise clients and DeFi protocols worldwide.
      `;
      
      console.log('🧪 Testing generateICP with custom content...');
      console.log('Content:', customContent);
      
      // Only run if API key is available
      if (process.env.PERPLEXITY_API_KEY) {
        const icp = await generateICP(customContent, 'Gora');
        
        console.log('🎯 Generated ICP:');
        console.log(JSON.stringify(icp, null, 2));
        
        expect(icp.name).toBeTruthy();
        expect(icp.target_industries.length).toBeGreaterThan(0);
        
        // For blockchain/oracle companies, we'd expect certain characteristics
        expect(icp.target_industries.some(industry => 
          industry.toLowerCase().includes('blockchain') || 
          industry.toLowerCase().includes('defi') ||
          industry.toLowerCase().includes('fintech') ||
          industry.toLowerCase().includes('technology')
        )).toBe(true);
        
      } else {
        console.log('⚠️  Skipping API test - PERPLEXITY_API_KEY not set');
        console.log('To test with the API, set PERPLEXITY_API_KEY environment variable');
      }
    }, INTEGRATION_TIMEOUT);
  });
}); 