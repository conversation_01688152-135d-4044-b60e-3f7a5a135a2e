import dotenv from "dotenv";
import { observeOpenAI } from "langfuse";
import OpenAI from "openai";

dotenv.config();

export const callLLM = async (
  prompt: any,
  langfuseConfig: any,
  model: string,
  additionalParams: any = {
    parse: true,
  },
  additionalMessages: any = []
) => {
  try {
    const url = "https://openrouter.ai/api/v1";

    const client = new OpenAI({
      baseURL: url,
      apiKey: process.env.OPENROUTER_API_KEY,
    });
    console.log("langfuseConfig:", langfuseConfig);
    console.log("langfuseConfig.temperature:", langfuseConfig.temperature);
    console.log("langfuseConfig.schema:", langfuseConfig.schema);
    console.log("langfuseConfig.response_format:", langfuseConfig.response_format);
    console.log("typeof langfuseConfig.schema:", typeof langfuseConfig.schema);

    const langfuseClient = observeOpenAI(client);

    // Prepare the parameters object
    const apiParams: any = {
      model, // or any OpenRouter-supported model
      temperature: langfuseConfig.temperature,
      messages: [{ role: "user", content: prompt }, ...additionalMessages],
      ...additionalParams,
    };

    // Check for response_format first (this is the correct property for JSON schema)
    if (langfuseConfig.response_format && typeof langfuseConfig.response_format === "object") {
      apiParams.response_format = langfuseConfig.response_format;
      console.log("Using response_format from langfuseConfig:", langfuseConfig.response_format);
    }
    // Fallback to schema if response_format doesn't exist
    else if (langfuseConfig.schema && typeof langfuseConfig.schema === "object") {
      apiParams.response_format = langfuseConfig.schema;
      console.log("Using schema as response_format:", langfuseConfig.schema);
    }

    console.log("Final API params:", JSON.stringify(apiParams, null, 2));

    const response = await langfuseClient.chat.completions.create(apiParams);
    console.log(response.choices[0].message.content);
    const cleanedJson = clean(response.choices[0].message.content || "");
    console.log("cleanedJson:", {additionalParams});
    if (additionalParams.parse) {
      return JSON.parse(cleanedJson);
    }
    return cleanedJson;
  } catch (error) {
    console.error({ error });
    throw new Error(`Failed to call LLM: ${error}`);
  }
};

const clean = (text: string) => text.trim().replace(/^```(?:\w*\n)?|```$/g, "");
