/**
 * Shared utilities for content generation (backend)
 */

import { 
  ContentGenerationParams, 
  PromptVariables, 
  CampaignData, 
  CompanyBrandData 
} from '../types/content-generation.js';

/**
 * Creates prompt variables from content generation parameters
 */
export function createPromptVariables(params: ContentGenerationParams): PromptVariables {
  return {
    channel: params.channel,
    content_type: params.contentType,
    task_title: params.taskTitle,
    task_description: params.taskDescription,
    personas_block: params.targetPersonas || "",
    icps_block: params.targetICPs || "",
    research_block: params.externalResearch || "",
    documents_block: params.productInformation || "",
    seo_keywords: "",
    trend_keywords: "",
    brand_guidelines: JSON.stringify(params.companyBrand) || "",
  };
}

/**
 * Normalizes compiled prompt into a single string for LLM consumption
 */
export function normalizeCompiledPrompt(compiled: any): string {
  if (Array.isArray(compiled)) {
    // Chat-style prompt: join messages in order with role headers
    return compiled
      .map((m: any) => `${m.role ? m.role.toUpperCase() + ': ' : ''}${typeof m.content === 'string' ? m.content : ''}`)
      .join('\n\n');
  } else if (compiled && typeof compiled === 'object' && Array.isArray((compiled as any).messages)) {
    return (compiled as any).messages
      .map((m: any) => `${m.role ? m.role.toUpperCase() + ': ' : ''}${typeof m.content === 'string' ? m.content : ''}`)
      .join('\n\n');
  } else {
    return String(compiled ?? '');
  }
}

/**
 * Validates that a compiled prompt has content
 */
export function validateCompiledPrompt(compiledText: string): void {
  if (!compiledText || compiledText.trim().length === 0) {
    throw new Error("Compiled prompt is empty - Langfuse prompt has no content");
  }
}

/**
 * Creates content generation parameters from campaign and task data
 */
export function createContentGenerationParams(
  task: { task_title: string; task_description: string; content_type: string; channel: string },
  campaign: CampaignData,
  productInformation?: string,
  targetICPs?: string,
  targetPersonas?: string,
  companyBrand?: CompanyBrandData,
  externalResearch?: string
): ContentGenerationParams {
  return {
    taskTitle: task.task_title,
    taskDescription: task.task_description,
    contentType: task.content_type,
    channel: task.channel,
    campaignGoal: campaign.objective,
    productInformation,
    targetICPs,
    targetPersonas,
    companyBrand,
    externalResearch,
  };
}

/**
 * Formats personas data for prompt inclusion
 */
export function formatPersonasForPrompt(personasData: any[]): string {
  return personasData
    .map(persona => JSON.stringify(persona.data))
    .join('\n\n');
}

/**
 * Formats ICPs data for prompt inclusion
 */
export function formatICPsForPrompt(icpsData: any[]): string {
  return icpsData
    .map(icp => JSON.stringify(icp.data))
    .join('\n\n');
}

/**
 * Error handling utility for content generation
 */
export class ContentGenerationError extends Error {
  constructor(
    message: string,
    public taskId?: string,
    public taskTitle?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ContentGenerationError';
  }
}

/**
 * Validates content generation parameters
 */
export function validateContentGenerationParams(params: ContentGenerationParams): void {
  if (!params.taskTitle?.trim()) {
    throw new ContentGenerationError('Task title is required');
  }
  if (!params.taskDescription?.trim()) {
    throw new ContentGenerationError('Task description is required');
  }
  if (!params.contentType?.trim()) {
    throw new ContentGenerationError('Content type is required');
  }
  if (!params.channel?.trim()) {
    throw new ContentGenerationError('Channel is required');
  }
}
