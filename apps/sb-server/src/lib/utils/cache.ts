/**
 * Simple in-memory cache for frequently accessed data
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class SimpleCache {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes default

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    };

    this.cache.set(key, entry);
  }

  /**
   * Delete data from cache
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Clean expired entries
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
export const cache = new SimpleCache();

// Cache key generators
export const CacheKeys = {
  campaign: (campaignId: string) => `campaign:${campaignId}`,
  companyBrand: (companyId: string) => `brand:${companyId}`,
  products: (companyId: string) => `products:${companyId}`,
  personas: (personaIds: string[]) => `personas:${personaIds.sort().join(',')}`,
  icps: (icpIds: string[]) => `icps:${icpIds.sort().join(',')}`,
  research: (researchIds: string[]) => `research:${researchIds.sort().join(',')}`,
  accountData: (companyId: string) => `account:${companyId}`,
} as const;

/**
 * Cache wrapper for async functions
 */
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // Try to get from cache first
  const cached = cache.get<T>(key);
  if (cached !== null) {
    return cached;
  }

  // Fetch data and cache it
  const data = await fetcher();
  cache.set(key, data, ttl);
  return data;
}

/**
 * Batch cache operations
 */
export class BatchCache {
  private operations: Array<() => Promise<any>> = [];
  private results: any[] = [];

  add<T>(key: string, fetcher: () => Promise<T>, ttl?: number): BatchCache {
    this.operations.push(async () => {
      return withCache(key, fetcher, ttl);
    });
    return this;
  }

  async execute(): Promise<any[]> {
    this.results = await Promise.all(this.operations.map(op => op()));
    return this.results;
  }

  getResult<T>(index: number): T {
    return this.results[index];
  }
}

/**
 * Auto-cleanup cache every 10 minutes
 */
setInterval(() => {
  cache.cleanup();
}, 10 * 60 * 1000);
