import dotenv from 'dotenv';
import { scrapeTwitter, formatTwitterDataForAnalysis, buildAdvancedTwitterQuery } from '../lib/services/scrape-x.js';

dotenv.config();

async function testTwitterScraper() {
  console.log('Testing Twitter/X scraper...');
  
  try {
    // Test 1: Search for tweets with specific keywords
    console.log('\n=== TEST 1: Search Terms ===');
    const searchResults = await scrapeTwitter({
      searchTerms: ['artificial intelligence', 'ai marketing'],
      maxItems: 10,
      sort: 'Latest',
      tweetLanguage: 'en'
    });

    console.log(`Search results: ${searchResults.totalItems} items`);
    console.log(`Tweets: ${searchResults.tweets.length}`);
    console.log(`Users: ${searchResults.users.length}`);

    if (searchResults.tweets.length > 0) {
      const tweet = searchResults.tweets[0];
      console.log(`\nSample tweet:`);
      console.log(`- Text: ${tweet.text.substring(0, 100)}...`);
      console.log(`- Author: @${tweet.author.userName} (${tweet.author.name})`);
      console.log(`- Engagement: ${tweet.likeCount} likes, ${tweet.retweetCount} retweets`);
      console.log(`- URL: ${tweet.url}`);
    }

    // Test 2: Search for tweets from specific users
    console.log('\n=== TEST 2: Twitter Handles ===');
    const handleResults = await scrapeTwitter({
      twitterHandles: ['elonmusk', 'OpenAI'],
      maxItems: 5,
      sort: 'Top'
    });

    console.log(`Handle results: ${handleResults.totalItems} items`);
    if (handleResults.tweets.length > 0) {
      const tweet = handleResults.tweets[0];
      console.log(`\nSample tweet from handle:`);
      console.log(`- Author: @${tweet.author.userName}`);
      console.log(`- Text: ${tweet.text.substring(0, 100)}...`);
      console.log(`- Engagement: ${tweet.likeCount} likes`);
    }

    // Test 3: Search with filters
    console.log('\n=== TEST 3: Filtered Search ===');
    const filteredResults = await scrapeTwitter({
      searchTerms: ['marketing'],
      maxItems: 5,
      onlyVerifiedUsers: true,
      minimumFavorites: 10,
      tweetLanguage: 'en'
    });

    console.log(`Filtered results: ${filteredResults.totalItems} items`);
    console.log(`All from verified users: ${filteredResults.filters.verifiedOnly}`);

    // Test 4: Advanced query building
    console.log('\n=== TEST 4: Advanced Query Building ===');
    const advancedQuery = buildAdvancedTwitterQuery({
      keywords: ['AI', 'machine learning'],
      fromUser: 'OpenAI',
      hashtags: ['AI', 'ML'],
      minLikes: 100,
      since: '2024-01-01',
      lang: 'en',
      verified: true
    });

    console.log(`Advanced query: ${advancedQuery}`);

    // Test 5: Date range search
    console.log('\n=== TEST 5: Date Range Search ===');
    const dateResults = await scrapeTwitter({
      searchTerms: ['tech news'],
      maxItems: 5,
      start: '2024-01-01',
      end: '2024-12-31',
      sort: 'Top'
    });

    console.log(`Date range results: ${dateResults.totalItems} items`);
    console.log(`Date range: ${dateResults.filters.dateRange?.start} to ${dateResults.filters.dateRange?.end}`);

    // Test formatting for analysis
    console.log('\n=== FORMATTED DATA FOR ANALYSIS ===');
    const formattedData = formatTwitterDataForAnalysis(searchResults);
    console.log(formattedData.substring(0, 1000) + '...');

    console.log('\n✅ Twitter scraper test completed successfully!');
    
  } catch (error) {
    console.error('❌ Twitter scraper test failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('token')) {
        console.error('\n💡 Make sure to set your APIFY_API_TOKEN environment variable');
        console.error('   You can get one from: https://console.apify.com/account/integrations');
      }
      if (error.message.includes('quota')) {
        console.error('\n💡 Check your Apify account limits and usage');
        console.error('   Twitter scraping may have different cost structure');
      }
      if (error.message.includes('At least one search term')) {
        console.error('\n💡 Twitter scraper requires at least one search parameter');
        console.error('   Provide searchTerms, twitterHandles, author, or mentioning');
      }
    }
  }
}

// Test individual functions
async function testUtilityFunctions() {
  console.log('\n=== TESTING UTILITY FUNCTIONS ===');
  
  // Test advanced query builder
  const query1 = buildAdvancedTwitterQuery({
    keywords: ['startup', 'entrepreneur'],
    fromUser: 'ycombinator',
    minLikes: 50,
    hasMedia: true
  });
  console.log(`Query 1: ${query1}`);

  const query2 = buildAdvancedTwitterQuery({
    hashtags: ['AI', 'MachineLearning'],
    verified: true,
    lang: 'en',
    since: '2024-01-01'
  });
  console.log(`Query 2: ${query2}`);

  const query3 = buildAdvancedTwitterQuery({
    mentioning: 'OpenAI',
    minRetweets: 100,
    until: '2024-12-31',
    hasLinks: true
  });
  console.log(`Query 3: ${query3}`);
}

// Run the tests
async function runAllTests() {
  await testTwitterScraper();
  await testUtilityFunctions();
}

runAllTests().catch(console.error); 