import dotenv from 'dotenv';
import { ApifyClient } from 'apify-client';

dotenv.config();

const client = new ApifyClient({
  token: process.env.APIFY_API_TOKEN,
});

async function debugTwitterStructure() {
  console.log('🔍 Debugging Twitter API response structure...');
  
  try {
    const input = {
      searchTerms: ['AI'],
      maxItems: 3,
      sort: 'Latest'
    };

    console.log('Input:', JSON.stringify(input, null, 2));

    const run = await client.actor('nfp1fpt5gUlBwPcor').call(input);
    
    if (!run || !run.defaultDatasetId) {
      throw new Error('Failed to start Twitter scraper');
    }

    const { items } = await client.dataset(run.defaultDatasetId).listItems();
    
    console.log(`\n📊 Found ${items.length} items`);
    
    if (items.length > 0) {
      console.log('\n🔍 First item structure:');
      console.log(JSON.stringify(items[0], null, 2));
      
      console.log('\n📋 First item keys:');
      console.log(Object.keys(items[0]));
      
      if (items.length > 1) {
        console.log('\n🔍 Second item structure:');
        console.log(JSON.stringify(items[1], null, 2));
        
        console.log('\n📋 Second item keys:');
        console.log(Object.keys(items[1]));
      }
      
      // Check for common field variations
      const item = items[0];
      console.log('\n🧪 Field analysis:');
      console.log(`- type: ${item.type}`);
      console.log(`- id: ${item.id}`);
      console.log(`- text: ${item.text ? 'present' : 'missing'}`);
      console.log(`- fullText: ${item.fullText ? 'present' : 'missing'}`);
      console.log(`- userName: ${item.userName ? 'present' : 'missing'}`);
      console.log(`- author: ${item.author ? 'present' : 'missing'}`);
      console.log(`- retweetCount: ${item.retweetCount !== undefined ? 'present' : 'missing'}`);
      console.log(`- likeCount: ${item.likeCount !== undefined ? 'present' : 'missing'}`);
    } else {
      console.log('❌ No items returned');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugTwitterStructure().catch(console.error); 