import dotenv from 'dotenv';
import { scrapeReddit, formatRedditDataForAnalysis } from '../lib/services/scrape-reddit.js';

dotenv.config();

async function testRedditScraper() {
  console.log('Testing Reddit scraper...');
  
  try {
    // Test basic Reddit scraping
    const results = await scrapeReddit({
      keywords: 'artificial intelligence',
      timeFilter: 'month', // Use the Apify format directly
      includeComments: true,
      includeEngagement: true,
      maxItems: 10,
      maxPostCount: 5,
      maxComments: 3,
      sort: 'new'
    });

    console.log('=== REDDIT SCRAPING RESULTS ===');
    console.log(`Total items found: ${results.totalItems}`);
    console.log(`Posts: ${results.posts.length}`);
    console.log(`Comments: ${results.comments.length}`);
    console.log(`Communities: ${results.communities.length}`);
    console.log(`Users: ${results.users.length}`);

    if (results.posts.length > 0) {
      console.log('\n=== SAMPLE POST ===');
      const post = results.posts[0];
      console.log(`Title: ${post.title}`);
      console.log(`Community: ${post.communityName}`);
      console.log(`Author: ${post.username}`);
      console.log(`Upvotes: ${post.upVotes}`);
      console.log(`Comments: ${post.numberOfComments}`);
      console.log(`URL: ${post.url}`);
    }

    if (results.comments.length > 0) {
      console.log('\n=== SAMPLE COMMENT ===');
      const comment = results.comments[0];
      console.log(`Author: ${comment.username}`);
      console.log(`Community: ${comment.communityName}`);
      console.log(`Content: ${comment.body.substring(0, 200)}...`);
      console.log(`Upvotes: ${comment.upVotes}`);
      console.log(`URL: ${comment.url}`);
    }

    // Test formatting for analysis
    console.log('\n=== FORMATTED DATA FOR ANALYSIS ===');
    const formattedData = formatRedditDataForAnalysis(results);
    console.log(formattedData.substring(0, 500) + '...');

    console.log('\n✅ Reddit scraper test completed successfully!');
    
  } catch (error) {
    console.error('❌ Reddit scraper test failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('token')) {
        console.error('\n💡 Make sure to set your APIFY_API_TOKEN environment variable');
        console.error('   You can get one from: https://console.apify.com/account/integrations');
      }
    }
  }
}

// Run the test
testRedditScraper().catch(console.error); 