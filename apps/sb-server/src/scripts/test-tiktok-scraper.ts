import dotenv from 'dotenv';
import { scrapeTikTok, formatTikTokDataForAnalysis } from '../lib/services/scrape-tiktok.js';

dotenv.config();

async function testTikTokScraper() {
  console.log('Testing TikTok scraper (new advanced version)...');
  
  try {
    // Test hashtag scraping with new advanced scraper
    const results = await scrapeTikTok({
      hashtags: ['ai', 'tech', 'business'],
      resultsPerPage: 10,
      proxyCountryCode: 'US'
    });

    console.log('=== TIKTOK SCRAPING RESULTS ===');
    console.log(`Total items found: ${results.totalItems}`);
    console.log(`Scrape type: ${results.scrapeType}`);
    console.log(`Hashtags: ${results.params.hashtags?.join(', ') || 'None'}`);
    console.log(`Scraped at: ${results.scrapedAt}`);

    if (results.items.length > 0) {
      console.log('\n=== SAMPLE RAW ITEM ===');
      const item = results.items[0];
      console.log('Available fields:', Object.keys(item).join(', '));
      console.log('\nSample item (first 500 chars):');
      console.log(JSON.stringify(item, null, 2).substring(0, 500) + '...');
    }

    // Test default behavior (should default to #fyp hashtag)
    console.log('\n=== TESTING DEFAULT BEHAVIOR ===');
    const defaultResults = await scrapeTikTok({
      resultsPerPage: 5
    });
    
    console.log(`Default scrape found ${defaultResults.totalItems} items with scrape type: ${defaultResults.scrapeType}`);

    // Test profile scraping
    console.log('\n=== TESTING PROFILE SCRAPING ===');
    const profileResults = await scrapeTikTok({
      profiles: ['garyvee'],
      resultsPerPage: 3,
      profileSorting: 'latest'
    });
    
    console.log(`Profile scrape found ${profileResults.totalItems} items from profiles`);

    // Test formatting for analysis
    console.log('\n=== FORMATTED DATA FOR ANALYSIS ===');
    const formattedData = formatTikTokDataForAnalysis(results);
    console.log(formattedData.substring(0, 1000) + '...');

    console.log('\n✅ TikTok scraper test completed successfully!');
    
  } catch (error) {
    console.error('❌ TikTok scraper test failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('token')) {
        console.error('\n💡 Make sure to set your APIFY_API_TOKEN environment variable');
        console.error('   You can get one from: https://console.apify.com/account/integrations');
      }
      if (error.message.includes('quota')) {
        console.error('\n💡 Check your Apify account limits and usage');
        console.error('   The new TikTok scraper may have different pricing');
      }
    }
  }
}

// Run the test
testTikTokScraper().catch(console.error); 