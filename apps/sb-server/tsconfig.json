{"extends": "../../tooling/typescript/base.json", "compilerOptions": {"target": "es2020", "module": "Node16", "moduleResolution": "Node16", "outDir": "./dist", "noEmit": false, "strict": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "types": ["node", "jest"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}