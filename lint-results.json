
> next-supabase-saas-kit-turbo@2.9.0 lint
> turbo lint --cache-dir=.turbo --affected --continue -- --cache --cache-location "node_modules/.cache/.eslintcache" && manypkg check --format=json

• Packages in scope: //, @kit/accounts, @kit/admin, @kit/analytics, @kit/auth, @kit/baselime, @kit/billing, @kit/billing-gateway, @kit/cms, @kit/cms-types, @kit/database-webhooks, @kit/email-templates, @kit/eslint-config, @kit/i18n, @kit/kanban, @kit/keystatic, @kit/lemon-squeezy, @kit/mailers, @kit/mailers-shared, @kit/monitoring, @kit/monitoring-core, @kit/next, @kit/nodemailer, @kit/notifications, @kit/otp, @kit/prettier-config, @kit/resend, @kit/sentry, @kit/shared, @kit/stripe, @kit/supabase, @kit/team-accounts, @kit/tsconfig, @kit/ui, @kit/wordpress, dev-tool, scripts, web, web-e2e
• Running lint in 39 packages
• Remote caching disabled
@kit/shared:lint: cache hit, replaying logs ceceb16268834eb2
@kit/shared:lint: 
@kit/shared:lint: 
@kit/shared:lint: > @kit/shared@0.1.0 lint /Users/<USER>/sb-front-end/packages/shared
@kit/shared:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/shared:lint: 
@kit/monitoring-core:lint: cache hit, replaying logs efc22df7451ee093
@kit/monitoring-core:lint: 
@kit/monitoring-core:lint: 
@kit/monitoring-core:lint: > @kit/monitoring-core@0.1.0 lint /Users/<USER>/sb-front-end/packages/monitoring/core
@kit/monitoring-core:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/monitoring-core:lint: 
@kit/analytics:lint: cache hit, replaying logs b411ab204e668929
@kit/analytics:lint: 
@kit/analytics:lint: 
@kit/analytics:lint: > @kit/analytics@0.1.0 lint /Users/<USER>/sb-front-end/packages/analytics
@kit/analytics:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/analytics:lint: 
@kit/ui:lint: cache hit, replaying logs c6c94f8c21069393
@kit/ui:lint: 
@kit/ui:lint: 
@kit/ui:lint: > @kit/ui@0.1.0 lint /Users/<USER>/sb-front-end/packages/ui
@kit/ui:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/ui:lint: 
@kit/baselime:lint: cache hit, replaying logs ce623b856264cbf7
@kit/baselime:lint: 
@kit/baselime:lint: 
@kit/baselime:lint: > @kit/baselime@0.1.0 lint /Users/<USER>/sb-front-end/packages/monitoring/baselime
@kit/baselime:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/baselime:lint: 
@kit/billing:lint: cache hit, replaying logs d6039ccec51b5c52
@kit/sentry:lint: cache hit, replaying logs e8e006aede340b4e
@kit/billing:lint: 
@kit/billing:lint: 
@kit/billing:lint: > @kit/billing@0.1.0 lint /Users/<USER>/sb-front-end/packages/billing/core
@kit/billing:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/billing:lint: 
@kit/kanban:lint: cache hit, replaying logs 0128ba6c99a287ae
@kit/nodemailer:lint: cache hit, replaying logs 29354dc8a45ee5c7
@kit/sentry:lint: 
@kit/sentry:lint: 
@kit/nodemailer:lint: 
@kit/nodemailer:lint: 
@kit/nodemailer:lint: > @kit/nodemailer@0.1.0 lint /Users/<USER>/sb-front-end/packages/mailers/nodemailer
@kit/nodemailer:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/nodemailer:lint: 
@kit/supabase:lint: cache hit, replaying logs 2cde1e68085c2d15
@kit/sentry:lint: > @kit/sentry@0.1.0 lint /Users/<USER>/sb-front-end/packages/monitoring/sentry
@kit/sentry:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/sentry:lint: 
@kit/notifications:lint: cache hit, replaying logs 9273bf01754ed029
@kit/kanban:lint: 
@kit/kanban:lint: 
@kit/kanban:lint: > @kit/kanban@0.1.0 lint /Users/<USER>/sb-front-end/packages/plugins/kanban
@kit/kanban:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/kanban:lint: 
@kit/mailers-shared:lint: cache hit, replaying logs 0fecedd364f90e01
@kit/notifications:lint: 
@kit/notifications:lint: 
@kit/notifications:lint: > @kit/notifications@0.1.0 lint /Users/<USER>/sb-front-end/packages/features/notifications
@kit/notifications:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/notifications:lint: 
@kit/mailers-shared:lint: 
@kit/mailers-shared:lint: 
@kit/mailers-shared:lint: > @kit/mailers-shared@0.1.0 lint /Users/<USER>/sb-front-end/packages/mailers/shared
@kit/mailers-shared:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/mailers-shared:lint: 
@kit/supabase:lint: 
@kit/supabase:lint: 
@kit/supabase:lint: > @kit/supabase@0.1.0 lint /Users/<USER>/sb-front-end/packages/supabase
@kit/supabase:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/supabase:lint: 
@kit/cms-types:lint: cache hit, replaying logs c789f4b93d52ade7
@kit/resend:lint: cache hit, replaying logs 0259365bc2250fdc
@kit/cms-types:lint: 
@kit/cms-types:lint: 
@kit/cms-types:lint: > @kit/cms-types@0.1.0 lint /Users/<USER>/sb-front-end/packages/cms/types
@kit/cms-types:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/cms-types:lint: 
@kit/resend:lint: 
@kit/resend:lint: 
@kit/resend:lint: > @kit/resend@0.1.0 lint /Users/<USER>/sb-front-end/packages/mailers/resend
@kit/resend:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/resend:lint: 
@kit/wordpress:lint: cache hit, replaying logs 851e3c0d939c669f
@kit/wordpress:lint: 
@kit/wordpress:lint: 
@kit/wordpress:lint: > @kit/wordpress@0.1.0 lint /Users/<USER>/sb-front-end/packages/cms/wordpress
@kit/wordpress:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/wordpress:lint: 
@kit/keystatic:lint: cache hit, replaying logs 729878de19ad70ba
@kit/keystatic:lint: 
@kit/keystatic:lint: 
@kit/keystatic:lint: > @kit/keystatic@0.1.0 lint /Users/<USER>/sb-front-end/packages/cms/keystatic
@kit/keystatic:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/keystatic:lint: 
@kit/auth:lint: cache hit, replaying logs 71b3ed0eefe572f1
@kit/auth:lint: 
@kit/auth:lint: 
@kit/auth:lint: > @kit/auth@0.1.0 lint /Users/<USER>/sb-front-end/packages/features/auth
@kit/auth:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/auth:lint: 
@kit/i18n:lint: cache hit, replaying logs cc26c93de19f4dcd
@kit/i18n:lint: 
@kit/i18n:lint: 
@kit/i18n:lint: > @kit/i18n@0.1.0 lint /Users/<USER>/sb-front-end/packages/i18n
@kit/i18n:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/i18n:lint: 
@kit/monitoring:lint: cache hit, replaying logs 231d3a56eac1d035
@kit/monitoring:lint: 
@kit/monitoring:lint: 
@kit/monitoring:lint: > @kit/monitoring@0.1.0 lint /Users/<USER>/sb-front-end/packages/monitoring/api
@kit/monitoring:lint: > eslint .. "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/monitoring:lint: 
@kit/lemon-squeezy:lint: cache hit, replaying logs 4da87e3e410a0b26
@kit/lemon-squeezy:lint: 
@kit/lemon-squeezy:lint: 
@kit/lemon-squeezy:lint: > @kit/lemon-squeezy@0.1.0 lint /Users/<USER>/sb-front-end/packages/billing/lemon-squeezy
@kit/lemon-squeezy:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/lemon-squeezy:lint: 
@kit/mailers:lint: cache hit, replaying logs e24c6fa7e3afd13e
@kit/mailers:lint: 
@kit/mailers:lint: 
@kit/mailers:lint: > @kit/mailers@0.1.0 lint /Users/<USER>/sb-front-end/packages/mailers/core
@kit/mailers:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/mailers:lint: 
@kit/stripe:lint: cache hit, replaying logs f6c0d507f725d0d9
@kit/stripe:lint: 
@kit/stripe:lint: 
@kit/stripe:lint: > @kit/stripe@0.1.0 lint /Users/<USER>/sb-front-end/packages/billing/stripe
@kit/stripe:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/stripe:lint: 
@kit/cms:lint: cache hit, replaying logs a4997be361259404
@kit/cms:lint: 
@kit/cms:lint: 
@kit/cms:lint: > @kit/cms@0.1.0 lint /Users/<USER>/sb-front-end/packages/cms/core
@kit/cms:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/cms:lint: 
@kit/email-templates:lint: cache hit, replaying logs 3021320f056b277c
@kit/email-templates:lint: 
@kit/email-templates:lint: 
@kit/email-templates:lint: > @kit/email-templates@0.1.0 lint /Users/<USER>/sb-front-end/packages/email-templates
@kit/email-templates:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/email-templates:lint: 
@kit/next:lint: cache hit, replaying logs 1b881084462bcb62
@kit/next:lint: 
@kit/next:lint: 
@kit/next:lint: > @kit/next@0.1.0 lint /Users/<USER>/sb-front-end/packages/next
@kit/next:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/next:lint: 
@kit/billing-gateway:lint: cache hit, replaying logs 00e77bd71f4a3a18
@kit/billing-gateway:lint: 
@kit/billing-gateway:lint: 
@kit/billing-gateway:lint: > @kit/billing-gateway@0.1.0 lint /Users/<USER>/sb-front-end/packages/billing/gateway
@kit/billing-gateway:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/billing-gateway:lint: 
@kit/admin:lint: cache hit, replaying logs 16d4cce4e9338dfb
@kit/admin:lint: 
@kit/admin:lint: 
@kit/admin:lint: > @kit/admin@0.1.0 lint /Users/<USER>/sb-front-end/packages/features/admin
@kit/admin:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/admin:lint: 
@kit/accounts:lint: cache hit, replaying logs e0eb26f313e7edf8
@kit/accounts:lint: 
@kit/accounts:lint: 
@kit/accounts:lint: > @kit/accounts@0.1.0 lint /Users/<USER>/sb-front-end/packages/features/accounts
@kit/accounts:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/accounts:lint: 
@kit/accounts:lint: [0m[0m
@kit/accounts:lint: [0m[4m/Users/<USER>/sb-front-end/packages/features/accounts/src/components/personal-account-settings/mfa/multi-factor-auth-setup-dialog.tsx[24m[0m
@kit/accounts:lint: [0m  [2m416:5[22m  [33mwarning[39m  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  [2m@next/next/no-img-element[22m[0m
@kit/accounts:lint: [0m[0m
@kit/accounts:lint: [0m[33m[1m✖ 1 problem (0 errors, 1 warning)[22m[39m[0m
@kit/accounts:lint: [0m[33m[1m[22m[39m[0m
@kit/team-accounts:lint: cache hit, replaying logs e8b0d268ff71d909
@kit/team-accounts:lint: 
@kit/team-accounts:lint: 
@kit/team-accounts:lint: > @kit/team-accounts@0.1.0 lint /Users/<USER>/sb-front-end/packages/features/team-accounts
@kit/team-accounts:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/team-accounts:lint: 
@kit/database-webhooks:lint: cache hit, replaying logs b33db858b2cf38b5
@kit/database-webhooks:lint: 
@kit/database-webhooks:lint: 
@kit/database-webhooks:lint: > @kit/database-webhooks@0.1.0 lint /Users/<USER>/sb-front-end/packages/database-webhooks
@kit/database-webhooks:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/database-webhooks:lint: 
@kit/database-webhooks:lint: [0m[0m
@kit/database-webhooks:lint: [0m[4m/Users/<USER>/sb-front-end/packages/database-webhooks/src/server/services/database-webhook-handler.service.ts[24m[0m
@kit/database-webhooks:lint: [0m  [2m71:9[22m  [33mwarning[39m  Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-explicit-any')[0m
@kit/database-webhooks:lint: [0m[0m
@kit/database-webhooks:lint: [0m[33m[1m✖ 1 problem (0 errors, 1 warning)[22m[39m[0m
@kit/database-webhooks:lint: [0m[33m[1m[22m[39m[33m[1m  0 errors and 1 warning potentially fixable with the `--fix` option.[22m[39m[0m
@kit/database-webhooks:lint: [0m[33m[1m[22m[39m[0m
web:lint: cache miss, executing 9676d777308afc64
@kit/otp:lint: cache hit, replaying logs e16bfbc6d1f064fc
@kit/otp:lint: 
@kit/otp:lint: 
@kit/otp:lint: > @kit/otp@0.1.0 lint /Users/<USER>/sb-front-end/packages/otp
@kit/otp:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
@kit/otp:lint: 
web:lint: 
web:lint: > web@0.1.0 lint /Users/<USER>/sb-front-end/apps/web
web:lint: > eslint . "--cache" "--cache-location" "node_modules/.cache/.eslintcache"
web:lint: 
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/_components/dashboard/components/dashboard-demo-charts.tsx
web:lint:   705:9  warning  The 'chartData' array makes the dependencies of useMemo Hook (at line 818) change on every render. To fix this, wrap the initialization of 'chartData' in its own useMemo() Hook  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/analytics/_components/post-analytics-list.tsx
web:lint:   114:19  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   664:23  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/BrandContent.tsx
web:lint:   39:48  error  Do not access Object.prototype method 'hasOwnProperty' from target object  no-prototype-builtins
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/BrandSidebar.tsx
web:lint:   12:54  warning  Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images  jsx-a11y/alt-text
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/add-brand.tsx
web:lint:   209:66  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/sections/BrandProfileSection.tsx
web:lint:   241:41  error  React Hook "useState" is called in function "renderBrandAttributes" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word "use"  react-hooks/rules-of-hooks
web:lint:   454:55  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                                                                              react/no-unescaped-entities
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/sections/ColorPickerDialog.tsx
web:lint:   48:23  error  'l' is never reassigned. Use 'const' instead  prefer-const
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/sections/LogoDialog.tsx
web:lint:   184:21  warning  Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images  jsx-a11y/alt-text
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/sections/MessagingStrategySection.tsx
web:lint:   97:94  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/sections/PromptLibrarySection.tsx
web:lint:   406:68  error  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
web:lint:   406:87  error  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/sections/SettingsSection.tsx
web:lint:    84:64  error  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
web:lint:    84:76  error  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
web:lint:   102:28  error  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
web:lint:   102:35  error  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/brand/components/sections/VisualIdentitySection.tsx
web:lint:   609:71  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
web:lint:   623:70  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
web:lint:   648:54  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
web:lint:   648:62  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
web:lint:   660:48  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/campaigns/[slug]/content-tasks/_components/brief-display.tsx
web:lint:   33:31  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/campaigns/[slug]/content-tasks/_components/content-task-outline-section.tsx
web:lint:   69:6  warning  React Hook useEffect has missing dependencies: 'brief', 'companyContent', and 'generateBrief'. Either include them or remove the dependency array. If '_setContentTasks' needs the current value of 'companyContent', you can also switch to useReducer instead of useState and read 'companyContent' in the reducer  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/campaigns/[slug]/creative-brief/_components/brief-display.tsx
web:lint:   36:31  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead      react-hooks/exhaustive-deps
web:lint:   69:6   warning  React Hook useEffect has a missing dependency: 'briefBlocks'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/campaigns/[slug]/creative-brief/_components/creative-brief-section.tsx
web:lint:   75:6  warning  React Hook useEffect has a missing dependency: 'generateBrief'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/campaigns/[slug]/ideation/_components/idea-display-section.tsx
web:lint:   78:31  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/campaigns/[slug]/overview/_components/campaign-objectives-form.tsx
web:lint:   196:25  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/integrations/_components/ConnectToAyrshare.tsx
web:lint:   23:6  warning  React Hook useEffect has missing dependencies: 'workspace.account.id' and 'workspace.user.id'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/integrations/_components/linkedin-integration-card.tsx
web:lint:   137:13  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   155:17  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/integrations/_components/require-linkedin.tsx
web:lint:   72:6  warning  React Hook useEffect has a missing dependency: 'checkLinkedInConnection'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/integrations/_components/require-twitter.tsx
web:lint:   72:6  warning  React Hook useEffect has a missing dependency: 'checkConnection'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/integrations/_components/social-profiles-list.tsx
web:lint:   124:6   warning  React Hook useEffect has a missing dependency: 'fetchProfiles'. Either include it or remove the dependency array                                                                                                                                                                         react-hooks/exhaustive-deps
web:lint:   218:11  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   227:13  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/integrations/_components/socials-integration-card.tsx
web:lint:   120:17  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   132:19  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/integrations/_components/twitter-integration-card.tsx
web:lint:    54:6   warning  React Hook useEffect has a missing dependency: 'fetchTwitterProfile'. Either include it or remove the dependency array                                                                                                                                                                   react-hooks/exhaustive-deps
web:lint:    81:6   warning  React Hook useEffect has a missing dependency: 'fetchTwitterProfile'. Either include it or remove the dependency array                                                                                                                                                                   react-hooks/exhaustive-deps
web:lint:   163:15  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   180:17  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/personas/_components/multi-select.tsx
web:lint:   82:11  warning  Elements with the ARIA role "combobox" must have the following attributes defined: aria-controls,aria-expanded  jsx-a11y/role-has-required-aria-props
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/ContentStudioHome.tsx
web:lint:   123:6  warning  React Hook useEffect has missing dependencies: 'initialCampaignId' and 'initialCampaigns'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/ContentTypeSelector.tsx
web:lint:   108:11  warning  Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images  jsx-a11y/alt-text
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/editor/BlogPostPreview.tsx
web:lint:   71:21  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/editor/LinkedInPreview.tsx
web:lint:    70:7   warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   186:17  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/editor/TwitterPreview.tsx
web:lint:    46:7   warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   183:17  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/editor/content-editor.tsx
web:lint:    90:34  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead                                                                                                react-hooks/exhaustive-deps
web:lint:   130:6   warning  React Hook useEffect has missing dependencies: 'blockContent', 'companyContentId', 'editor', 'setEditor', 'title', and 'zero.mutate.company_content'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint:   141:6   warning  React Hook useEffect has missing dependencies: 'setSelectedEditorImage', 'setSelectedImageId', 'setSelectedImageOption', and 'setSelectedType'. Either include them or remove the dependency array        react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/editor/general-content-editor.tsx
web:lint:    78:34  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead                                                              react-hooks/exhaustive-deps
web:lint:   113:6   warning  React Hook useEffect has missing dependencies: 'companyContent.content_editor_template', 'editor', and 'setEditor'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/image/EditImageDialog.tsx
web:lint:   36:6  warning  React Hook useEffect has a missing dependency: 'workspace.account.id'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/image/ImageGallery.tsx
web:lint:   120:13  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint:   175:15  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/image/ImageSubmenu.tsx
web:lint:   27:11  warning  Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images  jsx-a11y/alt-text
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/image/SelectedImageEditor.tsx
web:lint:   242:11  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/content-studio-workspace/components/text/TextContentEditor.tsx
web:lint:   105:6  warning  React Hook useEffect has a missing dependency: 'selectedCompanyContent'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/autosave/autosave-recovery-dialog.tsx
web:lint:   108:6  warning  React Hook React.useEffect has missing dependencies: 'dismiss', 'handleDiscard', 'handleRecover', 'relativeTime', 'timestamp', and 'toast'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/overlays/images/image-details.tsx
web:lint:   66:9  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/overlays/images/image-overlay-panel.tsx
web:lint:   165:21  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/overlays/sounds/sounds-panel.tsx
web:lint:   75:31  warning  The ref value 'audioRefs.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'audioRefs.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
web:lint:   80:6   warning  React Hook useEffect has an unnecessary dependency: 'localSounds'. Either exclude it or remove the dependency array. Outer scope values like 'localSounds' aren't valid dependencies because mutating them doesn't re-render the component                         react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/overlays/text/select-text-overlay.tsx
web:lint:   111:5  warning  React Hook useMemo has a missing dependency: 'handleAddOverlay'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/overlays/text/text-details.tsx
web:lint:   40:34  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/overlays/video/video-details.tsx
web:lint:   65:9  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/overlays/video/video-overlay-panel.tsx
web:lint:   170:21  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/selection/selected-outline.tsx
web:lint:   74:6  warning  React Hook useMemo has an unnecessary dependency: 'overlay.row'. Either exclude it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/timeline/timeline-item-label.tsx
web:lint:   40:16  warning  Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images  jsx-a11y/alt-text
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/timeline/timeline-item.tsx
web:lint:   208:13  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/components/timeline/timeline.tsx
web:lint:   250:6  warning  React Hook useEffect has a missing dependency: 'timelineRef'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/contexts/timeline-context.tsx
web:lint:   82:5  warning  React Hook useMemo has missing dependencies: 'addRow', 'removeRow', and 'setVisibleRows'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/db-video-editor.tsx
web:lint:   188:6  warning  React Hook useEffect has a missing dependency: 'saveToDatabase'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/hooks/use-keyframes.tsx
web:lint:   61:5  warning  React Hook React.useMemo has a missing dependency: 'overlay'. Either include it or remove the dependency array                                react-hooks/exhaustive-deps
web:lint:   63:7  warning  React Hook React.useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
web:lint:   64:7  warning  React Hook React.useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/hooks/use-timeline-drag-and-drop.tsx
web:lint:   185:5  warning  React Hook useCallback has missing dependencies: 'dragInfo' and 'timelineRef'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint:   257:5  warning  React Hook useCallback has missing dependencies: 'dragInfo' and 'timelineRef'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
web:lint:   342:6  warning  React Hook useCallback has a missing dependency: 'dragInfo'. Either include it or remove the dependency array                      react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/hooks/use-timeline-state.tsx
web:lint:   71:5  warning  React Hook useCallback has a missing dependency: 'timelineRef'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/editor/react-video-editor.tsx
web:lint:   152:6  warning  React Hook useEffect has a missing dependency: 'editorState'. Either include it or remove the dependency array       react-hooks/exhaustive-deps
web:lint:   235:6  warning  React Hook useEffect has a missing dependency: 'handleManualSave'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/components/shared/navbar.tsx
web:lint:   12:13  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/studio/components/video-editor/hooks/use-db-autosave.ts
web:lint:   223:6  warning  React Hook useEffect has a missing dependency: 'saveToDatabase'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/tasks/components/kanban-view.tsx
web:lint:   133:5  warning  React Hook useCallback has a missing dependency: 'zero.mutate.company_content'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: /Users/<USER>/sb-front-end/apps/web/app/home/<USER>/tasks/components/tasks-display-area.tsx
web:lint:   76:6  warning  React Hook useEffect has a missing dependency: 'user_cache'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
web:lint: 
web:lint: ✖ 90 problems (17 errors, 73 warnings)
web:lint: 
web:lint:  ELIFECYCLE  Command failed with exit code 1.

 Tasks:    31 successful, 32 total
Cached:    31 cached, 32 total
  Time:    2.411s 
Failed:    web#lint

