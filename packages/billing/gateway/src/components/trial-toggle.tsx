'use client';

import { Control, FieldPath, FieldValues } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormLabel } from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Switch } from '@kit/ui/switch';
import { Trans } from '@kit/ui/trans';

type TrialToggleProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>;
  name?: FieldPath<TFieldValues>;
  enabled: boolean;
  trialDays?: number;
  className?: string;
};

export function TrialToggle<TFieldValues extends FieldValues>(props: TrialToggleProps<TFieldValues>) {
  const { control, name, enabled, trialDays, className } = props;

  return (
    <If condition={enabled}>
      <FormField
        name={(name ?? ('startWithTrial' as FieldPath<TFieldValues>))}
        control={control}
        render={({ field }) => (
          <FormItem className={className ?? 'flex items-center gap-3'}>
            <FormControl>
              <Switch
                checked={Boolean(field.value)}
                onCheckedChange={(checked) => field.onChange(checked)}
                className={'data-[state=checked]:bg-green-500'}
              />
            </FormControl>

            <FormLabel className={'cursor-pointer'}>
              <Trans
                i18nKey={'billing:startWithTrialLabel'}
                defaults={`Start with ${trialDays ?? 0}-day free trial`}
              />
            </FormLabel>
          </FormItem>
        )}
      />
    </If>
  );
}


