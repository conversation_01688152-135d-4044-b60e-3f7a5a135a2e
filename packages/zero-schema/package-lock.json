{"name": "@kit/zero-schema", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@kit/zero-schema", "version": "0.0.0", "devDependencies": {"@rocicorp/zero": "0.21.**********", "@types/node": "^20.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@rocicorp/zero": "*"}}, "../../node_modules/.pnpm/@rocicorp+zero@0.21.**********_@opentelemetry+core@2.0.1_@opentelemetry+api@1.9.0__typescript@5.8.3/node_modules/@rocicorp/zero": {"version": "0.21.**********", "dev": true, "license": "Apache-2.0", "dependencies": {"@badrap/valita": "0.3.11", "@databases/escape-identifier": "^1.0.3", "@databases/sql": "^3.3.0", "@dotenvx/dotenvx": "^1.39.0", "@drdgvhbh/postgres-error-codes": "^0.0.6", "@fastify/cors": "^10.0.0", "@fastify/websocket": "^11.0.0", "@google-cloud/precise-date": "^4.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/auto-instrumentations-node": "^0.58.1", "@opentelemetry/exporter-logs-otlp-http": "^0.200.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-logs": "^0.200.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-node": "^2.0.0", "@postgresql-typed/oids": "^0.2.0", "@rocicorp/lock": "^1.0.4", "@rocicorp/logger": "^5.4.0", "@rocicorp/resolver": "^1.0.2", "@rocicorp/zero-sqlite3": "^1.0.7", "@types/basic-auth": "^1.1.8", "basic-auth": "^2.0.1", "chalk": "^5.3.0", "chalk-template": "^1.1.0", "chokidar": "^4.0.1", "command-line-args": "^6.0.1", "command-line-usage": "^7.0.3", "compare-utf8": "^0.1.1", "defu": "^6.1.4", "eventemitter3": "^5.0.1", "fastify": "^5.0.0", "is-in-subnet": "^4.0.1", "jose": "^5.9.3", "js-xxhash": "^4.0.0", "json-custom-numbers": "^3.1.1", "kasi": "^1.1.0", "nanoid": "^5.1.2", "parse-prometheus-text-format": "^1.1.1", "pg-format": "npm:pg-format-fix@^1.0.5", "postgres": "^3.4.4", "prettier": "^3.5.3", "semver": "^7.5.4", "tsx": "^4.19.1", "typedoc": "^0.28.2", "typedoc-plugin-markdown": "^4.6.1", "url-pattern": "^1.0.3", "ws": "^8.18.1"}, "bin": {"analyze-query": "out/zero/src/analyze-query.js", "ast-to-zql": "out/zero/src/ast-to-zql.js", "transform-query": "out/zero/src/transform-query.js", "zero-build-schema": "out/zero/src/build-schema.js", "zero-cache": "out/zero/src/cli.js", "zero-cache-dev": "out/zero/src/zero-cache-dev.js", "zero-deploy-permissions": "out/zero/src/deploy-permissions.js"}, "devDependencies": {"@rocicorp/eslint-config": "^0.7.0", "@rocicorp/prettier-config": "^0.3.0", "@vitest/runner": "3.0.8", "esbuild": "^0.25.0", "replicache": "15.2.1", "shared": "0.0.0", "typescript": "~5.8.2", "vitest": "3.0.8", "zero-cache": "0.0.0", "zero-client": "0.0.0", "zero-pg": "0.0.0", "zero-react": "0.0.0", "zero-server": "0.0.0", "zero-solid": "0.0.0", "zqlite": "0.0.0"}, "engines": {"node": ">=20"}}, "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "node_modules/@rocicorp/zero": {"resolved": "../../node_modules/.pnpm/@rocicorp+zero@0.21.**********_@opentelemetry+core@2.0.1_@opentelemetry+api@1.9.0__typescript@5.8.3/node_modules/@rocicorp/zero", "link": true}, "node_modules/@types/node": {"version": "20.19.11", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.19.11.tgz", "integrity": "sha512-uug3FEEGv0r+jrecvUUpbY8lLisvIjg6AAic6a2bSP5OEOLeJsDSnvhCDov7ipFFMXS3orMpzlmi0ZcuGkBbow==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/typescript": {"resolved": "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "link": true}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "dev": true, "license": "MIT"}}}