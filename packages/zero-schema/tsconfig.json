{"extends": "../../tooling/typescript/base.json", "compilerOptions": {"target": "es2020", "module": "ESNext", "moduleResolution": "bundler", "rootDir": "./src", "outDir": "./dist", "strict": false, "noImplicitAny": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "declaration": true, "noEmit": false, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}