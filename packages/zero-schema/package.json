{"name": "@kit/zero-schema", "version": "0.0.0", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "files": ["src", "dist"], "devDependencies": {"@rocicorp/zero": "0.21.2025062401", "@types/node": "^22.15.9", "typescript": "^5.8.3"}, "peerDependencies": {"@rocicorp/zero": "*"}}