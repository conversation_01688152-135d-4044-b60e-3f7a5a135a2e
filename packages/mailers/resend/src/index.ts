import 'server-only';

import { z } from 'zod';

import { <PERSON><PERSON>, MailerSchema, BatchEmailSchema } from '@kit/mailers-shared';

type Config = z.infer<typeof MailerSchema>;
type BatchConfig = z.infer<typeof BatchEmailSchema>;

const RESEND_API_KEY = z
  .string({
    description: 'The API key for the Resend API',
    required_error: 'Please provide the API key for the Resend API',
  })
  .parse(process.env.RESEND_API_KEY);

export function createResendMailer() {
  return new ResendMailer();
}

/**
 * A class representing a mailer using the Resend HTTP API.
 * @implements {Mailer}
 */
class ResendMailer implements Mailer {
  async sendEmail(config: Config) {
    const contentObject =
      'text' in config
        ? {
            text: config.text,
          }
        : {
            html: config.html,
          };

    const res = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify({
        from: config.from,
        to: [config.to],
        subject: config.subject,
        ...contentObject,
      }),
    });

    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(`Failed to send email: ${res.status} - ${res.statusText} - ${errorText}`);
    }
  }

  async sendBatchEmails(config: BatchConfig) {
    const batchEmails = config.emails.map((email) => {
      const contentObject =
        'text' in email
          ? {
              text: email.text,
            }
          : {
              html: email.html,
            };

      return {
        from: email.from,
        to: [email.to],
        subject: email.subject,
        ...contentObject,
      };
    });

    const res = await fetch('https://api.resend.com/emails/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify(batchEmails),
    });

    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(`Failed to send batch emails: ${res.status} - ${res.statusText} - ${errorText}`);
    }

    return res.json();
  }
}
