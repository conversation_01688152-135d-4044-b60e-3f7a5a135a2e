name: Deploy Supabase Migrations

on:
  push:
    paths:
      - 'apps/web/supabase/**'
    branches:
      - main          # Deploy to production
      - staging       # Deploy to staging  
      - preview       # Deploy to preview
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'preview'
        type: choice
        options:
        - preview
        - staging
        - production

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Cache Supabase CLI
      uses: actions/cache@v4
      with:
        path: ~/.cache/supabase
        key: ${{ runner.os }}-supabase-cli-${{ hashFiles('apps/web/supabase/config.toml') }}
        restore-keys: |
          ${{ runner.os }}-supabase-cli-

    - name: Install Supabase CLI
      uses: supabase/setup-cli@v1
      with:
        version: latest

    - name: Set environment variables
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          ENVIRONMENT="${{ github.event.inputs.environment }}"
        elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
          ENVIRONMENT="production"
        elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
          ENVIRONMENT="staging"
        elif [ "${{ github.ref }}" = "refs/heads/preview" ]; then
          ENVIRONMENT="preview"
        else
          echo "❌ Unsupported branch: ${{ github.ref_name }}"
          echo "Only main, staging, and preview branches are supported for automatic deployment"
          exit 1
        fi
        
        echo "Deploying to: $ENVIRONMENT"
        echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
        
        # Set database URL based on environment
        if [ "$ENVIRONMENT" = "production" ]; then
          echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_PRODUCTION }}@db.${{ secrets.SUPABASE_PROJECT_ID_PRODUCTION }}.supabase.co:5432/postgres" >> $GITHUB_ENV
        elif [ "$ENVIRONMENT" = "staging" ]; then
          echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_STAGING }}@db.${{ secrets.SUPABASE_PROJECT_ID_STAGING }}.supabase.co:5432/postgres" >> $GITHUB_ENV
        else
          echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_PREVIEW }}@db.qbdtpzsbkacowlbdoolk.supabase.co:5432/postgres" >> $GITHUB_ENV
        fi

    - name: Authenticate with Supabase
      run: |
        echo "🔑 Authenticating with Supabase..."
        supabase login --token ${{ secrets.SUPABASE_ACCESS_TOKEN }}

    - name: Navigate to web directory and push migrations
      run: |
        echo "📁 Navigating to apps/web directory..."
        cd apps/web
        
        echo "📋 Checking migration files..."
        ls -la supabase/migrations/
        
        echo "🚀 Pushing migrations to Supabase (${{ env.ENVIRONMENT }})..."
        supabase db push --db-url ${{ env.SUPABASE_DB_URL }} --include-all

    - name: Add deployment summary
      run: |
        echo "## 🗄️ Supabase Migrations Deployed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        if [ "${{ env.ENVIRONMENT }}" = "production" ]; then
          echo "**Supabase Project:** Production" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ env.ENVIRONMENT }}" = "staging" ]; then
          echo "**Supabase Project:** Staging" >> $GITHUB_STEP_SUMMARY
        else
          echo "**Supabase Project:** Preview (qbdtpzsbkacowlbdoolk)" >> $GITHUB_STEP_SUMMARY
          echo "**Supabase URL:** https://qbdtpzsbkacowlbdoolk.supabase.co" >> $GITHUB_STEP_SUMMARY
        fi