name: Deploy SB Server

on:
  push:
    paths:
      - 'apps/sb-server/**'
      - 'packages/zero-schema/**'
    branches:
      - main          # Deploy to production
      - staging       # Deploy to staging
      - preview       # Deploy to preview
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'preview'
        type: choice
        options:
        - preview
        - staging
        - production

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9.12.0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'pnpm'
        cache-dependency-path: 'pnpm-lock.yaml'

    - name: Cache pnpm store
      uses: actions/cache@v4
      with:
        path: ~/.pnpm-store
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Cache TypeScript build
      uses: actions/cache@v4
      with:
        path: |
          apps/sb-server/dist
          apps/sb-server/tsconfig.tsbuildinfo
          packages/zero-schema/dist
        key: ${{ runner.os }}-ts-build-${{ hashFiles('apps/sb-server/src/**/*', 'packages/zero-schema/src/**/*', 'apps/sb-server/tsconfig.json', 'packages/zero-schema/tsconfig.json') }}
        restore-keys: |
          ${{ runner.os }}-ts-build-

    - name: Install dependencies
      run: pnpm install --no-frozen-lockfile

    - name: Build sb-server
      run: |
        echo "📋 Building sb-server (includes zero-schema setup)..."
        cd apps/sb-server
        
        # Check if build cache was hit by looking for dist/index.js
        if [ ! -f "dist/index.js" ]; then
          echo "🔨 No cached build found, building from scratch..."
          pnpm run build
        else
          echo "✅ Using cached build output"
          # Still run setup to ensure zero-schema files are in place
          pnpm run setup-zero-schema
        fi
        
        echo "📋 Verifying build output..."
        ls -la dist/
        echo "📋 Checking index.js exists..."
        if [ -f "dist/index.js" ]; then
          echo "✅ dist/index.js found"
          head -n 5 dist/index.js
        else
          echo "❌ dist/index.js not found!"
          exit 1
        fi
        echo "📋 Verifying zero-schema setup..."
        ls -la dist/lib/zero-schema/

    - id: 'auth'
      name: 'Authenticate to Google Cloud'
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: '${{ secrets.GOOGLE_CLOUD_CREDENTIALS }}'

    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v2'
      with:
        version: 'latest'

    - name: Determine environment and deploy
      id: deploy
      run: |
        cd apps/sb-server
        
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          ENVIRONMENT="${{ github.event.inputs.environment }}"
        elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
          ENVIRONMENT="production"
        elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
          ENVIRONMENT="staging"
        elif [ "${{ github.ref }}" = "refs/heads/preview" ]; then
          ENVIRONMENT="preview"
        else
          echo "❌ Unsupported branch: ${{ github.ref_name }}"
          echo "Only main, staging, and preview branches are supported for automatic deployment"
          exit 1
        fi
        
        echo "Deploying to: $ENVIRONMENT"
        echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
        
        # Set environment variables based on target environment
        if [ "$ENVIRONMENT" = "preview" ]; then
          # Create .env.preview file for preview deployments
          cat > .env.preview << EOF
        SUPABASE_SERVICE_ROLE_KEY="${{ secrets.SUPABASE_SERVICE_ROLE_KEY_PREVIEW }}"
        OPENAI_API_KEY="${{ secrets.OPENAI_API_KEY }}"
        ZENROWS_API_KEY="${{ secrets.ZENROWS_API_KEY }}"
        PERPLEXITY_API_KEY="${{ secrets.PERPLEXITY_API_KEY }}"
        ZERO_UPSTREAM_DB="${{ secrets.ZERO_UPSTREAM_DB_PREVIEW }}"
        CLAUDE_API_KEY="${{ secrets.CLAUDE_API_KEY }}"
        FIRECRAWL_API_KEY="${{ secrets.FIRECRAWL_API_KEY }}"
        LANGFUSE_SECRET_KEY="${{ secrets.LANGFUSE_SECRET_KEY }}"
        LANGFUSE_PUBLIC_KEY="${{ secrets.LANGFUSE_PUBLIC_KEY }}"
        OPENROUTER_API_KEY="${{ secrets.OPENROUTER_API_KEY }}"
        AYRSHARE_PROFILE_KEY="${{ secrets.AYRSHARE_PROFILE_KEY }}"
        AYRSHARE_API_KEY="${{ secrets.AYRSHARE_API_KEY }}"
        JIGSAW_API_KEY="${{ secrets.JIGSAW_API_KEY }}"
        EOF
          
          # Deploy to preview
          ./deploy-preview-nodejs.sh
          
        elif [ "$ENVIRONMENT" = "staging" ]; then
          # Create .env.staging file for staging deployments
          cat > .env.staging << EOF
        SUPABASE_SERVICE_ROLE_KEY="${{ secrets.SUPABASE_SERVICE_ROLE_KEY_STAGING }}"
        OPENAI_API_KEY="${{ secrets.OPENAI_API_KEY }}"
        ZENROWS_API_KEY="${{ secrets.ZENROWS_API_KEY }}"
        PERPLEXITY_API_KEY="${{ secrets.PERPLEXITY_API_KEY }}"
        ZERO_UPSTREAM_DB="${{ secrets.ZERO_UPSTREAM_DB_STAGING }}"
        CLAUDE_API_KEY="${{ secrets.CLAUDE_API_KEY }}"
        FIRECRAWL_API_KEY="${{ secrets.FIRECRAWL_API_KEY }}"
        LANGFUSE_SECRET_KEY="${{ secrets.LANGFUSE_SECRET_KEY }}"
        LANGFUSE_PUBLIC_KEY="${{ secrets.LANGFUSE_PUBLIC_KEY }}"
        OPENROUTER_API_KEY="${{ secrets.OPENROUTER_API_KEY }}"
        AYRSHARE_PROFILE_KEY="${{ secrets.AYRSHARE_PROFILE_KEY }}"
        AYRSHARE_API_KEY="${{ secrets.AYRSHARE_API_KEY }}"
        JIGSAW_API_KEY="${{ secrets.JIGSAW_API_KEY }}"
        EOF
          
          # Deploy to staging
          ./deploy-staging-nodejs.sh
          
        elif [ "$ENVIRONMENT" = "production" ]; then
          echo "Production deployment not implemented yet"
          exit 1
        fi

    - name: Comment on PR (if applicable)
      if: github.event_name == 'pull_request' && steps.deploy.outputs.environment == 'preview'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 **SB Server Preview Deployed!**\n\n📍 **URL:** https://preview-dot-psychic-valve-439013-d2.lm.r.appspot.com\n\n✅ Your changes are now live in the preview environment.'
          })

    - name: Add deployment summary
      run: |
        echo "## 🚀 SB Server Deployment Complete" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ steps.deploy.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        if [ "${{ steps.deploy.outputs.environment }}" = "preview" ]; then
          echo "**Preview URL:** https://preview-dot-psychic-valve-439013-d2.lm.r.appspot.com" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ steps.deploy.outputs.environment }}" = "staging" ]; then
          echo "**Staging URL:** https://staging-dot-psychic-valve-439013-d2.lm.r.appspot.com" >> $GITHUB_STEP_SUMMARY
        fi